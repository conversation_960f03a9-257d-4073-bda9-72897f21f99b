<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    
    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- 相机和存储权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    
    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <!-- 电话权限（用于拨打客服电话） -->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    
    <!-- 唤醒锁权限（用于推送通知） -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    
    <!-- 相机功能声明 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    
    <!-- 位置功能声明 -->
    <uses-feature
        android:name="android.hardware.location"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.location.gps"
        android:required="false" />

    <application
        android:name=".LaundryApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.LaundryUser"
        android:usesCleartextTraffic="false"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">

        <!-- 主Activity -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 启动页Activity -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.LaundryUser.Splash"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- 登录Activity -->
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- 注册Activity -->
        <activity
            android:name=".ui.auth.RegisterActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- 搜索Activity -->
        <activity
            android:name=".ui.search.SearchActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- 服务详情Activity -->
        <activity
            android:name=".ui.service.ServiceDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- 商家详情Activity -->
        <activity
            android:name=".ui.merchant.MerchantDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- 订单详情Activity -->
        <activity
            android:name=".ui.order.OrderDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- 支付Activity -->
        <activity
            android:name=".ui.payment.PaymentActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- 地图Activity -->
        <activity
            android:name=".ui.map.MapActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- 设置Activity -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- 关于Activity -->
        <activity
            android:name=".ui.about.AboutActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser.NoActionBar"
            android:screenOrientation="portrait" />

        <!-- Firebase消息服务 -->
        <service
            android:name=".service.LaundryFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- 文件提供者 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Google Maps API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_key" />

        <!-- Firebase配置 -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorPrimary" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />

        <!-- 支付宝SDK配置 -->
        <activity
            android:name="com.alipay.sdk.app.H5PayActivity"
            android:configChanges="orientation|keyboardHidden|navigation|screenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.alipay.sdk.app.H5AuthActivity"
            android:configChanges="orientation|keyboardHidden|navigation"
            android:exported="false"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustResize|stateHidden" />

        <!-- 微信支付SDK配置 -->
        <activity-alias
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:targetActivity=".ui.payment.WXPayEntryActivity" />

    </application>

</manifest>
