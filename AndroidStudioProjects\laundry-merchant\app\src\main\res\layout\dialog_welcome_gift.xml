<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_overlay"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="24dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/layoutContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_gift_background"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 关闭按钮 -->
            <ImageView
                android:id="@+id/buttonClose"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="end"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:src="@drawable/ic_close"
                app:tint="@android:color/white" />

            <!-- 礼品盒图标 -->
            <ImageView
                android:id="@+id/imageViewGiftBox"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:src="@drawable/ic_gift_box" />

            <!-- 标题 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:text="@string/welcome_gift_title"
                android:textColor="@android:color/white"
                android:textSize="20sp"
                android:textStyle="bold" />

            <!-- 描述 -->
            <TextView
                android:id="@+id/textViewDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="@string/welcome_gift_message"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:alpha="0.9" />

            <!-- 积分奖励 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:background="@drawable/rounded_white_background"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_points"
                    app:tint="@color/orange_500" />

                <TextView
                    android:id="@+id/textViewPoints"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+100积分"
                    android:textColor="@color/orange_500"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- 优惠券列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewCoupons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:nestedScrollingEnabled="false" />

            <!-- 领取状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:orientation="horizontal">

                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="8dp"
                    android:indeterminateTint="@android:color/white"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/textViewClaimStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 领取按钮 -->
            <Button
                android:id="@+id/buttonClaim"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/button_white_background"
                android:text="@string/claim_gift"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
