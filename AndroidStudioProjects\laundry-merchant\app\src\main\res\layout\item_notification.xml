<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左侧图标和指示器 -->
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp">

            <ImageView
                android:id="@+id/imageViewIcon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/circle_background"
                android:padding="8dp"
                android:src="@drawable/ic_notifications"
                app:tint="@color/colorPrimary" />

            <!-- 未读指示器 -->
            <View
                android:id="@+id/viewUnreadIndicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:layout_alignEnd="@id/imageViewIcon"
                android:layout_alignTop="@id/imageViewIcon"
                android:background="@drawable/circle_red"
                android:visibility="gone" />

            <!-- 优先级指示器 -->
            <View
                android:id="@+id/viewPriorityIndicator"
                android:layout_width="3dp"
                android:layout_height="match_parent"
                android:layout_alignStart="@id/imageViewIcon"
                android:layout_marginStart="-6dp"
                android:background="@color/red_500"
                android:visibility="gone" />

        </RelativeLayout>

        <!-- 中间内容区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 标题和时间 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewTitle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="通知标题"
                    android:textColor="@color/gray_800"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="10:30"
                    android:textColor="@color/gray_500"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 内容 -->
            <TextView
                android:id="@+id/textViewContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="通知内容详情"
                android:textColor="@color/gray_600"
                android:textSize="14sp" />

            <!-- 操作按钮 -->
            <Button
                android:id="@+id/buttonAction"
                style="@style/Button.LaundryMerchant.Borderless"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginTop="8dp"
                android:text="查看详情"
                android:textSize="12sp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 右侧删除按钮 -->
        <ImageView
            android:id="@+id/imageViewDelete"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:src="@drawable/ic_delete"
            app:tint="@color/gray_400" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
