{"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "java.debug.settings.onBuildFailureProceed": true, "java.debug.settings.enableRunDebugCodeLens": true, "java.debug.settings.hotCodeReplace": "auto", "kotlin.languageServer.enabled": true, "kotlin.compiler.jvm.target": "1.8", "android.home": "${env:ANDROID_HOME}", "android.sdk.path": "${env:ANDROID_HOME}", "android.gradle.path": "./gradlew", "files.associations": {"*.kt": "kotlin", "*.kts": "kotlin", "*.gradle": "groovy", "*.gradle.kts": "kotlin"}, "files.exclude": {"**/build": true, "**/.gradle": true, "**/local.properties": true, "**/*.iml": true, "**/.idea": true, "**/captures": true, "**/.externalNativeBuild": true, "**/.cxx": true}, "search.exclude": {"**/build": true, "**/.gradle": true, "**/node_modules": true, "**/captures": true, "**/.externalNativeBuild": true, "**/.cxx": true}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true, "source.fixAll": true}, "editor.rulers": [100, 120], "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "[kotlin]": {"editor.defaultFormatter": "fwcd.kotlin", "editor.tabSize": 4, "editor.insertSpaces": true}, "[java]": {"editor.defaultFormatter": "redhat.java", "editor.tabSize": 4, "editor.insertSpaces": true}, "[xml]": {"editor.defaultFormatter": "redhat.vscode-xml", "editor.tabSize": 2, "editor.insertSpaces": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2, "editor.insertSpaces": true}, "[gradle]": {"editor.tabSize": 4, "editor.insertSpaces": true}, "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.defaultProfile.linux": "bash", "git.ignoreLimitWarning": true, "git.autofetch": true, "problems.decorations.enabled": true, "problems.showCurrentInStatus": true, "workbench.colorCustomizations": {"activityBar.background": "#4CAF50", "activityBar.foreground": "#FFFFFF", "statusBar.background": "#4CAF50", "statusBar.foreground": "#FFFFFF"}, "emmet.includeLanguages": {"xml": "html"}, "xml.validation.enabled": true, "xml.format.enabled": true, "xml.format.splitAttributes": true, "gradle.nestedProjects": true, "gradle.reuseTerminals": "all", "extensions.ignoreRecommendations": false, "todo-tree.general.tags": ["TODO", "FIXME", "HACK", "NOTE", "XXX", "BUG"], "todo-tree.highlights.defaultHighlight": {"icon": "alert", "type": "tag", "foreground": "#FFFFFF", "background": "#FF9800", "opacity": 50, "iconColour": "#FF9800"}, "todo-tree.highlights.customHighlight": {"TODO": {"icon": "check", "background": "#4CAF50"}, "FIXME": {"icon": "bug", "background": "#F44336"}, "HACK": {"icon": "tools", "background": "#FF9800"}, "NOTE": {"icon": "note", "background": "#2196F3"}}}