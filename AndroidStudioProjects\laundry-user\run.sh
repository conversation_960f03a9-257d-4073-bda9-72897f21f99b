#!/bin/bash

# 洗护帮用户端 - 快速启动脚本
# 使用方法: ./run.sh [command]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
APP_NAME="洗护帮用户端"
PACKAGE_NAME="com.laundry.user"
MAIN_ACTIVITY="com.laundry.user.ui.main.MainActivity"

# 函数：打印彩色消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}    $1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 函数：检查环境
check_environment() {
    print_header "检查开发环境"
    
    # 检查Java
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        print_message "Java版本: $JAVA_VERSION"
    else
        print_error "Java未安装，请先安装JDK 11或17"
        exit 1
    fi
    
    # 检查Android SDK
    if [ -z "$ANDROID_HOME" ]; then
        print_error "ANDROID_HOME环境变量未设置"
        exit 1
    else
        print_message "Android SDK路径: $ANDROID_HOME"
    fi
    
    # 检查ADB
    if command -v adb &> /dev/null; then
        print_message "ADB已安装"
    else
        print_error "ADB未找到，请检查Android SDK安装"
        exit 1
    fi
    
    # 检查Gradle
    if [ -f "./gradlew" ]; then
        print_message "Gradle Wrapper已就绪"
    else
        print_error "gradlew文件未找到"
        exit 1
    fi
}

# 函数：检查设备
check_devices() {
    print_header "检查连接的设备"
    
    DEVICES=$(adb devices | grep -v "List of devices" | grep -v "^$" | wc -l)
    
    if [ $DEVICES -eq 0 ]; then
        print_warning "没有检测到连接的设备"
        print_message "请连接Android设备或启动模拟器"
        return 1
    else
        print_message "检测到 $DEVICES 个设备:"
        adb devices
        return 0
    fi
}

# 函数：清理项目
clean_project() {
    print_header "清理项目"
    ./gradlew clean
    print_message "项目清理完成"
}

# 函数：构建项目
build_debug() {
    print_header "构建Debug版本"
    ./gradlew assembleDebug
    print_message "Debug版本构建完成"
}

build_release() {
    print_header "构建Release版本"
    ./gradlew assembleRelease
    print_message "Release版本构建完成"
}

# 函数：安装应用
install_debug() {
    print_header "安装Debug版本"
    
    if ! check_devices; then
        exit 1
    fi
    
    ./gradlew installDebug
    print_message "应用安装完成"
}

# 函数：启动应用
launch_app() {
    print_header "启动应用"
    
    if ! check_devices; then
        exit 1
    fi
    
    adb shell am start -n $MAIN_ACTIVITY
    print_message "应用已启动"
}

# 函数：卸载应用
uninstall_app() {
    print_header "卸载应用"
    
    if ! check_devices; then
        exit 1
    fi
    
    adb uninstall $PACKAGE_NAME
    print_message "应用已卸载"
}

# 函数：查看日志
view_logs() {
    print_header "查看应用日志"
    
    if ! check_devices; then
        exit 1
    fi
    
    print_message "按Ctrl+C停止日志查看"
    adb logcat -v time $PACKAGE_NAME:V *:S
}

# 函数：运行测试
run_tests() {
    print_header "运行单元测试"
    ./gradlew test
    print_message "单元测试完成"
}

run_instrumented_tests() {
    print_header "运行集成测试"
    
    if ! check_devices; then
        exit 1
    fi
    
    ./gradlew connectedAndroidTest
    print_message "集成测试完成"
}

# 函数：代码检查
lint_check() {
    print_header "代码检查"
    ./gradlew lint
    print_message "代码检查完成"
}

# 函数：启动模拟器
start_emulator() {
    print_header "启动Android模拟器"
    
    # 检查可用的AVD
    AVDS=$(emulator -list-avds)
    
    if [ -z "$AVDS" ]; then
        print_error "没有找到可用的AVD，请先创建模拟器"
        exit 1
    fi
    
    # 使用第一个可用的AVD
    FIRST_AVD=$(echo "$AVDS" | head -n 1)
    print_message "启动模拟器: $FIRST_AVD"
    
    emulator -avd "$FIRST_AVD" &
    print_message "模拟器启动中..."
}

# 函数：完整构建和运行
full_run() {
    print_header "完整构建和运行流程"
    
    check_environment
    clean_project
    build_debug
    install_debug
    launch_app
    
    print_message "应用已成功运行！"
}

# 函数：开发模式
dev_mode() {
    print_header "开发模式 - 快速迭代"
    
    build_debug
    install_debug
    launch_app
    
    print_message "开发版本已更新并启动"
}

# 函数：显示帮助
show_help() {
    echo -e "${BLUE}$APP_NAME - 快速启动脚本${NC}"
    echo ""
    echo "使用方法: ./run.sh [command]"
    echo ""
    echo "可用命令:"
    echo "  check       - 检查开发环境"
    echo "  devices     - 检查连接的设备"
    echo "  clean       - 清理项目"
    echo "  build       - 构建Debug版本"
    echo "  release     - 构建Release版本"
    echo "  install     - 安装Debug版本"
    echo "  launch      - 启动应用"
    echo "  uninstall   - 卸载应用"
    echo "  logs        - 查看应用日志"
    echo "  test        - 运行单元测试"
    echo "  uitest      - 运行集成测试"
    echo "  lint        - 代码检查"
    echo "  emulator    - 启动模拟器"
    echo "  run         - 完整构建和运行"
    echo "  dev         - 开发模式（快速迭代）"
    echo "  help        - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./run.sh run        # 完整构建和运行"
    echo "  ./run.sh dev        # 开发模式"
    echo "  ./run.sh logs       # 查看日志"
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_environment
            ;;
        "devices")
            check_devices
            ;;
        "clean")
            clean_project
            ;;
        "build")
            build_debug
            ;;
        "release")
            build_release
            ;;
        "install")
            install_debug
            ;;
        "launch")
            launch_app
            ;;
        "uninstall")
            uninstall_app
            ;;
        "logs")
            view_logs
            ;;
        "test")
            run_tests
            ;;
        "uitest")
            run_instrumented_tests
            ;;
        "lint")
            lint_check
            ;;
        "emulator")
            start_emulator
            ;;
        "run")
            full_run
            ;;
        "dev")
            dev_mode
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
