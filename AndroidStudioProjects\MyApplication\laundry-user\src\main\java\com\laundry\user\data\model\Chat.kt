package com.laundry.user.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 消息类型枚举
 */
enum class MessageType(val value: Int) {
    TEXT(0),
    IMAGE(1),
    VOICE(2),
    SYSTEM(3),
    ORDER_UPDATE(4),
    LOCATION(5)
}

/**
 * 聊天会话
 */
@Entity(tableName = "chat_sessions")
data class ChatSession(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("user_id")
    val userId: String = "",
    
    @SerializedName("merchant_id")
    val merchantId: String = "",
    
    @SerializedName("merchant_name")
    val merchantName: String = "",
    
    @SerializedName("merchant_avatar")
    val merchantAvatar: String = "",
    
    @SerializedName("order_id")
    val orderId: String = "", // 关联的订单ID
    
    @SerializedName("last_message")
    val lastMessage: String = "",
    
    @SerializedName("last_message_time")
    val lastMessageTime: String = "",
    
    @SerializedName("unread_count")
    val unreadCount: Int = 0,
    
    @SerializedName("is_active")
    val isActive: Boolean = true,
    
    @SerializedName("created_at")
    val createdAt: String = "",
    
    @SerializedName("updated_at")
    val updatedAt: String = ""
)

/**
 * 聊天消息
 */
@Entity(tableName = "chat_messages")
data class ChatMessage(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("session_id")
    val sessionId: String = "",
    
    @SerializedName("sender_id")
    val senderId: String = "",
    
    @SerializedName("sender_type")
    val senderType: String = "", // user, merchant, system
    
    @SerializedName("sender_name")
    val senderName: String = "",
    
    @SerializedName("sender_avatar")
    val senderAvatar: String = "",
    
    @SerializedName("message_type")
    val messageType: Int = MessageType.TEXT.value,
    
    @SerializedName("content")
    val content: String = "",
    
    @SerializedName("media_url")
    val mediaUrl: String = "", // 图片、语音文件URL
    
    @SerializedName("media_duration")
    val mediaDuration: Int = 0, // 语音时长（秒）
    
    @SerializedName("location_latitude")
    val locationLatitude: Double = 0.0,
    
    @SerializedName("location_longitude")
    val locationLongitude: Double = 0.0,
    
    @SerializedName("location_address")
    val locationAddress: String = "",
    
    @SerializedName("is_read")
    val isRead: Boolean = false,
    
    @SerializedName("created_at")
    val createdAt: String = "",
    
    @SerializedName("local_id")
    val localId: String = "", // 本地消息ID，用于发送状态跟踪
    
    @SerializedName("send_status")
    val sendStatus: Int = 0 // 0: 发送中, 1: 发送成功, 2: 发送失败
)

/**
 * 通知消息
 */
@Entity(tableName = "notifications")
data class Notification(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("user_id")
    val userId: String = "",
    
    @SerializedName("title")
    val title: String = "",
    
    @SerializedName("content")
    val content: String = "",
    
    @SerializedName("type")
    val type: String = "", // order_update, promotion, system, chat
    
    @SerializedName("data")
    val data: String = "", // JSON格式的额外数据
    
    @SerializedName("image_url")
    val imageUrl: String = "",
    
    @SerializedName("action_url")
    val actionUrl: String = "", // 点击跳转的URL
    
    @SerializedName("is_read")
    val isRead: Boolean = false,
    
    @SerializedName("is_important")
    val isImportant: Boolean = false,
    
    @SerializedName("created_at")
    val createdAt: String = ""
)

/**
 * 收藏夹
 */
@Entity(tableName = "favorites")
data class Favorite(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("user_id")
    val userId: String = "",
    
    @SerializedName("merchant_id")
    val merchantId: String = "",
    
    @SerializedName("merchant_name")
    val merchantName: String = "",
    
    @SerializedName("merchant_avatar")
    val merchantAvatar: String = "",
    
    @SerializedName("merchant_rating")
    val merchantRating: Float = 0f,
    
    @SerializedName("created_at")
    val createdAt: String = ""
)

/**
 * 地址信息
 */
@Entity(tableName = "addresses")
data class Address(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("user_id")
    val userId: String = "",
    
    @SerializedName("name")
    val name: String = "", // 地址标签：家、公司等
    
    @SerializedName("contact_name")
    val contactName: String = "",
    
    @SerializedName("contact_phone")
    val contactPhone: String = "",
    
    @SerializedName("province")
    val province: String = "",
    
    @SerializedName("city")
    val city: String = "",
    
    @SerializedName("district")
    val district: String = "",
    
    @SerializedName("detail_address")
    val detailAddress: String = "",
    
    @SerializedName("latitude")
    val latitude: Double = 0.0,
    
    @SerializedName("longitude")
    val longitude: Double = 0.0,
    
    @SerializedName("is_default")
    val isDefault: Boolean = false,
    
    @SerializedName("created_at")
    val createdAt: String = ""
)

/**
 * 搜索历史
 */
@Entity(tableName = "search_history")
data class SearchHistory(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("user_id")
    val userId: String = "",
    
    @SerializedName("keyword")
    val keyword: String = "",
    
    @SerializedName("search_count")
    val searchCount: Int = 1,
    
    @SerializedName("last_search_time")
    val lastSearchTime: String = ""
)
