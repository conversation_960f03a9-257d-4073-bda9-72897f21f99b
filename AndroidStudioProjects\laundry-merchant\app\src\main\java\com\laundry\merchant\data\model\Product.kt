package com.laundry.merchant.data.model

import java.util.Date

data class Product(
    val id: String,
    val name: String,
    val description: String,
    val categoryId: String,
    val categoryName: String,
    val originalPrice: Double,
    val currentPrice: Double,
    val discountPercentage: Int = 0,
    val imageUrls: List<String>,
    val thumbnailUrl: String,
    val stock: Int,
    val isHot: Boolean = false,
    val isNew: Boolean = false,
    val isRecommended: Boolean = false,
    val rating: Float = 0f,
    val reviewCount: Int = 0,
    val salesCount: Int = 0,
    val specifications: List<ProductSpecification>,
    val tags: List<String> = emptyList(),
    val createdAt: Date,
    val updatedAt: Date,
    val isAvailable: Boolean = true,
    val estimatedDuration: Int? = null, // 预计服务时长（分钟）
    val serviceType: ServiceType = ServiceType.PRODUCT
) {
    fun getDiscountAmount(): Double = originalPrice - currentPrice
    
    fun hasDiscount(): Boolean = discountPercentage > 0
    
    fun isLowStock(): Boolean = stock <= 3
    
    fun getStockStatus(): StockStatus {
        return when {
            stock <= 0 -> StockStatus.OUT_OF_STOCK
            stock <= 3 -> StockStatus.LOW_STOCK
            else -> StockStatus.IN_STOCK
        }
    }
}

data class ProductSpecification(
    val name: String,
    val value: String,
    val unit: String? = null
)

enum class ServiceType {
    PRODUCT,        // 实物商品
    HOME_SERVICE,   // 上门服务
    STORE_SERVICE,  // 到店服务
    URGENT_SERVICE  // 紧急服务
}

enum class StockStatus {
    IN_STOCK,
    LOW_STOCK,
    OUT_OF_STOCK
}

data class ProductCategory(
    val id: String,
    val name: String,
    val parentId: String? = null,
    val iconUrl: String? = null,
    val imageUrl: String? = null,
    val description: String? = null,
    val sortOrder: Int = 0,
    val isHot: Boolean = false,
    val productCount: Int = 0,
    val subCategories: List<ProductCategory> = emptyList()
)

data class HotSearchKeyword(
    val keyword: String,
    val searchCount: Int,
    val trend: SearchTrend = SearchTrend.STABLE
)

enum class SearchTrend {
    UP,      // 上升
    DOWN,    // 下降
    STABLE   // 稳定
}

data class SearchSuggestion(
    val text: String,
    val type: SuggestionType,
    val count: Int = 0
)

enum class SuggestionType {
    KEYWORD,    // 关键词
    PRODUCT,    // 商品
    CATEGORY,   // 分类
    BRAND       // 品牌
}

data class ProductFilter(
    val categoryIds: List<String> = emptyList(),
    val priceRange: PriceRange? = null,
    val serviceTypes: List<ServiceType> = emptyList(),
    val tags: List<String> = emptyList(),
    val sortBy: SortType = SortType.DEFAULT,
    val onlyAvailable: Boolean = true,
    val onlyInStock: Boolean = false,
    val onlyDiscount: Boolean = false
)

data class PriceRange(
    val min: Double,
    val max: Double
)

enum class SortType {
    DEFAULT,        // 默认排序
    PRICE_LOW,      // 价格从低到高
    PRICE_HIGH,     // 价格从高到低
    SALES,          // 销量排序
    RATING,         // 评分排序
    NEWEST,         // 最新上架
    DISCOUNT        // 折扣力度
}
