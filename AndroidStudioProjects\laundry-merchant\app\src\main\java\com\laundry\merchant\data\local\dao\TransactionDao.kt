package com.laundry.merchant.data.local.dao

import androidx.room.*
import com.laundry.merchant.data.local.entity.TransactionEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface TransactionDao {
    
    @Query("SELECT * FROM transactions ORDER BY createdAt DESC")
    fun getAllTransactions(): Flow<List<TransactionEntity>>
    
    @Query("SELECT * FROM transactions WHERE type = :type ORDER BY createdAt DESC")
    fun getTransactionsByType(type: String): Flow<List<TransactionEntity>>
    
    @Query("SELECT * FROM transactions WHERE category = :category ORDER BY createdAt DESC")
    fun getTransactionsByCategory(category: String): Flow<List<TransactionEntity>>
    
    @Query("SELECT * FROM transactions WHERE type = :type AND category = :category ORDER BY createdAt DESC")
    fun getTransactionsByTypeAndCategory(type: String, category: String): Flow<List<TransactionEntity>>
    
    @Query("SELECT * FROM transactions WHERE id = :transactionId")
    suspend fun getTransactionById(transactionId: String): TransactionEntity?
    
    @Query("SELECT * FROM transactions WHERE relatedOrderId = :orderId")
    suspend fun getTransactionsByOrderId(orderId: String): List<TransactionEntity>
    
    @Query("SELECT * FROM transactions WHERE isSynced = 0")
    suspend fun getUnsyncedTransactions(): List<TransactionEntity>
    
    @Query("SELECT SUM(amount) FROM transactions WHERE type = :type AND createdAt >= :startTime AND createdAt <= :endTime")
    suspend fun getTotalAmountByTypeAndPeriod(type: String, startTime: Long, endTime: Long): Double?
    
    @Query("SELECT COUNT(*) FROM transactions")
    suspend fun getTotalTransactionCount(): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTransaction(transaction: TransactionEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTransactions(transactions: List<TransactionEntity>)
    
    @Update
    suspend fun updateTransaction(transaction: TransactionEntity)
    
    @Query("UPDATE transactions SET status = :status, updatedAt = :updatedAt, isSynced = 0 WHERE id = :transactionId")
    suspend fun updateTransactionStatus(transactionId: String, status: String, updatedAt: Long)
    
    @Query("UPDATE transactions SET isSynced = 1 WHERE id = :transactionId")
    suspend fun markTransactionAsSynced(transactionId: String)
    
    @Delete
    suspend fun deleteTransaction(transaction: TransactionEntity)
    
    @Query("DELETE FROM transactions WHERE id = :transactionId")
    suspend fun deleteTransactionById(transactionId: String)
    
    @Query("DELETE FROM transactions")
    suspend fun deleteAllTransactions()
}
