# 📱 VSCode启动用户端应用指南

## 🚀 快速启动步骤

### 1. 安装必要扩展
在VSCode中安装以下扩展：
- **Extension Pack for Java** - Java开发支持
- **Kotlin Language** - Kotlin语言支持  
- **Android iOS Emulator** - 移动端开发支持
- **Gradle for Java** - Gradle构建支持

### 2. 配置环境变量
```bash
# Windows PowerShell
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools"

# 验证配置
java -version
adb version
```

### 3. 打开项目
```bash
# 在VSCode中打开项目
code laundry-user
```

### 4. 配置local.properties
在 `laundry-user/local.properties` 中设置：
```properties
sdk.dir=C\:\\Users\\YourName\\AppData\\Local\\Android\\Sdk
```

### 5. 启动方式

#### 方式A: 使用调试器
1. 按 `F5` 启动调试
2. 选择 `Launch Android App`
3. 等待构建完成

#### 方式B: 使用任务
1. `Ctrl+Shift+P` 打开命令面板
2. 输入 `Tasks: Run Task`
3. 选择 `android: build debug`
4. 选择 `android: install debug`

#### 方式C: 使用终端
```bash
# 在VSCode终端中执行
cd laundry-user
./gradlew assembleDebug
./gradlew installDebug
adb shell am start -n com.laundry.user/.ui.main.MainActivity
```

## 🔧 故障排除

### 问题1: Java未找到
```bash
# 安装OpenJDK 11
winget install Microsoft.OpenJDK.11
```

### 问题2: Android SDK未找到
```bash
# 下载Android Studio并安装SDK
# 或使用命令行工具
sdkmanager "platform-tools" "platforms;android-33"
```

### 问题3: 设备未连接
```bash
# 检查设备
adb devices

# 启动模拟器
emulator -avd Pixel_4_API_30
```

### 问题4: Gradle构建失败
```bash
# 清理项目
./gradlew clean

# 重新构建
./gradlew assembleDebug
```

## 📊 开发工具

### 查看日志
```bash
# 查看应用日志
adb logcat -v time com.laundry.user:V *:S
```

### 性能监控
```bash
# 内存使用
adb shell dumpsys meminfo com.laundry.user

# CPU使用
adb shell top -p $(adb shell pidof com.laundry.user)
```

### 调试功能
- ✅ 断点调试
- ✅ 变量监视
- ✅ 调用堆栈
- ✅ 实时日志

## 🎯 推荐工作流

1. **开发**: 使用VSCode编写代码
2. **调试**: 使用VSCode调试器
3. **测试**: 使用Android Studio运行测试
4. **发布**: 使用命令行构建Release版本

## 📱 设备要求

### Android设备
- Android 7.0 (API 24) 或更高版本
- 启用开发者选项和USB调试
- 至少2GB RAM

### 模拟器
- 推荐: Pixel 4 API 30
- RAM: 4GB
- 存储: 8GB

## 🔗 相关链接

- [Android开发文档](https://developer.android.com/)
- [Kotlin官方文档](https://kotlinlang.org/)
- [VSCode Android开发](https://code.visualstudio.com/docs/java/java-on-azure)
- [Gradle用户指南](https://docs.gradle.org/current/userguide/userguide.html)
