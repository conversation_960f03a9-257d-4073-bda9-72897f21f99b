package com.laundry.merchant.ui.favorite

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.laundry.merchant.databinding.ActivityFavoriteBinding
import com.laundry.merchant.ui.favorite.adapter.FavoriteAdapter
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class FavoriteActivity : AppCompatActivity() {

    private lateinit var binding: ActivityFavoriteBinding
    private val viewModel: FavoriteViewModel by viewModels()
    private lateinit var favoriteAdapter: FavoriteAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFavoriteBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerView()
        setupTabs()
        observeViewModel()
        
        // 加载数据
        viewModel.loadFavorites()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "我的收藏"

        // 设置刷新监听
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshFavorites()
        }

        // 设置清空按钮
        binding.buttonClearAll.setOnClickListener {
            showClearAllDialog()
        }
    }

    private fun setupRecyclerView() {
        favoriteAdapter = FavoriteAdapter(
            onItemClick = { favorite ->
                handleFavoriteClick(favorite)
            },
            onRemoveClick = { favorite ->
                viewModel.removeFavorite(favorite.id)
            },
            onShareClick = { favorite ->
                shareFavorite(favorite)
            }
        )

        binding.recyclerViewFavorites.apply {
            layoutManager = LinearLayoutManager(this@FavoriteActivity)
            adapter = favoriteAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("全部"))
            addTab(newTab().setText("订单"))
            addTab(newTab().setText("客户"))
            addTab(newTab().setText("服务"))
            addTab(newTab().setText("其他"))

            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    val type = when (tab?.position) {
                        0 -> null
                        1 -> "order"
                        2 -> "customer"
                        3 -> "service"
                        4 -> "other"
                        else -> null
                    }
                    viewModel.filterByType(type)
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: FavoriteUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新收藏列表
        favoriteAdapter.updateData(state.favorites)

        // 更新统计信息
        binding.textViewTotalCount.text = "共 ${state.favorites.size} 项收藏"

        // 更新按钮状态
        binding.buttonClearAll.isEnabled = state.favorites.isNotEmpty()

        // 更新空状态
        binding.emptyView.visibility = if (state.favorites.isEmpty() && !state.isLoading) {
            View.VISIBLE
        } else {
            View.GONE
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: FavoriteEvent) {
        when (event) {
            is FavoriteEvent.ShowError -> {
                showError(event.message)
            }
            is FavoriteEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is FavoriteEvent.NavigateToOrder -> {
                navigateToOrder(event.orderId)
            }
            is FavoriteEvent.NavigateToCustomer -> {
                navigateToCustomer(event.customerId)
            }
        }
    }

    private fun handleFavoriteClick(favorite: FavoriteData) {
        when (favorite.type) {
            "order" -> {
                favorite.relatedId?.let { orderId ->
                    navigateToOrder(orderId)
                }
            }
            "customer" -> {
                favorite.relatedId?.let { customerId ->
                    navigateToCustomer(customerId)
                }
            }
            "service" -> {
                // TODO: 导航到服务详情
            }
            else -> {
                // 其他类型的处理
            }
        }
    }

    private fun shareFavorite(favorite: FavoriteData) {
        val shareText = "${favorite.title}\n${favorite.description}"
        val shareIntent = android.content.Intent().apply {
            action = android.content.Intent.ACTION_SEND
            type = "text/plain"
            putExtra(android.content.Intent.EXTRA_TEXT, shareText)
        }
        startActivity(android.content.Intent.createChooser(shareIntent, "分享收藏"))
    }

    private fun showClearAllDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("清空收藏")
            .setMessage("确定要清空所有收藏吗？此操作不可撤销。")
            .setPositiveButton("确定") { _, _ ->
                viewModel.clearAllFavorites()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun navigateToOrder(orderId: String) {
        // TODO: 导航到订单详情页面
    }

    private fun navigateToCustomer(customerId: String) {
        // TODO: 导航到客户详情页面
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
