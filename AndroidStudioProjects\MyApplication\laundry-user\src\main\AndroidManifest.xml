<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!-- 相机权限（用于扫码支付） -->
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 振动权限（通知） -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 电话权限（用于客服功能） -->
    <uses-permission android:name="android.permission.CALL_PHONE" />

    <!-- 录音权限（用于语音消息） -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- WiFi权限 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <!-- 唤醒锁权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- 相机特性 -->
    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />

    <application
        android:name=".LaundryApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.LaundryUser"
        tools:targetApi="31">
        <activity
            android:name="com.laundry.user.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.LaundryUser.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 登录Activity -->
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser" />

        <!-- 搜索Activity -->
        <activity
            android:name=".ui.search.SearchActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser" />

        <!-- 商家列表Activity -->
        <activity
            android:name=".ui.merchant.MerchantListActivity"
            android:exported="false"
            android:theme="@style/Theme.LaundryUser" />

        <!-- Firebase消息服务 -->
        <service
            android:name=".notification.LaundryFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- 高德地图Key -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="your_amap_key_here" />

        <!-- Firebase配置 -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/primary_color" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="laundry_notifications" />
    </application>

</manifest>