package com.laundry.merchant.ui.settings

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.laundry.merchant.databinding.ActivitySettingsBinding
import com.laundry.merchant.ui.auth.LoginActivity
import com.laundry.merchant.ui.favorite.FavoriteActivity
import com.laundry.merchant.ui.notification.NotificationActivity
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class SettingsActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySettingsBinding
    private val viewModel: SettingsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        observeViewModel()
        
        // 加载设置数据
        viewModel.loadSettings()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "设置"

        // 账户设置
        binding.layoutProfile.setOnClickListener {
            startActivity(Intent(this, ProfileActivity::class.java))
        }

        binding.layoutSecurity.setOnClickListener {
            startActivity(Intent(this, SecurityActivity::class.java))
        }

        // 通知设置
        binding.switchNotification.setOnCheckedChangeListener { _, isChecked ->
            viewModel.setNotificationEnabled(isChecked)
        }

        binding.switchSound.setOnCheckedChangeListener { _, isChecked ->
            viewModel.setSoundEnabled(isChecked)
        }

        binding.switchVibration.setOnCheckedChangeListener { _, isChecked ->
            viewModel.setVibrationEnabled(isChecked)
        }

        // 业务设置
        binding.switchAutoAccept.setOnCheckedChangeListener { _, isChecked ->
            viewModel.setAutoAcceptOrders(isChecked)
        }

        binding.layoutWorkingHours.setOnClickListener {
            startActivity(Intent(this, WorkingHoursActivity::class.java))
        }

        binding.layoutServiceArea.setOnClickListener {
            startActivity(Intent(this, ServiceAreaActivity::class.java))
        }

        // 应用设置
        binding.layoutLanguage.setOnClickListener {
            showLanguageDialog()
        }

        binding.layoutTheme.setOnClickListener {
            showThemeDialog()
        }

        binding.layoutCache.setOnClickListener {
            showClearCacheDialog()
        }

        // 其他功能
        binding.layoutFavorites.setOnClickListener {
            startActivity(Intent(this, FavoriteActivity::class.java))
        }

        binding.layoutNotifications.setOnClickListener {
            startActivity(Intent(this, NotificationActivity::class.java))
        }

        binding.layoutFeedback.setOnClickListener {
            startActivity(Intent(this, FeedbackActivity::class.java))
        }

        binding.layoutAbout.setOnClickListener {
            startActivity(Intent(this, AboutActivity::class.java))
        }

        binding.layoutHelp.setOnClickListener {
            startActivity(Intent(this, HelpActivity::class.java))
        }

        // 退出登录
        binding.buttonLogout.setOnClickListener {
            showLogoutDialog()
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: SettingsUiState) {
        // 更新用户信息
        state.userProfile?.let { profile ->
            binding.textViewMerchantName.text = profile.name
            binding.textViewPhone.text = profile.phone
            // TODO: 加载头像
        }

        // 更新通知设置
        binding.switchNotification.isChecked = state.notificationEnabled
        binding.switchSound.isChecked = state.soundEnabled
        binding.switchVibration.isChecked = state.vibrationEnabled

        // 更新业务设置
        binding.switchAutoAccept.isChecked = state.autoAcceptOrders

        // 更新应用设置
        binding.textViewLanguage.text = when (state.language) {
            "zh" -> "中文"
            "en" -> "English"
            else -> "中文"
        }

        binding.textViewTheme.text = when (state.theme) {
            "light" -> "浅色模式"
            "dark" -> "深色模式"
            "system" -> "跟随系统"
            else -> "跟随系统"
        }

        // 更新缓存大小
        binding.textViewCacheSize.text = state.cacheSize

        // 更新版本信息
        binding.textViewVersion.text = "v${state.appVersion}"

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: SettingsEvent) {
        when (event) {
            is SettingsEvent.ShowError -> {
                showError(event.message)
            }
            is SettingsEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is SettingsEvent.LogoutSuccess -> {
                navigateToLogin()
            }
            is SettingsEvent.CacheClearSuccess -> {
                showToast("缓存清理成功")
            }
        }
    }

    private fun showLanguageDialog() {
        val languages = arrayOf("中文", "English")
        val languageCodes = arrayOf("zh", "en")
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("选择语言")
            .setItems(languages) { _, which ->
                viewModel.setLanguage(languageCodes[which])
            }
            .show()
    }

    private fun showThemeDialog() {
        val themes = arrayOf("浅色模式", "深色模式", "跟随系统")
        val themeCodes = arrayOf("light", "dark", "system")
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("选择主题")
            .setItems(themes) { _, which ->
                viewModel.setTheme(themeCodes[which])
            }
            .show()
    }

    private fun showClearCacheDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("清理缓存")
            .setMessage("确定要清理应用缓存吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.clearCache()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showLogoutDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("退出登录")
            .setMessage("确定要退出登录吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.logout()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
