<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.statistics.StatisticsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/title_statistics"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text"
            android:layout_marginBottom="24dp" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Today Statistics -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="今日数据"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_today_orders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="8"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/today_orders"
                            android:textSize="12sp"
                            android:textColor="@color/secondary_text" />

                        <ProgressBar
                            android:id="@+id/progress_today_orders"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="60dp"
                            android:layout_height="8dp"
                            android:layout_marginTop="4dp"
                            android:progress="80" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_today_revenue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥240.00"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/price_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/today_revenue"
                            android:textSize="12sp"
                            android:textColor="@color/secondary_text" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Month Statistics -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="本月数据"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_month_orders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="156"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/month_orders"
                            android:textSize="12sp"
                            android:textColor="@color/secondary_text" />

                        <ProgressBar
                            android:id="@+id/progress_month_orders"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="60dp"
                            android:layout_height="8dp"
                            android:layout_marginTop="4dp"
                            android:progress="75" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_month_revenue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥4680.00"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            android:textColor="@color/price_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/month_revenue"
                            android:textSize="12sp"
                            android:textColor="@color/secondary_text" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Total Statistics -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="总体数据"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_text"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_total_orders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1250"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/primary_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/total_orders"
                            android:textSize="12sp"
                            android:textColor="@color/secondary_text" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_total_revenue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥37500.00"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/price_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/total_revenue"
                            android:textSize="12sp"
                            android:textColor="@color/secondary_text" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/text_average_rating"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.6"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/accent_color" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/average_rating"
                            android:textSize="12sp"
                            android:textColor="@color/secondary_text" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
