package com.laundry.user.data.model

import com.google.gson.annotations.SerializedName
import java.util.*

// ==================== 用户相关 ====================

data class LoginResponse(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long,
    val user: UserProfile
)

data class RegisterResponse(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long,
    val user: UserProfile
)

data class TokenResponse(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long
)

data class UserProfile(
    val id: String,
    val phone: String,
    val nickname: String,
    val avatar: String?,
    val gender: String?, // male, female, unknown
    val birthday: String?,
    val memberLevel: String, // bronze, silver, gold, diamond
    val points: Int,
    val balance: Double,
    val isVerified: Boolean,
    val createdAt: String,
    val updatedAt: String
)

data class UserAddress(
    val id: String,
    val name: String,
    val phone: String,
    val province: String,
    val city: String,
    val district: String,
    val address: String,
    val latitude: Double,
    val longitude: Double,
    val isDefault: Boolean,
    val createdAt: String
)

// ==================== 服务相关 ====================

data class Service(
    val id: String,
    val name: String,
    val description: String,
    val category: ServiceCategory,
    val merchant: Merchant,
    val price: Double,
    val originalPrice: Double?,
    val unit: String,
    val images: List<String>,
    val rating: Double,
    val reviewCount: Int,
    val orderCount: Int,
    val isPromoted: Boolean,
    val promotedRank: Int?,
    val tags: List<String>,
    val features: List<String>,
    val distance: Double?,
    val estimatedTime: String?,
    val isAvailable: Boolean,
    val createdAt: String,
    val updatedAt: String
)

data class ServiceDetail(
    val id: String,
    val name: String,
    val description: String,
    val detailDescription: String,
    val category: ServiceCategory,
    val merchant: MerchantDetail,
    val price: Double,
    val originalPrice: Double?,
    val unit: String,
    val images: List<String>,
    val rating: Double,
    val reviewCount: Int,
    val orderCount: Int,
    val isPromoted: Boolean,
    val promotedRank: Int?,
    val tags: List<String>,
    val features: List<String>,
    val specifications: List<ServiceSpecification>,
    val distance: Double?,
    val estimatedTime: String?,
    val isAvailable: Boolean,
    val isFavorited: Boolean,
    val reviews: List<Review>,
    val relatedServices: List<Service>,
    val createdAt: String,
    val updatedAt: String
)

data class ServiceCategory(
    val id: String,
    val name: String,
    val icon: String,
    val description: String?,
    val parentId: String?,
    val sortOrder: Int,
    val isActive: Boolean
)

data class ServiceSpecification(
    val name: String,
    val value: String,
    val unit: String?
)

data class Merchant(
    val id: String,
    val name: String,
    val avatar: String?,
    val rating: Double,
    val reviewCount: Int,
    val orderCount: Int,
    val distance: Double?,
    val isVerified: Boolean,
    val businessHours: String?,
    val phone: String?,
    val address: String?
)

data class MerchantDetail(
    val id: String,
    val name: String,
    val avatar: String?,
    val description: String?,
    val rating: Double,
    val reviewCount: Int,
    val orderCount: Int,
    val distance: Double?,
    val isVerified: Boolean,
    val businessHours: String?,
    val phone: String?,
    val address: String?,
    val latitude: Double?,
    val longitude: Double?,
    val images: List<String>,
    val certificates: List<String>,
    val services: List<Service>,
    val createdAt: String
)

// ==================== 订单相关 ====================

data class Order(
    val id: String,
    val orderNo: String,
    val service: Service,
    val merchant: Merchant,
    val user: UserProfile,
    val address: UserAddress,
    val quantity: Int,
    val unitPrice: Double,
    val totalAmount: Double,
    val discountAmount: Double,
    val finalAmount: Double,
    val status: String, // pending, confirmed, picked_up, processing, quality_check, delivering, completed, cancelled
    val statusText: String,
    val note: String?,
    val appointmentTime: String?,
    val createdAt: String,
    val updatedAt: String,
    val timeline: List<OrderTimeline>
)

data class OrderDetail(
    val id: String,
    val orderNo: String,
    val service: ServiceDetail,
    val merchant: MerchantDetail,
    val user: UserProfile,
    val address: UserAddress,
    val quantity: Int,
    val unitPrice: Double,
    val totalAmount: Double,
    val discountAmount: Double,
    val finalAmount: Double,
    val status: String,
    val statusText: String,
    val note: String?,
    val appointmentTime: String?,
    val pickupTime: String?,
    val deliveryTime: String?,
    val completedTime: String?,
    val payment: Payment?,
    val review: Review?,
    val coupon: Coupon?,
    val timeline: List<OrderTimeline>,
    val createdAt: String,
    val updatedAt: String
)

data class OrderTimeline(
    val status: String,
    val statusText: String,
    val description: String?,
    val timestamp: String,
    val operator: String?
)

// ==================== 支付相关 ====================

data class Payment(
    val id: String,
    val orderId: String,
    val amount: Double,
    val method: String, // alipay, wechat, balance
    val status: String, // pending, success, failed, cancelled
    val transactionId: String?,
    val createdAt: String,
    val paidAt: String?
)

data class PaymentResponse(
    val paymentId: String,
    val paymentUrl: String?,
    val paymentData: Map<String, Any>?,
    val qrCode: String?
)

data class PaymentStatus(
    val paymentId: String,
    val status: String,
    val transactionId: String?,
    val paidAt: String?
)

// ==================== 评价相关 ====================

data class Review(
    val id: String,
    val user: UserProfile,
    val service: Service?,
    val merchant: Merchant?,
    val order: Order?,
    val rating: Int,
    val content: String,
    val images: List<String>,
    val tags: List<String>,
    val isAnonymous: Boolean,
    val merchantReply: String?,
    val merchantReplyTime: String?,
    val likeCount: Int,
    val isLiked: Boolean,
    val createdAt: String
)

// ==================== 收藏相关 ====================

data class Favorite(
    val id: String,
    val type: String, // service, merchant
    val targetId: String,
    val service: Service?,
    val merchant: Merchant?,
    val createdAt: String
)

// ==================== 积分和优惠券 ====================

data class PointsRecord(
    val id: String,
    val type: String, // earn, spend
    val amount: Int,
    val description: String,
    val orderId: String?,
    val createdAt: String
)

data class Coupon(
    val id: String,
    val name: String,
    val description: String,
    val type: String, // discount, cash, free_shipping
    val value: Double,
    val minAmount: Double?,
    val maxDiscount: Double?,
    val validFrom: String,
    val validTo: String,
    val status: String, // available, used, expired
    val usedAt: String?,
    val orderId: String?,
    val conditions: List<String>
)

data class CouponUsage(
    val couponId: String,
    val orderId: String,
    val discountAmount: Double,
    val usedAt: String
)

// ==================== 消息和通知 ====================

data class Message(
    val id: String,
    val conversationId: String,
    val senderId: String,
    val senderType: String, // user, merchant, system
    val content: String,
    val type: String, // text, image, file, system
    val extra: Map<String, Any>?,
    val isRead: Boolean,
    val createdAt: String
)

data class Notification(
    val id: String,
    val title: String,
    val content: String,
    val type: String, // order, payment, promotion, system
    val data: Map<String, Any>?,
    val isRead: Boolean,
    val createdAt: String
)

// ==================== 文件上传 ====================

data class UploadResponse(
    val url: String,
    val filename: String,
    val size: Long,
    val mimeType: String
)

// ==================== 搜索和筛选 ====================

data class SearchSuggestion(
    val keyword: String,
    val type: String, // keyword, service, merchant
    val count: Int?
)

data class FilterOption(
    val key: String,
    val name: String,
    val values: List<FilterValue>
)

data class FilterValue(
    val value: String,
    val name: String,
    val count: Int?
)

// ==================== 地理位置 ====================

data class Location(
    val latitude: Double,
    val longitude: Double,
    val address: String?,
    val city: String?,
    val district: String?
)

// ==================== 应用配置 ====================

data class AppConfig(
    val version: String,
    val forceUpdate: Boolean,
    val updateUrl: String?,
    val announcement: String?,
    val customerServicePhone: String,
    val customerServiceHours: String,
    val features: Map<String, Boolean>
)
