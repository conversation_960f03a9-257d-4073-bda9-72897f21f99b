package com.laundry.merchant.data.repository

import com.laundry.merchant.data.model.*
import com.laundry.merchant.network.ApiService
import com.laundry.merchant.network.NetworkResult
import kotlinx.coroutines.delay
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

interface ProductRepository {
    suspend fun getCategories(): NetworkResult<List<ProductCategory>>
    suspend fun getHotCategories(): NetworkResult<List<ProductCategory>>
    suspend fun getProducts(
        categoryId: String? = null,
        filter: ProductFilter = ProductFilter(),
        page: Int = 1,
        limit: Int = 20
    ): NetworkResult<List<Product>>
    suspend fun searchProducts(
        query: String,
        filter: ProductFilter = ProductFilter(),
        page: Int = 1,
        limit: Int = 20
    ): NetworkResult<List<Product>>
    suspend fun getProductById(productId: String): NetworkResult<Product>
    suspend fun getRecommendedProducts(limit: Int = 10): NetworkResult<List<Product>>
    suspend fun getHotSearchKeywords(): NetworkResult<List<HotSearchKeyword>>
    suspend fun getSearchSuggestions(query: String): NetworkResult<List<SearchSuggestion>>
    suspend fun saveSearchHistory(keyword: String)
    suspend fun getSearchHistory(): NetworkResult<List<String>>
    suspend fun clearSearchHistory()
}

@Singleton
class ProductRepositoryImpl @Inject constructor(
    private val apiService: ApiService
) : ProductRepository {

    override suspend fun getCategories(): NetworkResult<List<ProductCategory>> {
        return try {
            // TODO: 实际API调用
            // val response = apiService.getCategories()
            
            // 模拟数据
            delay(500)
            val categories = getMockCategories()
            NetworkResult.Success(categories)
        } catch (e: Exception) {
            NetworkResult.Error("获取分类失败: ${e.message}")
        }
    }

    override suspend fun getHotCategories(): NetworkResult<List<ProductCategory>> {
        return try {
            delay(300)
            val hotCategories = getMockCategories().filter { it.isHot }
            NetworkResult.Success(hotCategories)
        } catch (e: Exception) {
            NetworkResult.Error("获取热门分类失败: ${e.message}")
        }
    }

    override suspend fun getProducts(
        categoryId: String?,
        filter: ProductFilter,
        page: Int,
        limit: Int
    ): NetworkResult<List<Product>> {
        return try {
            delay(800)
            var products = getMockProducts()
            
            // 应用筛选条件
            if (categoryId != null) {
                products = products.filter { it.categoryId == categoryId }
            }
            
            if (filter.onlyAvailable) {
                products = products.filter { it.isAvailable }
            }
            
            if (filter.onlyInStock) {
                products = products.filter { it.stock > 0 }
            }
            
            if (filter.onlyDiscount) {
                products = products.filter { it.hasDiscount() }
            }
            
            if (filter.serviceTypes.isNotEmpty()) {
                products = products.filter { it.serviceType in filter.serviceTypes }
            }
            
            // 应用价格筛选
            filter.priceRange?.let { range ->
                products = products.filter { it.currentPrice >= range.min && it.currentPrice <= range.max }
            }
            
            // 应用排序
            products = when (filter.sortBy) {
                SortType.PRICE_LOW -> products.sortedBy { it.currentPrice }
                SortType.PRICE_HIGH -> products.sortedByDescending { it.currentPrice }
                SortType.SALES -> products.sortedByDescending { it.salesCount }
                SortType.RATING -> products.sortedByDescending { it.rating }
                SortType.NEWEST -> products.sortedByDescending { it.createdAt }
                SortType.DISCOUNT -> products.sortedByDescending { it.discountPercentage }
                else -> products
            }
            
            // 分页
            val startIndex = (page - 1) * limit
            val endIndex = minOf(startIndex + limit, products.size)
            val pagedProducts = if (startIndex < products.size) {
                products.subList(startIndex, endIndex)
            } else {
                emptyList()
            }
            
            NetworkResult.Success(pagedProducts)
        } catch (e: Exception) {
            NetworkResult.Error("获取商品失败: ${e.message}")
        }
    }

    override suspend fun searchProducts(
        query: String,
        filter: ProductFilter,
        page: Int,
        limit: Int
    ): NetworkResult<List<Product>> {
        return try {
            delay(600)
            
            // 保存搜索历史
            saveSearchHistory(query)
            
            var products = getMockProducts()
            
            // 搜索匹配
            products = products.filter { product ->
                product.name.contains(query, ignoreCase = true) ||
                product.description.contains(query, ignoreCase = true) ||
                product.tags.any { it.contains(query, ignoreCase = true) } ||
                product.categoryName.contains(query, ignoreCase = true)
            }
            
            // 应用其他筛选条件
            getProducts(null, filter, page, limit).let { result ->
                when (result) {
                    is NetworkResult.Success -> {
                        val filteredProducts = products.intersect(result.data.toSet()).toList()
                        NetworkResult.Success(filteredProducts)
                    }
                    is NetworkResult.Error -> result
                    is NetworkResult.Loading -> NetworkResult.Loading()
                }
            }
        } catch (e: Exception) {
            NetworkResult.Error("搜索失败: ${e.message}")
        }
    }

    override suspend fun getProductById(productId: String): NetworkResult<Product> {
        return try {
            delay(400)
            val product = getMockProducts().find { it.id == productId }
            if (product != null) {
                NetworkResult.Success(product)
            } else {
                NetworkResult.Error("商品不存在")
            }
        } catch (e: Exception) {
            NetworkResult.Error("获取商品详情失败: ${e.message}")
        }
    }

    override suspend fun getRecommendedProducts(limit: Int): NetworkResult<List<Product>> {
        return try {
            delay(400)
            val recommended = getMockProducts()
                .filter { it.isRecommended }
                .take(limit)
            NetworkResult.Success(recommended)
        } catch (e: Exception) {
            NetworkResult.Error("获取推荐商品失败: ${e.message}")
        }
    }

    override suspend fun getHotSearchKeywords(): NetworkResult<List<HotSearchKeyword>> {
        return try {
            delay(200)
            val keywords = listOf(
                HotSearchKeyword("洗衣服务", 1250, SearchTrend.UP),
                HotSearchKeyword("干洗", 980, SearchTrend.STABLE),
                HotSearchKeyword("床上用品清洗", 756, SearchTrend.UP),
                HotSearchKeyword("窗帘清洗", 543, SearchTrend.DOWN),
                HotSearchKeyword("皮具护理", 432, SearchTrend.UP),
                HotSearchKeyword("鞋子清洗", 321, SearchTrend.STABLE)
            )
            NetworkResult.Success(keywords)
        } catch (e: Exception) {
            NetworkResult.Error("获取热门搜索失败: ${e.message}")
        }
    }

    override suspend fun getSearchSuggestions(query: String): NetworkResult<List<SearchSuggestion>> {
        return try {
            delay(200)
            val suggestions = mutableListOf<SearchSuggestion>()
            
            // 添加关键词建议
            val keywords = listOf("洗衣", "干洗", "床上用品", "窗帘", "皮具", "鞋子")
            keywords.filter { it.contains(query, ignoreCase = true) }
                .forEach { suggestions.add(SearchSuggestion(it, SuggestionType.KEYWORD)) }
            
            // 添加商品建议
            getMockProducts()
                .filter { it.name.contains(query, ignoreCase = true) }
                .take(3)
                .forEach { suggestions.add(SearchSuggestion(it.name, SuggestionType.PRODUCT)) }
            
            // 添加分类建议
            getMockCategories()
                .filter { it.name.contains(query, ignoreCase = true) }
                .take(2)
                .forEach { suggestions.add(SearchSuggestion(it.name, SuggestionType.CATEGORY)) }
            
            NetworkResult.Success(suggestions.take(8))
        } catch (e: Exception) {
            NetworkResult.Error("获取搜索建议失败: ${e.message}")
        }
    }

    override suspend fun saveSearchHistory(keyword: String) {
        // TODO: 保存到本地数据库或SharedPreferences
        // 这里可以实现搜索历史的本地存储
    }

    override suspend fun getSearchHistory(): NetworkResult<List<String>> {
        return try {
            // TODO: 从本地存储获取搜索历史
            val history = listOf("洗衣服务", "干洗", "床上用品清洗", "窗帘清洗")
            NetworkResult.Success(history)
        } catch (e: Exception) {
            NetworkResult.Error("获取搜索历史失败: ${e.message}")
        }
    }

    override suspend fun clearSearchHistory() {
        // TODO: 清除本地搜索历史
    }

    // 模拟数据方法
    private fun getMockCategories(): List<ProductCategory> {
        return listOf(
            ProductCategory("cat_1", "洗衣服务", isHot = true, productCount = 25),
            ProductCategory("cat_2", "干洗服务", isHot = true, productCount = 18),
            ProductCategory("cat_3", "床上用品", isHot = true, productCount = 12),
            ProductCategory("cat_4", "窗帘清洗", productCount = 8),
            ProductCategory("cat_5", "皮具护理", isHot = true, productCount = 15),
            ProductCategory("cat_6", "鞋子清洗", productCount = 10),
            ProductCategory("cat_7", "地毯清洗", productCount = 6),
            ProductCategory("cat_8", "沙发清洗", productCount = 9)
        )
    }

    private fun getMockProducts(): List<Product> {
        val now = Date()
        return listOf(
            Product(
                id = "prod_1",
                name = "普通洗衣服务",
                description = "专业洗衣服务，适用于日常衣物清洗",
                categoryId = "cat_1",
                categoryName = "洗衣服务",
                originalPrice = 30.0,
                currentPrice = 25.0,
                discountPercentage = 17,
                imageUrls = listOf(),
                thumbnailUrl = "",
                stock = 100,
                isHot = true,
                rating = 4.8f,
                reviewCount = 156,
                salesCount = 1250,
                specifications = listOf(
                    ProductSpecification("服务时长", "24", "小时"),
                    ProductSpecification("取送范围", "5", "公里")
                ),
                tags = listOf("快速", "专业", "上门取送"),
                createdAt = now,
                updatedAt = now,
                serviceType = ServiceType.HOME_SERVICE
            ),
            Product(
                id = "prod_2",
                name = "高端干洗服务",
                description = "专业干洗设备，适用于高档衣物",
                categoryId = "cat_2",
                categoryName = "干洗服务",
                originalPrice = 80.0,
                currentPrice = 68.0,
                discountPercentage = 15,
                imageUrls = listOf(),
                thumbnailUrl = "",
                stock = 50,
                isRecommended = true,
                rating = 4.9f,
                reviewCount = 89,
                salesCount = 456,
                specifications = listOf(
                    ProductSpecification("服务时长", "48", "小时"),
                    ProductSpecification("适用材质", "丝绸、羊毛、皮革", "")
                ),
                tags = listOf("高端", "专业", "精细"),
                createdAt = now,
                updatedAt = now,
                serviceType = ServiceType.STORE_SERVICE
            ),
            Product(
                id = "prod_3",
                name = "床上用品清洗",
                description = "专业清洗床单、被套、枕套等床上用品",
                categoryId = "cat_3",
                categoryName = "床上用品",
                originalPrice = 45.0,
                currentPrice = 35.0,
                discountPercentage = 22,
                imageUrls = listOf(),
                thumbnailUrl = "",
                stock = 2,
                isNew = true,
                rating = 4.7f,
                reviewCount = 234,
                salesCount = 789,
                specifications = listOf(
                    ProductSpecification("服务时长", "36", "小时"),
                    ProductSpecification("除菌率", "99.9", "%")
                ),
                tags = listOf("除菌", "深度清洁", "上门取送"),
                createdAt = now,
                updatedAt = now,
                serviceType = ServiceType.HOME_SERVICE
            )
        )
    }
}
