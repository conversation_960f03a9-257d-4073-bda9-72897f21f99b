<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 产品图片区域 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="160dp">

            <ImageView
                android:id="@+id/imageViewProduct"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/placeholder_product" />

            <!-- 标签区域 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_alignParentTop="true"
                android:layout_margin="8dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewHotTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:background="@drawable/tag_hot_background"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    android:text="热门"
                    android:textColor="@android:color/white"
                    android:textSize="10sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/textViewNewTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/tag_new_background"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    android:text="新品"
                    android:textColor="@android:color/white"
                    android:textSize="10sp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 折扣标签 -->
            <TextView
                android:id="@+id/textViewDiscount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentTop="true"
                android:layout_margin="8dp"
                android:background="@drawable/tag_discount_background"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="20%OFF"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <!-- 库存状态 -->
            <TextView
                android:id="@+id/textViewStockStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_alignParentStart="true"
                android:layout_margin="8dp"
                android:background="@drawable/tag_stock_background"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="仅剩3件"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:visibility="gone" />

            <!-- 服务类型 -->
            <TextView
                android:id="@+id/textViewServiceType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_alignParentEnd="true"
                android:layout_margin="8dp"
                android:background="@drawable/tag_service_background"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:text="上门"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:visibility="gone" />

        </RelativeLayout>

        <!-- 产品信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- 产品名称 -->
            <TextView
                android:id="@+id/textViewProductName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="普通洗衣服务"
                android:textColor="@color/gray_800"
                android:textSize="14sp"
                android:textStyle="bold" />

            <!-- 价格区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewCurrentPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥25.00"
                    android:textColor="@color/red_500"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewOriginalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="¥30.00"
                    android:textColor="@color/gray_500"
                    android:textSize="12sp"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- 评分和销量 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <RatingBar
                    android:id="@+id/ratingBar"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:isIndicator="true"
                    android:numStars="5"
                    android:rating="4.8"
                    android:stepSize="0.1" />

                <TextView
                    android:id="@+id/textViewSalesCount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:text="已售1250"
                    android:textColor="@color/gray_500"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 加购物车按钮 -->
            <Button
                android:id="@+id/buttonAddToCart"
                style="@style/Button.LaundryMerchant.Small"
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:layout_marginTop="8dp"
                android:text="加购物车"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
