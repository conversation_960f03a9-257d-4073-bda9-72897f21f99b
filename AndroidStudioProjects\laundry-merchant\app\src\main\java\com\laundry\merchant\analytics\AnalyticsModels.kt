package com.laundry.merchant.analytics

// 用户行为事件
sealed class UserBehaviorEvent {
    data class Login(
        val userId: String,
        val method: String,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class Register(
        val userId: String,
        val method: String,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class ProductView(
        val userId: String,
        val productId: String,
        val productName: String,
        val category: String,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class AddToCart(
        val userId: String,
        val productId: String,
        val productName: String,
        val price: Double,
        val quantity: Int,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class Purchase(
        val userId: String,
        val orderId: String,
        val totalAmount: Double,
        val paymentMethod: String,
        val items: List<PurchaseItem>,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class Search(
        val userId: String,
        val query: String,
        val resultCount: Int,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class BookingCreated(
        val userId: String,
        val bookingId: String,
        val serviceType: String,
        val amount: Double,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class PageView(
        val userId: String,
        val pageName: String,
        val pageClass: String,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class ButtonClick(
        val userId: String,
        val buttonName: String,
        val pageName: String,
        val timestamp: Long
    ) : UserBehaviorEvent()
    
    data class Error(
        val userId: String,
        val errorType: String,
        val errorMessage: String,
        val pageName: String,
        val timestamp: Long
    ) : UserBehaviorEvent()
}

// 业务指标
sealed class BusinessMetric {
    data class Revenue(
        val amount: Double,
        val currency: String,
        val timestamp: Long
    ) : BusinessMetric()
    
    data class UserRetention(
        val userId: String,
        val daysSinceFirstUse: Int,
        val timestamp: Long
    ) : BusinessMetric()
    
    data class SessionDuration(
        val userId: String,
        val durationSeconds: Long,
        val timestamp: Long
    ) : BusinessMetric()
    
    data class ConversionFunnel(
        val userId: String,
        val step: String,
        val funnelName: String,
        val timestamp: Long
    ) : BusinessMetric()
}

// 购买项目
data class PurchaseItem(
    val productId: String,
    val productName: String,
    val category: String,
    val price: Double,
    val quantity: Int
)

// 用户画像数据
data class UserProfile(
    val userId: String,
    val age: Int? = null,
    val gender: String? = null,
    val city: String? = null,
    val registrationDate: Long,
    val lastActiveDate: Long,
    val totalOrders: Int = 0,
    val totalSpent: Double = 0.0,
    val averageOrderValue: Double = 0.0,
    val favoriteCategories: List<String> = emptyList(),
    val membershipLevel: String? = null,
    val lifetimeValue: Double = 0.0,
    val churnRisk: ChurnRisk = ChurnRisk.LOW
)

enum class ChurnRisk {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}

// 产品分析数据
data class ProductAnalytics(
    val productId: String,
    val productName: String,
    val category: String,
    val viewCount: Int = 0,
    val addToCartCount: Int = 0,
    val purchaseCount: Int = 0,
    val conversionRate: Double = 0.0,
    val revenue: Double = 0.0,
    val averageRating: Double = 0.0,
    val reviewCount: Int = 0,
    val returnRate: Double = 0.0,
    val profitMargin: Double = 0.0
)

// 业务统计数据
data class BusinessStatistics(
    val period: TimePeriod,
    val startDate: Long,
    val endDate: Long,
    val totalRevenue: Double = 0.0,
    val totalOrders: Int = 0,
    val totalUsers: Int = 0,
    val newUsers: Int = 0,
    val activeUsers: Int = 0,
    val averageOrderValue: Double = 0.0,
    val conversionRate: Double = 0.0,
    val retentionRate: Double = 0.0,
    val churnRate: Double = 0.0,
    val customerLifetimeValue: Double = 0.0,
    val topProducts: List<ProductAnalytics> = emptyList(),
    val topCategories: List<CategoryAnalytics> = emptyList(),
    val paymentMethodDistribution: Map<String, Double> = emptyMap(),
    val geographicDistribution: Map<String, Int> = emptyMap()
)

enum class TimePeriod {
    DAILY,
    WEEKLY,
    MONTHLY,
    QUARTERLY,
    YEARLY
}

// 分类分析数据
data class CategoryAnalytics(
    val categoryId: String,
    val categoryName: String,
    val revenue: Double = 0.0,
    val orderCount: Int = 0,
    val productCount: Int = 0,
    val averageOrderValue: Double = 0.0,
    val conversionRate: Double = 0.0,
    val growthRate: Double = 0.0
)

// 用户行为路径
data class UserJourney(
    val userId: String,
    val sessionId: String,
    val startTime: Long,
    val endTime: Long,
    val touchpoints: List<Touchpoint>,
    val conversionGoal: String? = null,
    val converted: Boolean = false,
    val conversionValue: Double = 0.0
)

data class Touchpoint(
    val timestamp: Long,
    val type: TouchpointType,
    val pageName: String,
    val action: String,
    val duration: Long = 0,
    val metadata: Map<String, String> = emptyMap()
)

enum class TouchpointType {
    PAGE_VIEW,
    BUTTON_CLICK,
    FORM_SUBMIT,
    SEARCH,
    PRODUCT_VIEW,
    ADD_TO_CART,
    CHECKOUT,
    PURCHASE,
    ERROR
}

// 实时分析数据
data class RealTimeAnalytics(
    val timestamp: Long,
    val activeUsers: Int = 0,
    val currentSessions: Int = 0,
    val realtimeRevenue: Double = 0.0,
    val realtimeOrders: Int = 0,
    val topPages: List<PageAnalytics> = emptyList(),
    val topProducts: List<ProductAnalytics> = emptyList(),
    val errorRate: Double = 0.0,
    val averageLoadTime: Double = 0.0
)

data class PageAnalytics(
    val pageName: String,
    val pageViews: Int = 0,
    val uniqueViews: Int = 0,
    val averageTimeOnPage: Double = 0.0,
    val bounceRate: Double = 0.0,
    val exitRate: Double = 0.0
)

// 漏斗分析
data class FunnelAnalysis(
    val funnelName: String,
    val steps: List<FunnelStep>,
    val totalUsers: Int,
    val conversionRate: Double,
    val dropOffPoints: List<DropOffPoint>
)

data class FunnelStep(
    val stepName: String,
    val stepOrder: Int,
    val userCount: Int,
    val conversionRate: Double,
    val averageTimeToNext: Double = 0.0
)

data class DropOffPoint(
    val fromStep: String,
    val toStep: String,
    val dropOffRate: Double,
    val dropOffCount: Int
)

// 队列分析
data class CohortAnalysis(
    val cohortType: CohortType,
    val cohorts: List<Cohort>
)

enum class CohortType {
    REGISTRATION,
    FIRST_PURCHASE,
    FIRST_LOGIN
}

data class Cohort(
    val cohortName: String,
    val cohortDate: Long,
    val initialSize: Int,
    val retentionRates: Map<Int, Double> // 天数 -> 留存率
)

// A/B测试结果
data class ABTestResult(
    val testName: String,
    val variants: List<ABTestVariant>,
    val startDate: Long,
    val endDate: Long,
    val status: ABTestStatus,
    val winningVariant: String? = null,
    val confidenceLevel: Double = 0.0,
    val statisticalSignificance: Boolean = false
)

data class ABTestVariant(
    val variantName: String,
    val userCount: Int,
    val conversionRate: Double,
    val averageValue: Double,
    val totalValue: Double
)

enum class ABTestStatus {
    DRAFT,
    RUNNING,
    COMPLETED,
    PAUSED,
    CANCELLED
}
