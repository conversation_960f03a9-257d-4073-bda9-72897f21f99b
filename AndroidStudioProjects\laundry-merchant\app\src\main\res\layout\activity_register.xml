<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 标题 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:text="商家注册"
                android:textColor="@color/gray_800"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- 手机号输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="手机号"
                app:startIconDrawable="@drawable/ic_phone">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone"
                    android:maxLength="11" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 验证码输入 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutVerificationCode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:layout_weight="1"
                    android:hint="验证码"
                    app:startIconDrawable="@drawable/ic_verified">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextVerificationCode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="6" />

                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/buttonSendCode"
                    style="@style/Button.LaundryMerchant.Outlined"
                    android:layout_width="wrap_content"
                    android:layout_height="56dp"
                    android:enabled="false"
                    android:text="发送验证码"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 商家名称输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutMerchantName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="商家名称"
                app:startIconDrawable="@drawable/ic_merchant_placeholder">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextMerchantName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLength="50" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 密码输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="设置密码"
                app:endIconMode="password_toggle"
                app:startIconDrawable="@drawable/ic_lock">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 确认密码输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutConfirmPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="确认密码"
                app:endIconMode="password_toggle"
                app:startIconDrawable="@drawable/ic_lock">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextConfirmPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 用户协议 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/checkBoxAgreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="我已阅读并同意"
                    android:textColor="@color/gray_600"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/textViewUserAgreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:text="《用户协议》"
                    android:textColor="@color/colorPrimary"
                    android:textSize="12sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="和"
                    android:textColor="@color/gray_600"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/textViewPrivacyPolicy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:text="《隐私政策》"
                    android:textColor="@color/colorPrimary"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 注册按钮 -->
            <Button
                android:id="@+id/buttonRegister"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:enabled="false"
                android:text="注册"
                android:textSize="16sp" />

            <!-- 登录提示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="已有账号？"
                    android:textColor="@color/gray_600"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/textViewLogin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="8dp"
                    android:text="立即登录"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- 加载进度 -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="16dp"
        android:visibility="gone" />

</LinearLayout>
