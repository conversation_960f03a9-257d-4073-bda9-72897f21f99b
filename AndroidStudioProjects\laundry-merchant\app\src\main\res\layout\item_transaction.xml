<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 类型图标 -->
        <TextView
            android:id="@+id/textViewTypeIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/circle_background"
            android:gravity="center"
            android:text="+"
            android:textColor="@color/green_500"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 交易信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewDescription"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="订单收入 - ORD001"
                    android:textColor="@color/gray_800"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10:30"
                    android:textColor="@color/gray_500"
                    android:textSize="12sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewCategory"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/background_category"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:text="订单"
                    android:textColor="@color/blue_500"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/textViewStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="已完成"
                    android:textColor="@color/green_500"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 金额 -->
        <TextView
            android:id="@+id/textViewAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:text="+¥25.00"
            android:textColor="@color/green_500"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
