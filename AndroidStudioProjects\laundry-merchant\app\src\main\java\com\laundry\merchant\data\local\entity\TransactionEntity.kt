package com.laundry.merchant.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "transactions")
data class TransactionEntity(
    @PrimaryKey
    val id: String,
    val type: String, // INCOME, EXPENSE
    val category: String, // order, promotion, withdraw, recharge
    val amount: Double,
    val description: String,
    val relatedOrderId: String?,
    val status: String, // PENDING, COMPLETED, FAILED, CANCELLED
    val createdAt: Date,
    val updatedAt: Date,
    val isSynced: Boolean = false
)
