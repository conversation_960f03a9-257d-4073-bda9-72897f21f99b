package com.laundry.user.network

object ApiConfig {
    // 生产环境API地址
    const val BASE_URL = "https://api.laundryhelp.com/v1/"
    
    // 开发环境API地址
    const val DEV_BASE_URL = "https://dev-api.laundryhelp.com/v1/"
    
    // 测试环境API地址
    const val TEST_BASE_URL = "https://test-api.laundryhelp.com/v1/"
    
    // 当前使用的API地址
    val CURRENT_BASE_URL = if (BuildConfig.DEBUG) DEV_BASE_URL else BASE_URL
    
    // API端点
    object Endpoints {
        // 用户认证
        const val LOGIN = "auth/login"
        const val REGISTER = "auth/register"
        const val REFRESH_TOKEN = "auth/refresh"
        const val LOGOUT = "auth/logout"
        
        // 用户信息
        const val USER_PROFILE = "user/profile"
        const val UPDATE_PROFILE = "user/profile"
        const val USER_ADDRESSES = "user/addresses"
        
        // 服务相关
        const val SERVICES = "services"
        const val SERVICE_CATEGORIES = "services/categories"
        const val SERVICE_SEARCH = "services/search"
        const val SERVICE_DETAILS = "services/{id}"
        
        // 订单相关
        const val ORDERS = "orders"
        const val CREATE_ORDER = "orders"
        const val ORDER_DETAILS = "orders/{id}"
        const val ORDER_STATUS = "orders/{id}/status"
        const val CANCEL_ORDER = "orders/{id}/cancel"
        
        // 支付相关
        const val CREATE_PAYMENT = "payments/create"
        const val PAYMENT_STATUS = "payments/{id}/status"
        const val PAYMENT_CALLBACK = "payments/callback"
        
        // 评价相关
        const val REVIEWS = "reviews"
        const val CREATE_REVIEW = "reviews"
        const val REVIEW_IMAGES = "reviews/images"
        
        // 收藏相关
        const val FAVORITES = "favorites"
        const val ADD_FAVORITE = "favorites"
        const val REMOVE_FAVORITE = "favorites/{id}"
        
        // 积分和优惠券
        const val POINTS = "user/points"
        const val COUPONS = "user/coupons"
        const val USE_COUPON = "user/coupons/{id}/use"
        
        // 消息和通知
        const val MESSAGES = "messages"
        const val NOTIFICATIONS = "notifications"
        const val MARK_READ = "notifications/{id}/read"
        
        // 文件上传
        const val UPLOAD_IMAGE = "upload/image"
        const val UPLOAD_FILE = "upload/file"
    }
    
    // HTTP状态码
    object StatusCode {
        const val SUCCESS = 200
        const val CREATED = 201
        const val BAD_REQUEST = 400
        const val UNAUTHORIZED = 401
        const val FORBIDDEN = 403
        const val NOT_FOUND = 404
        const val INTERNAL_ERROR = 500
    }
    
    // 请求头
    object Headers {
        const val AUTHORIZATION = "Authorization"
        const val CONTENT_TYPE = "Content-Type"
        const val ACCEPT = "Accept"
        const val USER_AGENT = "User-Agent"
        const val DEVICE_ID = "X-Device-ID"
        const val APP_VERSION = "X-App-Version"
        const val PLATFORM = "X-Platform"
    }
    
    // 请求参数
    object Params {
        const val PAGE = "page"
        const val SIZE = "size"
        const val SORT = "sort"
        const val KEYWORD = "keyword"
        const val CATEGORY = "category"
        const val LATITUDE = "lat"
        const val LONGITUDE = "lng"
        const val RADIUS = "radius"
        const val MIN_PRICE = "min_price"
        const val MAX_PRICE = "max_price"
        const val RATING = "rating"
        const val PROMOTED = "promoted"
    }
}

// API响应基础类
data class ApiResponse<T>(
    val code: Int,
    val message: String,
    val data: T?,
    val timestamp: Long = System.currentTimeMillis()
)

// 分页响应类
data class PageResponse<T>(
    val content: List<T>,
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean
)

// 错误响应类
data class ErrorResponse(
    val code: Int,
    val message: String,
    val details: String? = null,
    val timestamp: Long = System.currentTimeMillis()
)

// 网络配置
object NetworkConfig {
    const val CONNECT_TIMEOUT = 30L // 秒
    const val READ_TIMEOUT = 30L // 秒
    const val WRITE_TIMEOUT = 30L // 秒
    const val CACHE_SIZE = 10 * 1024 * 1024L // 10MB
    const val MAX_RETRY_COUNT = 3
    const val RETRY_DELAY = 1000L // 毫秒
}

// 缓存配置
object CacheConfig {
    const val CACHE_CONTROL_MAX_AGE = 60 * 5 // 5分钟
    const val CACHE_CONTROL_MAX_STALE = 60 * 60 * 24 * 7 // 7天
    const val SERVICES_CACHE_KEY = "services_cache"
    const val CATEGORIES_CACHE_KEY = "categories_cache"
    const val USER_PROFILE_CACHE_KEY = "user_profile_cache"
}
