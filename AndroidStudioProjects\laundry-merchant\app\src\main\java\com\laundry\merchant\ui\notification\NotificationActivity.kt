package com.laundry.merchant.ui.notification

import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.laundry.merchant.databinding.ActivityNotificationBinding
import com.laundry.merchant.ui.notification.adapter.NotificationAdapter
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class NotificationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityNotificationBinding
    private val viewModel: NotificationViewModel by viewModels()
    private lateinit var notificationAdapter: NotificationAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNotificationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerView()
        setupTabs()
        observeViewModel()
        
        // 加载数据
        viewModel.loadNotifications()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "消息通知"

        // 设置刷新监听
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshNotifications()
        }

        // 设置全部已读按钮
        binding.buttonMarkAllRead.setOnClickListener {
            viewModel.markAllAsRead()
        }

        // 设置清空按钮
        binding.buttonClearAll.setOnClickListener {
            showClearAllDialog()
        }
    }

    private fun setupRecyclerView() {
        notificationAdapter = NotificationAdapter(
            onNotificationClick = { notification ->
                viewModel.markAsRead(notification.id)
                handleNotificationClick(notification)
            },
            onDeleteClick = { notification ->
                viewModel.deleteNotification(notification.id)
            }
        )

        binding.recyclerViewNotifications.apply {
            layoutManager = LinearLayoutManager(this@NotificationActivity)
            adapter = notificationAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("全部"))
            addTab(newTab().setText("订单"))
            addTab(newTab().setText("财务"))
            addTab(newTab().setText("投流"))
            addTab(newTab().setText("系统"))

            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    val type = when (tab?.position) {
                        0 -> null
                        1 -> "order"
                        2 -> "finance"
                        3 -> "promotion"
                        4 -> "system"
                        else -> null
                    }
                    viewModel.filterByType(type)
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: NotificationUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新通知列表
        notificationAdapter.updateData(state.notifications)

        // 更新未读数量
        val unreadCount = state.notifications.count { !it.isRead }
        binding.textViewUnreadCount.text = "未读 $unreadCount 条"
        binding.textViewUnreadCount.visibility = if (unreadCount > 0) View.VISIBLE else View.GONE

        // 更新按钮状态
        binding.buttonMarkAllRead.isEnabled = unreadCount > 0
        binding.buttonClearAll.isEnabled = state.notifications.isNotEmpty()

        // 更新空状态
        binding.emptyView.visibility = if (state.notifications.isEmpty() && !state.isLoading) {
            View.VISIBLE
        } else {
            View.GONE
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: NotificationEvent) {
        when (event) {
            is NotificationEvent.ShowError -> {
                showError(event.message)
            }
            is NotificationEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is NotificationEvent.NavigateToOrder -> {
                navigateToOrder(event.orderId)
            }
            is NotificationEvent.NavigateToFinance -> {
                navigateToFinance()
            }
            is NotificationEvent.NavigateToPromotion -> {
                navigateToPromotion()
            }
        }
    }

    private fun handleNotificationClick(notification: NotificationData) {
        when (notification.type) {
            "order" -> {
                notification.relatedId?.let { orderId ->
                    navigateToOrder(orderId)
                }
            }
            "finance" -> {
                navigateToFinance()
            }
            "promotion" -> {
                navigateToPromotion()
            }
            "system" -> {
                // 系统通知可能不需要跳转
            }
        }
    }

    private fun showClearAllDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("清空通知")
            .setMessage("确定要清空所有通知吗？此操作不可撤销。")
            .setPositiveButton("确定") { _, _ ->
                viewModel.clearAllNotifications()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun navigateToOrder(orderId: String) {
        // TODO: 导航到订单详情页面
        finish()
    }

    private fun navigateToFinance() {
        // TODO: 导航到财务页面
        finish()
    }

    private fun navigateToPromotion() {
        // TODO: 导航到投流页面
        finish()
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
