package com.laundry.merchant.ui.product

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.model.Product
import com.laundry.merchant.data.repository.ProductRepository
import com.laundry.merchant.data.repository.FavoriteRepository
import com.laundry.merchant.network.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class ProductDetailViewModel @Inject constructor(
    private val productRepository: ProductRepository,
    private val favoriteRepository: FavoriteRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ProductDetailUiState())
    val uiState: StateFlow<ProductDetailUiState> = _uiState.asStateFlow()

    private val _events = MutableSharedFlow<ProductDetailEvent>()
    val events: SharedFlow<ProductDetailEvent> = _events.asSharedFlow()

    private var currentProductId: String? = null

    fun loadProductDetail(productId: String) {
        currentProductId = productId
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            when (val result = productRepository.getProductById(productId)) {
                is NetworkResult.Success -> {
                    val product = result.data
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        product = product
                    )
                    
                    // 检查是否已收藏
                    checkFavoriteStatus(productId)
                    
                    // 加载评价数据
                    loadProductReviews(productId)
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                    _events.emit(ProductDetailEvent.ShowError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    fun toggleFavorite() {
        val product = _uiState.value.product ?: return
        val isFavorite = _uiState.value.isFavorite

        viewModelScope.launch {
            try {
                if (isFavorite) {
                    // 取消收藏
                    val favoriteId = "fav_${product.id}"
                    favoriteRepository.removeFavorite(favoriteId)
                    _uiState.value = _uiState.value.copy(isFavorite = false)
                    _events.emit(ProductDetailEvent.ShowSuccess("已取消收藏"))
                } else {
                    // 添加收藏
                    val favorite = com.laundry.merchant.ui.favorite.FavoriteData(
                        id = "fav_${product.id}",
                        title = product.name,
                        description = product.description,
                        type = "product",
                        relatedId = product.id,
                        createdAt = Date(),
                        tags = product.tags,
                        imageUrl = product.thumbnailUrl
                    )
                    favoriteRepository.addFavorite(favorite)
                    _uiState.value = _uiState.value.copy(isFavorite = true)
                    _events.emit(ProductDetailEvent.ShowSuccess("已添加到收藏"))
                }
            } catch (e: Exception) {
                _events.emit(ProductDetailEvent.ShowError("操作失败"))
            }
        }
    }

    fun addToCart() {
        val product = _uiState.value.product ?: return
        
        viewModelScope.launch {
            try {
                // TODO: 实现添加到购物车逻辑
                val currentCount = _uiState.value.cartCount
                _uiState.value = _uiState.value.copy(cartCount = currentCount + 1)
                _events.emit(ProductDetailEvent.ShowSuccess("已添加到购物车"))
            } catch (e: Exception) {
                _events.emit(ProductDetailEvent.ShowError("添加失败"))
            }
        }
    }

    fun buyNow() {
        val product = _uiState.value.product ?: return
        
        viewModelScope.launch {
            try {
                // TODO: 实现立即购买逻辑
                _events.emit(ProductDetailEvent.NavigateToCheckout)
            } catch (e: Exception) {
                _events.emit(ProductDetailEvent.ShowError("购买失败"))
            }
        }
    }

    private fun checkFavoriteStatus(productId: String) {
        viewModelScope.launch {
            try {
                val isFavorite = favoriteRepository.isFavorite(productId, "product")
                _uiState.value = _uiState.value.copy(isFavorite = isFavorite)
            } catch (e: Exception) {
                // 检查收藏状态失败不影响主要功能
            }
        }
    }

    private fun loadProductReviews(productId: String) {
        viewModelScope.launch {
            try {
                // TODO: 从API加载真实评价数据
                val mockReviews = getMockReviews()
                _uiState.value = _uiState.value.copy(reviews = mockReviews)
            } catch (e: Exception) {
                // 评价加载失败不影响主要功能
            }
        }
    }

    private fun getMockReviews(): List<ProductReview> {
        return listOf(
            ProductReview(
                id = "review_1",
                userId = "user_1",
                userName = "张**",
                userAvatar = "",
                rating = 5f,
                content = "服务很好，衣服洗得很干净，师傅很专业，会继续使用的。",
                images = listOf(),
                createdAt = Date(),
                isVerified = true,
                hasImages = false,
                isAppended = false
            ),
            ProductReview(
                id = "review_2",
                userId = "user_2",
                userName = "李**",
                userAvatar = "",
                rating = 4f,
                content = "整体不错，就是取送时间稍微有点长，希望能再快一些。",
                images = listOf(),
                createdAt = Date(System.currentTimeMillis() - 86400000),
                isVerified = true,
                hasImages = false,
                isAppended = false
            ),
            ProductReview(
                id = "review_3",
                userId = "user_3",
                userName = "王**",
                userAvatar = "",
                rating = 5f,
                content = "非常满意！衣服洗得很干净，而且还有淡淡的香味，价格也合理。",
                images = listOf("image1.jpg", "image2.jpg"),
                createdAt = Date(System.currentTimeMillis() - 172800000),
                isVerified = true,
                hasImages = true,
                isAppended = false
            )
        )
    }
}

// UI状态数据类
data class ProductDetailUiState(
    val isLoading: Boolean = false,
    val product: Product? = null,
    val reviews: List<ProductReview> = emptyList(),
    val isFavorite: Boolean = false,
    val cartCount: Int = 0,
    val error: String? = null
)

// 事件数据类
sealed class ProductDetailEvent {
    data class ShowError(val message: String) : ProductDetailEvent()
    data class ShowSuccess(val message: String) : ProductDetailEvent()
    object NavigateToCart : ProductDetailEvent()
    object NavigateToCheckout : ProductDetailEvent()
}

// 产品评价数据类
data class ProductReview(
    val id: String,
    val userId: String,
    val userName: String,
    val userAvatar: String,
    val rating: Float,
    val content: String,
    val images: List<String>,
    val createdAt: Date,
    val isVerified: Boolean = false,
    val hasImages: Boolean = false,
    val isAppended: Boolean = false // 是否是追评
)
