package com.laundry.merchant.ui.dialog.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.databinding.ItemGiftCouponBinding
import com.laundry.merchant.utils.CouponType
import com.laundry.merchant.utils.GiftCoupon

class GiftCouponAdapter(
    private val coupons: List<GiftCoupon>
) : RecyclerView.Adapter<GiftCouponAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemGiftCouponBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(coupons[position])
    }

    override fun getItemCount(): Int = coupons.size

    class ViewHolder(private val binding: ItemGiftCouponBinding) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(coupon: GiftCoupon) {
            binding.textViewCouponName.text = coupon.name
            binding.textViewCouponDescription.text = coupon.description
            binding.textViewValidDays.text = "有效期${coupon.validDays}天"
            
            // 设置优惠券值显示
            when (coupon.type) {
                CouponType.DISCOUNT -> {
                    binding.textViewCouponValue.text = "¥${coupon.value.toInt()}"
                    binding.textViewCouponUnit.text = "减"
                }
                CouponType.PERCENTAGE -> {
                    binding.textViewCouponValue.text = "${(coupon.value * 10).toInt()}"
                    binding.textViewCouponUnit.text = "折"
                }
                CouponType.FREE_SHIPPING -> {
                    binding.textViewCouponValue.text = "免费"
                    binding.textViewCouponUnit.text = "配送"
                }
                CouponType.CASH -> {
                    binding.textViewCouponValue.text = "¥${coupon.value.toInt()}"
                    binding.textViewCouponUnit.text = "券"
                }
            }
            
            // 设置最低消费金额
            if (coupon.minAmount > 0) {
                binding.textViewMinAmount.text = "满¥${coupon.minAmount.toInt()}可用"
            } else {
                binding.textViewMinAmount.text = "无门槛"
            }
        }
    }
}
