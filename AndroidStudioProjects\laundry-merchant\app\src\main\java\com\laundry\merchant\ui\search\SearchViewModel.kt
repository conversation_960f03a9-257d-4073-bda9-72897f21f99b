package com.laundry.merchant.ui.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.model.*
import com.laundry.merchant.data.repository.ProductRepository
import com.laundry.merchant.network.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SearchViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()

    private val _events = MutableSharedFlow<SearchEvent>()
    val events: SharedFlow<SearchEvent> = _events.asSharedFlow()

    private val searchHistoryList = mutableListOf<String>()

    fun loadHotSearchKeywords() {
        viewModelScope.launch {
            when (val result = productRepository.getHotSearchKeywords()) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(hotKeywords = result.data)
                }
                is NetworkResult.Error -> {
                    _events.emit(SearchEvent.ShowError("加载热门搜索失败"))
                }
                is NetworkResult.Loading -> {
                    // 处理加载状态
                }
            }
        }
    }

    fun loadSearchHistory() {
        viewModelScope.launch {
            when (val result = productRepository.getSearchHistory()) {
                is NetworkResult.Success -> {
                    searchHistoryList.clear()
                    searchHistoryList.addAll(result.data)
                    _uiState.value = _uiState.value.copy(searchHistory = result.data)
                }
                is NetworkResult.Error -> {
                    // 搜索历史加载失败不显示错误
                }
                is NetworkResult.Loading -> {
                    // 处理加载状态
                }
            }
        }
    }

    fun getSearchSuggestions(query: String) {
        viewModelScope.launch {
            when (val result = productRepository.getSearchSuggestions(query)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(suggestions = result.data)
                }
                is NetworkResult.Error -> {
                    // 搜索建议失败不显示错误
                    _uiState.value = _uiState.value.copy(suggestions = emptyList())
                }
                is NetworkResult.Loading -> {
                    // 处理加载状态
                }
            }
        }
    }

    fun searchProducts(query: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null,
                currentQuery = query
            )

            // 添加到搜索历史
            addToSearchHistory(query)

            when (val result = productRepository.searchProducts(query)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        searchResults = result.data
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message,
                        searchResults = emptyList()
                    )
                    _events.emit(SearchEvent.ShowError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    fun addToCart(product: Product) {
        viewModelScope.launch {
            try {
                // TODO: 实现添加到购物车逻辑
                _events.emit(SearchEvent.ShowSuccess("已添加到购物车"))
            } catch (e: Exception) {
                _events.emit(SearchEvent.ShowError("添加失败"))
            }
        }
    }

    fun removeSearchHistory(keyword: String) {
        searchHistoryList.remove(keyword)
        _uiState.value = _uiState.value.copy(searchHistory = searchHistoryList.toList())
        
        // TODO: 从本地存储中删除
    }

    fun clearSearchHistory() {
        viewModelScope.launch {
            try {
                productRepository.clearSearchHistory()
                searchHistoryList.clear()
                _uiState.value = _uiState.value.copy(searchHistory = emptyList())
                _events.emit(SearchEvent.ShowSuccess("搜索历史已清空"))
            } catch (e: Exception) {
                _events.emit(SearchEvent.ShowError("清空失败"))
            }
        }
    }

    private fun addToSearchHistory(keyword: String) {
        if (keyword.isBlank()) return
        
        // 移除重复项
        searchHistoryList.remove(keyword)
        // 添加到开头
        searchHistoryList.add(0, keyword)
        
        // 限制历史记录数量为10条
        if (searchHistoryList.size > 10) {
            searchHistoryList.removeAt(searchHistoryList.size - 1)
        }
        
        _uiState.value = _uiState.value.copy(searchHistory = searchHistoryList.toList())
        
        // 保存到本地存储
        viewModelScope.launch {
            productRepository.saveSearchHistory(keyword)
        }
    }
}

// UI状态数据类
data class SearchUiState(
    val isLoading: Boolean = false,
    val hotKeywords: List<HotSearchKeyword> = emptyList(),
    val searchHistory: List<String> = emptyList(),
    val suggestions: List<SearchSuggestion> = emptyList(),
    val searchResults: List<Product> = emptyList(),
    val currentQuery: String = "",
    val error: String? = null
)

// 事件数据类
sealed class SearchEvent {
    data class ShowError(val message: String) : SearchEvent()
    data class ShowSuccess(val message: String) : SearchEvent()
}
