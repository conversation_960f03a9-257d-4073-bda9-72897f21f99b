package com.laundry.admin

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GravityCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.navigation.NavigationView
import com.laundry.admin.databinding.ActivityMainBinding
import com.laundry.admin.ui.analytics.AdvancedAnalyticsActivity
import com.laundry.admin.ui.dashboard.AdminDashboardActivity
import com.laundry.admin.ui.finance.FinanceManagementActivity
import com.laundry.admin.ui.merchant.MerchantManagementActivity
import com.laundry.admin.ui.operation.OperationToolsActivity
import com.laundry.admin.ui.order.OrderManagementActivity
import com.laundry.admin.ui.system.SystemConfigActivity
import com.laundry.admin.ui.user.UserManagementActivity
import com.laundry.admin.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MainActivity : AppCompatActivity(), NavigationView.OnNavigationItemSelectedListener {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupNavigation()
        observeViewModel()

        // 检查管理员权限
        viewModel.checkAdminPermissions()

        // 默认显示仪表板
        if (savedInstanceState == null) {
            startActivity(Intent(this, AdminDashboardActivity::class.java))
        }
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_menu)
        supportActionBar?.title = "洗护帮管理后台"

        // 设置抽屉布局
        binding.navigationView.setNavigationItemSelectedListener(this)

        // 设置快捷操作卡片点击事件
        setupQuickActionCards()
    }

    private fun setupQuickActionCards() {
        binding.cardDashboard.setOnClickListener {
            startActivity(Intent(this, AdminDashboardActivity::class.java))
        }

        binding.cardUserManagement.setOnClickListener {
            startActivity(Intent(this, UserManagementActivity::class.java))
        }

        binding.cardMerchantManagement.setOnClickListener {
            startActivity(Intent(this, MerchantManagementActivity::class.java))
        }

        binding.cardOrderManagement.setOnClickListener {
            startActivity(Intent(this, OrderManagementActivity::class.java))
        }

        binding.cardFinanceManagement.setOnClickListener {
            startActivity(Intent(this, FinanceManagementActivity::class.java))
        }

        binding.cardOperationTools.setOnClickListener {
            startActivity(Intent(this, OperationToolsActivity::class.java))
        }

        binding.cardAdvancedAnalytics.setOnClickListener {
            startActivity(Intent(this, AdvancedAnalyticsActivity::class.java))
        }

        binding.cardSystemConfig.setOnClickListener {
            startActivity(Intent(this, SystemConfigActivity::class.java))
        }
    }

    private fun setupNavigation() {
        // 设置导航头部信息
        val headerView = binding.navigationView.getHeaderView(0)
        // 可以在这里设置管理员头像、姓名等信息
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.nav_dashboard -> {
                startActivity(Intent(this, AdminDashboardActivity::class.java))
            }
            R.id.nav_user_management -> {
                startActivity(Intent(this, UserManagementActivity::class.java))
            }
            R.id.nav_merchant_management -> {
                startActivity(Intent(this, MerchantManagementActivity::class.java))
            }
            R.id.nav_order_management -> {
                startActivity(Intent(this, OrderManagementActivity::class.java))
            }
            R.id.nav_finance_management -> {
                startActivity(Intent(this, FinanceManagementActivity::class.java))
            }
            R.id.nav_operation_tools -> {
                startActivity(Intent(this, OperationToolsActivity::class.java))
            }
            R.id.nav_advanced_analytics -> {
                startActivity(Intent(this, AdvancedAnalyticsActivity::class.java))
            }
            R.id.nav_system_config -> {
                startActivity(Intent(this, SystemConfigActivity::class.java))
            }
            R.id.nav_reports -> {
                showReportsDialog()
            }
            R.id.nav_audit_logs -> {
                showAuditLogsDialog()
            }
            R.id.nav_help -> {
                showHelpDialog()
            }
            R.id.nav_settings -> {
                showSettingsDialog()
            }
            R.id.nav_logout -> {
                showLogoutDialog()
            }
        }

        binding.drawerLayout.closeDrawer(GravityCompat.START)
        return true
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_main, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                binding.drawerLayout.openDrawer(GravityCompat.START)
                true
            }
            R.id.action_notifications -> {
                showNotificationsDialog()
                true
            }
            R.id.action_search -> {
                showGlobalSearchDialog()
                true
            }
            R.id.action_emergency -> {
                showEmergencyDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: MainUiState) {
        // 更新快捷统计
        state.quickStats?.let { stats ->
            binding.textViewTotalUsers.text = "${stats.totalUsers}"
            binding.textViewTotalMerchants.text = "${stats.totalMerchants}"
            binding.textViewTotalOrders.text = "${stats.totalOrders}"
            binding.textViewTotalRevenue.text = "${stats.totalRevenue}"

            binding.textViewPendingApprovals.text = "${stats.pendingApprovals}"
            binding.textViewActiveAlerts.text = "${stats.activeAlerts}"
            binding.textViewSystemHealth.text = stats.systemHealth

            // 设置系统健康状态颜色
            val healthColor = when (stats.systemHealth) {
                "正常" -> R.color.green_500
                "警告" -> R.color.orange_500
                "异常" -> R.color.red_500
                else -> R.color.gray_500
            }
            binding.textViewSystemHealth.setTextColor(getColor(healthColor))
        }

        // 更新管理员信息
        state.adminInfo?.let { admin ->
            // 更新导航头部的管理员信息
            val headerView = binding.navigationView.getHeaderView(0)
            // 设置管理员姓名、角色等
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: MainEvent) {
        when (event) {
            is MainEvent.ShowError -> {
                showError(event.message)
            }
            is MainEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is MainEvent.NavigateToLogin -> {
                // 跳转到登录页面
                showToast("请重新登录")
                finish()
            }
            is MainEvent.ShowEmergencyAlert -> {
                showEmergencyAlert(event.message)
            }
        }
    }

    private fun showReportsDialog() {
        val reportTypes = arrayOf(
            "用户报告", "商家报告", "订单报告", "财务报告",
            "运营报告", "系统报告", "自定义报告"
        )

        MaterialAlertDialogBuilder(this)
            .setTitle("生成报告")
            .setItems(reportTypes) { _, which ->
                val reportType = reportTypes[which]
                viewModel.generateReport(reportType)
                showToast("正在生成${reportType}...")
            }
            .show()
    }

    private fun showAuditLogsDialog() {
        val dialog = AuditLogsDialog.newInstance()
        dialog.show(supportFragmentManager, "AuditLogsDialog")
    }

    private fun showHelpDialog() {
        val dialog = HelpDialog.newInstance()
        dialog.show(supportFragmentManager, "HelpDialog")
    }

    private fun showSettingsDialog() {
        val dialog = AdminSettingsDialog.newInstance()
        dialog.show(supportFragmentManager, "AdminSettingsDialog")
    }

    private fun showLogoutDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("退出登录")
            .setMessage("确定要退出管理后台吗？")
            .setPositiveButton("退出") { _, _ ->
                viewModel.logout()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showNotificationsDialog() {
        val dialog = AdminNotificationsDialog.newInstance()
        dialog.show(supportFragmentManager, "AdminNotificationsDialog")
    }

    private fun showGlobalSearchDialog() {
        val dialog = GlobalSearchDialog.newInstance()
        dialog.show(supportFragmentManager, "GlobalSearchDialog")
    }

    private fun showEmergencyDialog() {
        val emergencyActions = arrayOf(
            "启用维护模式", "紧急停止服务", "发送紧急通知",
            "冻结所有交易", "联系技术支持"
        )

        MaterialAlertDialogBuilder(this)
            .setTitle("紧急操作")
            .setItems(emergencyActions) { _, which ->
                val action = emergencyActions[which]
                showEmergencyConfirmDialog(action)
            }
            .show()
    }

    private fun showEmergencyConfirmDialog(action: String) {
        MaterialAlertDialogBuilder(this)
            .setTitle("确认紧急操作")
            .setMessage("确定要执行 \"$action\" 吗？此操作可能影响所有用户。")
            .setPositiveButton("确认") { _, _ ->
                viewModel.executeEmergencyAction(action)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showEmergencyAlert(message: String) {
        MaterialAlertDialogBuilder(this)
            .setTitle("紧急警报")
            .setMessage(message)
            .setPositiveButton("知道了", null)
            .setCancelable(false)
            .show()
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onBackPressed() {
        if (binding.drawerLayout.isDrawerOpen(GravityCompat.START)) {
            binding.drawerLayout.closeDrawer(GravityCompat.START)
        } else {
            super.onBackPressed()
        }
    }

    override fun onResume() {
        super.onResume()
        // 刷新快捷统计数据
        viewModel.refreshQuickStats()
    }
}
