package com.laundry.admin

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import android.widget.TextView
import android.widget.LinearLayout
import android.view.Gravity

class MainActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 创建主布局
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setPadding(50, 100, 50, 100)
        }
        
        // 创建标题
        val titleText = TextView(this).apply {
            text = "洗护帮管理端"
            textSize = 24f
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 50)
        }
        
        // 创建欢迎信息
        val welcomeText = TextView(this).apply {
            text = "欢迎使用洗护帮管理端！\n\n专业的洗护服务管理平台\n\n功能开发中..."
            textSize = 16f
            gravity = Gravity.CENTER
            setLineSpacing(8f, 1f)
        }
        
        // 添加到布局
        mainLayout.addView(titleText)
        mainLayout.addView(welcomeText)
        
        setContentView(mainLayout)
    }
}
