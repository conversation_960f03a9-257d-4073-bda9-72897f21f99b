{"version": "2.0.0", "tasks": [{"label": "android: build debug", "type": "shell", "command": "./gradlew", "args": ["assembleDebug"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "android: build release", "type": "shell", "command": "./gradlew", "args": ["assembleRelease"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "android: clean", "type": "shell", "command": "./gradlew", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "android: install debug", "type": "shell", "command": "./gradlew", "args": ["installDebug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}, "dependsOn": "android: build debug"}, {"label": "android: run tests", "type": "shell", "command": "./gradlew", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "android: run instrumented tests", "type": "shell", "command": "./gradlew", "args": ["connectedAndroidTest"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "android: lint check", "type": "shell", "command": "./gradlew", "args": ["lint"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "adb: list devices", "type": "shell", "command": "adb", "args": ["devices"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "adb: start emulator", "type": "shell", "command": "emulator", "args": ["-avd", "Pixel_4_API_30"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "isBackground": true}, {"label": "adb: logcat", "type": "shell", "command": "adb", "args": ["logcat", "-v", "time", "com.laundry.user:V", "*:S"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "isBackground": true}]}