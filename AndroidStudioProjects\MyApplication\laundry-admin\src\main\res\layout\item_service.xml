<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <ImageView
            android:id="@+id/image_service"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="12dp"
            android:background="@drawable/circle_background"
            android:padding="16dp"
            android:src="@drawable/ic_laundry_24"
            app:tint="@color/primary_color" />

        <TextView
            android:id="@+id/text_service_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="普通洗衣"
            android:textColor="@color/primary_text"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_service_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:text="日常衣物清洗"
            android:textColor="@color/secondary_text"
            android:textSize="12sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/text_service_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/category_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:text="洗衣"
                android:textColor="@color/primary_color"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/text_service_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="¥15"
                android:textColor="@color/price_color"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
