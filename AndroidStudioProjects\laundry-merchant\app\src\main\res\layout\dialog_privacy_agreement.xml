<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_overlay"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="24dp">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 标题栏 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/privacy_agreement_title"
                    android:textColor="@color/gray_800"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/buttonClose"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:src="@drawable/ic_close"
                    app:tint="@color/gray_600" />

            </RelativeLayout>

            <!-- 内容 -->
            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:maxHeight="400dp">

                <TextView
                    android:id="@+id/textViewContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="4dp"
                    android:text="@string/privacy_agreement_content"
                    android:textColor="@color/gray_700"
                    android:textSize="14sp" />

            </ScrollView>

            <!-- 按钮区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/buttonDisagree"
                    style="@style/Button.LaundryMerchant.Outlined"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:text="@string/disagree_and_exit"
                    android:textColor="@color/gray_600" />

                <Button
                    android:id="@+id/buttonAgree"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:text="@string/read_and_agree" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
