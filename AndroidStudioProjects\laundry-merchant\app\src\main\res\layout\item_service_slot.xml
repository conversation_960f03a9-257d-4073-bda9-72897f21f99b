<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:background="@drawable/slot_normal_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="12dp">

    <TextView
        android:id="@+id/textViewTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:text="09:00-10:00"
        android:textColor="@color/gray_800"
        android:textSize="14sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/textViewPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="2dp"
        android:text="¥50.00"
        android:textColor="@color/red_500"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/textViewUrgentPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:text="紧急: ¥75.00"
        android:textColor="@color/orange_500"
        android:textSize="10sp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/textViewStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="可预约"
        android:textColor="@color/green_500"
        android:textSize="10sp" />

</LinearLayout>
