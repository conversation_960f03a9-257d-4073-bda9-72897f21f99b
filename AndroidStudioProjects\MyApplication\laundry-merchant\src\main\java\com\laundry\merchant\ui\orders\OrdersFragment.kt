package com.laundry.merchant.ui.orders

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.laundry.merchant.databinding.FragmentOrdersBinding
import com.laundry.merchant.model.OrderStatus

class OrdersFragment : Fragment() {

    private var _binding: FragmentOrdersBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var ordersViewModel: OrdersViewModel
    private lateinit var orderAdapter: MerchantOrderAdapter
    private var currentStatus: OrderStatus? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        ordersViewModel = ViewModelProvider(this)[OrdersViewModel::class.java]

        _binding = FragmentOrdersBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupTabs()
        setupRecyclerView()
        observeViewModel()

        return root
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("待接单"))
            addTab(newTab().setText("进行中"))
            addTab(newTab().setText("已完成"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    currentStatus = when (tab?.position) {
                        0 -> OrderStatus.PENDING
                        1 -> null // 进行中包含多个状态
                        2 -> OrderStatus.COMPLETED
                        else -> null
                    }
                    ordersViewModel.filterOrders(currentStatus, tab?.position == 1)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun setupRecyclerView() {
        orderAdapter = MerchantOrderAdapter(
            onAcceptOrder = { order ->
                ordersViewModel.acceptOrder(order.id)
            },
            onUpdateStatus = { order, newStatus ->
                ordersViewModel.updateOrderStatus(order.id, newStatus)
            },
            onOrderClick = { order ->
                // Navigate to order details
            }
        )
        
        binding.recyclerViewOrders.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = orderAdapter
        }
    }

    private fun observeViewModel() {
        ordersViewModel.filteredOrders.observe(viewLifecycleOwner) { orders ->
            orderAdapter.submitList(orders)
            binding.textEmptyOrders.visibility = if (orders.isEmpty()) View.VISIBLE else View.GONE
        }
        
        ordersViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
