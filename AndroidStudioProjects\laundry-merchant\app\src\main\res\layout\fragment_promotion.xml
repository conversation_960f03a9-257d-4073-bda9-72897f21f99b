<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 标签页 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        app:tabIndicatorColor="@color/colorPrimary"
        app:tabSelectedTextColor="@color/colorPrimary"
        app:tabTextColor="@color/gray_600" />

    <!-- 内容区域 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <!-- 概览内容 -->
                <LinearLayout
                    android:id="@+id/layoutOverview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 推广状态卡片 -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="4dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/gradient_background"
                            android:orientation="vertical"
                            android:padding="20dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/textPromotionStatus"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="投流中"
                                        android:textColor="@android:color/white"
                                        android:textSize="24sp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/textBalance"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="4dp"
                                        android:text="余额: ¥1,280.50"
                                        android:textColor="@android:color/white"
                                        android:textSize="16sp" />

                                </LinearLayout>

                                <Button
                                    android:id="@+id/buttonStartPromotion"
                                    android:layout_width="wrap_content"
                                    android:layout_height="40dp"
                                    android:background="@drawable/button_white_background"
                                    android:paddingHorizontal="20dp"
                                    android:text="暂停投流"
                                    android:textColor="@color/colorPrimary"
                                    android:textSize="14sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="16dp"
                                android:orientation="horizontal">

                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/textDailyBudget"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="日预算: ¥200.00"
                                        android:textColor="@android:color/white"
                                        android:textSize="14sp" />

                                    <TextView
                                        android:id="@+id/textTodaySpent"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="4dp"
                                        android:text="今日消费: ¥156.00"
                                        android:textColor="@android:color/white"
                                        android:textSize="14sp" />

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <Button
                                        android:id="@+id/buttonDecreaseBudget"
                                        android:layout_width="36dp"
                                        android:layout_height="36dp"
                                        android:layout_marginEnd="8dp"
                                        android:background="@drawable/button_white_background"
                                        android:text="-"
                                        android:textColor="@color/colorPrimary"
                                        android:textSize="18sp"
                                        android:textStyle="bold" />

                                    <Button
                                        android:id="@+id/buttonIncreaseBudget"
                                        android:layout_width="36dp"
                                        android:layout_height="36dp"
                                        android:background="@drawable/button_white_background"
                                        android:text="+"
                                        android:textColor="@color/colorPrimary"
                                        android:textSize="18sp"
                                        android:textStyle="bold" />

                                </LinearLayout>

                            </LinearLayout>

                            <Button
                                android:id="@+id/buttonRecharge"
                                android:layout_width="match_parent"
                                android:layout_height="40dp"
                                android:layout_marginTop="16dp"
                                android:background="@drawable/button_white_background"
                                android:text="推广充值"
                                android:textColor="@color/colorPrimary"
                                android:textSize="14sp" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- 数据统计 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_marginEnd="4dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="4dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/textTotalClicks"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="1,250"
                                    android:textColor="@color/blue_500"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="总点击"
                                    android:textColor="@color/gray_600"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_marginHorizontal="4dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="4dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/textTotalImpressions"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="15,600"
                                    android:textColor="@color/green_500"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="总展现"
                                    android:textColor="@color/gray_600"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_marginStart="4dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="4dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/textClickRate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="8.0%"
                                    android:textColor="@color/orange_500"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="点击率"
                                    android:textColor="@color/gray_600"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <!-- 更多统计 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="4dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/textAverageCpc"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="¥1.25"
                                    android:textColor="@color/purple_500"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="平均点击价格"
                                    android:textColor="@color/gray_600"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="80dp"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="4dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/textConversionRate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="12.0%"
                                    android:textColor="@color/red_500"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="转化率"
                                    android:textColor="@color/gray_600"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                </LinearLayout>

                <!-- 推广计划内容 -->
                <LinearLayout
                    android:id="@+id/layoutCampaigns"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:visibility="gone">

                    <Button
                        android:id="@+id/buttonCreateCampaign"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/button_primary_background"
                        android:text="+ 创建推广计划"
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerViewCampaigns"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- 关键词内容 -->
                <LinearLayout
                    android:id="@+id/layoutKeywords"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:visibility="gone">

                    <Button
                        android:id="@+id/buttonAddKeyword"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/button_primary_background"
                        android:text="+ 添加关键词"
                        android:textColor="@android:color/white"
                        android:textSize="16sp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerViewKeywords"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- 数据报告内容 -->
                <LinearLayout
                    android:id="@+id/layoutReports"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="32dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:alpha="0.5"
                        android:src="@drawable/ic_chart"
                        android:tint="@color/gray_400" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="数据报告"
                        android:textColor="@color/gray_600"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="详细的推广数据分析报告即将上线"
                        android:textColor="@color/gray_500"
                        android:textSize="14sp" />

                </LinearLayout>

            </FrameLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>
