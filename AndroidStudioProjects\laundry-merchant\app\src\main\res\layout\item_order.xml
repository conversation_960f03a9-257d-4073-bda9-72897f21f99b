<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 订单头部信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 紧急标识 -->
            <View
                android:id="@+id/viewUrgentIndicator"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="8dp"
                android:background="@color/red_500"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textViewOrderId"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="ORD001"
                        android:textColor="@color/gray_800"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textViewTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="10:30"
                        android:textColor="@color/gray_500"
                        android:textSize="12sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textViewCustomerName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="张三"
                        android:textColor="@color/gray_600"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textViewStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="待接单"
                        android:textColor="@color/orange_500"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/textViewAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:text="¥25.00"
                android:textColor="@color/green_500"
                android:textSize="18sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- 服务信息 -->
        <TextView
            android:id="@+id/textViewServiceName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="普通洗衣"
            android:textColor="@color/gray_700"
            android:textSize="14sp"
            android:textStyle="bold" />

        <!-- 地址信息 -->
        <TextView
            android:id="@+id/textViewAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:drawableStart="@drawable/ic_location"
            android:drawablePadding="4dp"
            android:drawableTint="@color/gray_500"
            android:text="北京市朝阳区某某小区1号楼101室"
            android:textColor="@color/gray_600"
            android:textSize="12sp" />

        <!-- 备注信息 -->
        <TextView
            android:id="@+id/textViewNotes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@drawable/background_notes"
            android:padding="8dp"
            android:text="备注: 请小心处理，有贵重衣物"
            android:textColor="@color/gray_600"
            android:textSize="12sp"
            android:visibility="gone" />

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/buttonAccept"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_primary_background"
                android:text="接单"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/buttonUpdateStatus"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_primary_background"
                android:text="更新状态"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/buttonCall"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:background="@drawable/button_outline_background"
                android:drawableStart="@drawable/ic_phone"
                android:drawablePadding="4dp"
                android:drawableTint="@color/colorPrimary"
                android:paddingHorizontal="12dp"
                android:text="联系"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
