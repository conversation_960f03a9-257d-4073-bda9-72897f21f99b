package com.laundry.merchant.data.local

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "laundry_merchant_preferences")

@Singleton
class PreferencesManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val dataStore = context.dataStore

    companion object {
        // 认证相关
        private val ACCESS_TOKEN = stringPreferencesKey("access_token")
        private val REFRESH_TOKEN = stringPreferencesKey("refresh_token")
        private val IS_LOGGED_IN = booleanPreferencesKey("is_logged_in")
        private val MERCHANT_ID = stringPreferencesKey("merchant_id")
        
        // 用户设置
        private val NOTIFICATION_ENABLED = booleanPreferencesKey("notification_enabled")
        private val SOUND_ENABLED = booleanPreferencesKey("sound_enabled")
        private val VIBRATION_ENABLED = booleanPreferencesKey("vibration_enabled")
        private val AUTO_ACCEPT_ORDERS = booleanPreferencesKey("auto_accept_orders")
        
        // 应用设置
        private val FIRST_LAUNCH = booleanPreferencesKey("first_launch")
        private val LAST_SYNC_TIME = stringPreferencesKey("last_sync_time")
        private val SELECTED_LANGUAGE = stringPreferencesKey("selected_language")
        private val THEME_MODE = stringPreferencesKey("theme_mode")
        
        // 缓存设置
        private val CACHE_EXPIRY_TIME = stringPreferencesKey("cache_expiry_time")
        private val LAST_LOCATION = stringPreferencesKey("last_location")
    }

    // 认证相关方法
    suspend fun saveAccessToken(token: String) {
        dataStore.edit { preferences ->
            preferences[ACCESS_TOKEN] = token
        }
    }

    fun getAccessToken(): Flow<String?> {
        return dataStore.data.map { preferences ->
            preferences[ACCESS_TOKEN]
        }
    }

    suspend fun saveRefreshToken(token: String) {
        dataStore.edit { preferences ->
            preferences[REFRESH_TOKEN] = token
        }
    }

    fun getRefreshToken(): Flow<String?> {
        return dataStore.data.map { preferences ->
            preferences[REFRESH_TOKEN]
        }
    }

    suspend fun saveLoginState(isLoggedIn: Boolean) {
        dataStore.edit { preferences ->
            preferences[IS_LOGGED_IN] = isLoggedIn
        }
    }

    fun getLoginState(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[IS_LOGGED_IN] ?: false
        }
    }

    suspend fun saveMerchantId(merchantId: String) {
        dataStore.edit { preferences ->
            preferences[MERCHANT_ID] = merchantId
        }
    }

    fun getMerchantId(): Flow<String?> {
        return dataStore.data.map { preferences ->
            preferences[MERCHANT_ID]
        }
    }

    suspend fun clearTokens() {
        dataStore.edit { preferences ->
            preferences.remove(ACCESS_TOKEN)
            preferences.remove(REFRESH_TOKEN)
            preferences[IS_LOGGED_IN] = false
        }
    }

    // 通知设置
    suspend fun setNotificationEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[NOTIFICATION_ENABLED] = enabled
        }
    }

    fun isNotificationEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[NOTIFICATION_ENABLED] ?: true
        }
    }

    suspend fun setSoundEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[SOUND_ENABLED] = enabled
        }
    }

    fun isSoundEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[SOUND_ENABLED] ?: true
        }
    }

    suspend fun setVibrationEnabled(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[VIBRATION_ENABLED] = enabled
        }
    }

    fun isVibrationEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[VIBRATION_ENABLED] ?: true
        }
    }

    suspend fun setAutoAcceptOrders(enabled: Boolean) {
        dataStore.edit { preferences ->
            preferences[AUTO_ACCEPT_ORDERS] = enabled
        }
    }

    fun isAutoAcceptOrdersEnabled(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[AUTO_ACCEPT_ORDERS] ?: false
        }
    }

    // 应用设置
    suspend fun setFirstLaunch(isFirst: Boolean) {
        dataStore.edit { preferences ->
            preferences[FIRST_LAUNCH] = isFirst
        }
    }

    fun isFirstLaunch(): Flow<Boolean> {
        return dataStore.data.map { preferences ->
            preferences[FIRST_LAUNCH] ?: true
        }
    }

    suspend fun setLastSyncTime(time: String) {
        dataStore.edit { preferences ->
            preferences[LAST_SYNC_TIME] = time
        }
    }

    fun getLastSyncTime(): Flow<String?> {
        return dataStore.data.map { preferences ->
            preferences[LAST_SYNC_TIME]
        }
    }

    suspend fun setSelectedLanguage(language: String) {
        dataStore.edit { preferences ->
            preferences[SELECTED_LANGUAGE] = language
        }
    }

    fun getSelectedLanguage(): Flow<String> {
        return dataStore.data.map { preferences ->
            preferences[SELECTED_LANGUAGE] ?: "zh"
        }
    }

    suspend fun setThemeMode(mode: String) {
        dataStore.edit { preferences ->
            preferences[THEME_MODE] = mode
        }
    }

    fun getThemeMode(): Flow<String> {
        return dataStore.data.map { preferences ->
            preferences[THEME_MODE] ?: "system"
        }
    }

    // 缓存相关
    suspend fun setCacheExpiryTime(time: String) {
        dataStore.edit { preferences ->
            preferences[CACHE_EXPIRY_TIME] = time
        }
    }

    fun getCacheExpiryTime(): Flow<String?> {
        return dataStore.data.map { preferences ->
            preferences[CACHE_EXPIRY_TIME]
        }
    }

    suspend fun setLastLocation(location: String) {
        dataStore.edit { preferences ->
            preferences[LAST_LOCATION] = location
        }
    }

    fun getLastLocation(): Flow<String?> {
        return dataStore.data.map { preferences ->
            preferences[LAST_LOCATION]
        }
    }

    // 清除所有数据
    suspend fun clearAll() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }
}
