package com.laundry.merchant.utils

import android.content.Context
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.laundry.merchant.data.local.PreferencesManager
import kotlinx.coroutines.flow.first
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WelcomeGiftManager @Inject constructor(
    private val preferencesManager: PreferencesManager
) {

    companion object {
        private val WELCOME_GIFT_CLAIMED = booleanPreferencesKey("welcome_gift_claimed")
        private val WELCOME_GIFT_DATE = stringPreferencesKey("welcome_gift_date")
    }

    /**
     * 检查是否已领取新手礼包
     */
    suspend fun isWelcomeGiftClaimed(): Boolean {
        return try {
            preferencesManager.getDataStore().data.first()[WELCOME_GIFT_CLAIMED] ?: false
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取新手礼包内容
     */
    fun getWelcomeGiftContent(): WelcomeGift {
        return WelcomeGift(
            coupons = listOf(
                GiftCoupon(
                    id = "welcome_coupon_1",
                    name = "新用户专享券",
                    type = CouponType.DISCOUNT,
                    value = 20.0,
                    minAmount = 100.0,
                    description = "满100减20",
                    validDays = 30
                ),
                GiftCoupon(
                    id = "welcome_coupon_2",
                    name = "首单免费券",
                    type = CouponType.FREE_SHIPPING,
                    value = 0.0,
                    minAmount = 0.0,
                    description = "首单免配送费",
                    validDays = 7
                )
            ),
            points = 100,
            description = "恭喜您注册成功！获得新手礼包：满减券2张、积分100分"
        )
    }

    /**
     * 领取新手礼包
     */
    suspend fun claimWelcomeGift(): ClaimResult {
        return try {
            if (isWelcomeGiftClaimed()) {
                ClaimResult.AlreadyClaimed
            } else {
                // 标记为已领取
                preferencesManager.getDataStore().edit { preferences ->
                    preferences[WELCOME_GIFT_CLAIMED] = true
                    preferences[WELCOME_GIFT_DATE] = getCurrentTimeString()
                }

                // TODO: 调用服务器API发放礼包
                // 这里应该调用后端接口，实际发放优惠券和积分

                ClaimResult.Success(getWelcomeGiftContent())
            }
        } catch (e: Exception) {
            ClaimResult.Error("领取失败: ${e.message}")
        }
    }

    /**
     * 重置新手礼包状态（用于测试）
     */
    suspend fun resetWelcomeGiftStatus() {
        preferencesManager.getDataStore().edit { preferences ->
            preferences.remove(WELCOME_GIFT_CLAIMED)
            preferences.remove(WELCOME_GIFT_DATE)
        }
    }

    /**
     * 检查用户是否符合新手礼包条件
     */
    suspend fun isEligibleForWelcomeGift(): Boolean {
        // 检查是否是新注册用户且未领取过礼包
        return !isWelcomeGiftClaimed()
    }

    /**
     * 获取礼包领取时间
     */
    suspend fun getWelcomeGiftClaimDate(): String? {
        return try {
            preferencesManager.getDataStore().data.first()[WELCOME_GIFT_DATE]
        } catch (e: Exception) {
            null
        }
    }

    private fun getCurrentTimeString(): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            .format(Date())
    }
}

data class WelcomeGift(
    val coupons: List<GiftCoupon>,
    val points: Int,
    val description: String
)

data class GiftCoupon(
    val id: String,
    val name: String,
    val type: CouponType,
    val value: Double,
    val minAmount: Double,
    val description: String,
    val validDays: Int
)

enum class CouponType {
    DISCOUNT,        // 满减券
    PERCENTAGE,      // 折扣券
    FREE_SHIPPING,   // 免配送费
    CASH             // 现金券
}

sealed class ClaimResult {
    data class Success(val gift: WelcomeGift) : ClaimResult()
    object AlreadyClaimed : ClaimResult()
    data class Error(val message: String) : ClaimResult()
}
