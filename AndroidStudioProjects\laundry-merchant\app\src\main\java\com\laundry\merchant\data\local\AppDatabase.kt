package com.laundry.merchant.data.local

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.laundry.merchant.data.local.dao.OrderDao
import com.laundry.merchant.data.local.dao.TransactionDao
import com.laundry.merchant.data.local.entity.OrderEntity
import com.laundry.merchant.data.local.entity.TransactionEntity

@Database(
    entities = [
        OrderEntity::class,
        TransactionEntity::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun orderDao(): OrderDao
    abstract fun transactionDao(): TransactionDao
    
    companion object {
        const val DATABASE_NAME = "laundry_merchant_database"
    }
}
