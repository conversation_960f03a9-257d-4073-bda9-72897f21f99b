<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.main.MainActivity">

    <!-- 主内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 顶部工具栏 -->
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="?attr/colorPrimary"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <!-- 商家头像 -->
                    <ImageView
                        android:id="@+id/merchantAvatar"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/circle_background"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_merchant_placeholder" />

                    <!-- 商家信息 -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/merchantName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="商家名称"
                                android:textColor="@android:color/white"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <ImageView
                                android:id="@+id/verifiedBadge"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginStart="4dp"
                                android:src="@drawable/ic_verified"
                                android:visibility="gone" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/merchantRating"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="⭐ 4.6"
                            android:textColor="@android:color/white"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <!-- 投流状态指示器 -->
                    <View
                        android:id="@+id/promotionStatusIndicator"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/circle_background"
                        android:backgroundTint="@color/green_500" />

                    <!-- 通知按钮 -->
                    <FrameLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp">

                        <ImageButton
                            android:id="@+id/notificationButton"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:src="@drawable/ic_notifications"
                            android:tint="@android:color/white" />

                        <TextView
                            android:id="@+id/notificationBadge"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_gravity="top|end"
                            android:layout_marginTop="4dp"
                            android:layout_marginEnd="4dp"
                            android:background="@drawable/circle_background"
                            android:backgroundTint="@color/red_500"
                            android:gravity="center"
                            android:text="3"
                            android:textColor="@android:color/white"
                            android:textSize="10sp"
                            android:visibility="gone" />

                    </FrameLayout>

                    <!-- 设置按钮 -->
                    <ImageButton
                        android:id="@+id/settingsButton"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginEnd="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:src="@drawable/ic_settings"
                        android:tint="@android:color/white" />

                </LinearLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <!-- 网络错误提示 -->
        <LinearLayout
            android:id="@+id/networkErrorView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/red_500"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="8dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_wifi_off"
                android:tint="@android:color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="网络连接异常，点击重试"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 加载进度条 -->
        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:indeterminate="true"
            android:visibility="gone" />

        <!-- Fragment容器 -->
        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- 底部导航 -->
        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottomNavigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            app:elevation="8dp"
            app:menu="@menu/bottom_navigation" />

    </LinearLayout>

    <!-- 悬浮按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
