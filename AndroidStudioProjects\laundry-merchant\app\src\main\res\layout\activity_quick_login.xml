<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 标题 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:text="@string/quick_login"
                android:textColor="@color/gray_800"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- 手机号输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/phone_hint"
                app:startIconDrawable="@drawable/ic_phone">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone"
                    android:maxLength="11" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 验证码输入 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:orientation="horizontal">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/textInputLayoutVerificationCode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:layout_weight="1"
                    android:hint="@string/verification_code_hint"
                    app:startIconDrawable="@drawable/ic_verified">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextVerificationCode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="6" />

                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/buttonSendCode"
                    style="@style/Button.LaundryMerchant.Outlined"
                    android:layout_width="wrap_content"
                    android:layout_height="56dp"
                    android:enabled="false"
                    android:text="@string/send_code"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 登录按钮 -->
            <Button
                android:id="@+id/buttonLogin"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="24dp"
                android:enabled="false"
                android:text="@string/login_title"
                android:textSize="16sp" />

            <!-- 密码登录 -->
            <TextView
                android:id="@+id/textViewPasswordLogin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="12dp"
                android:text="密码登录"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp" />

        </LinearLayout>

    </ScrollView>

    <!-- 加载进度 -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="16dp"
        android:visibility="gone" />

</LinearLayout>
