package com.laundry.user.data.database

import androidx.room.*
import androidx.lifecycle.LiveData
import kotlinx.coroutines.flow.Flow
import com.laundry.user.data.model.*

/**
 * 用户数据访问对象
 */
@Dao
interface UserDao {
    @Query("SELECT * FROM users WHERE id = :userId")
    suspend fun getUserById(userId: String): User?
    
    @Query("SELECT * FROM users WHERE phone = :phone")
    suspend fun getUserByPhone(phone: String): User?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: User)
    
    @Update
    suspend fun updateUser(user: User)
    
    @Delete
    suspend fun deleteUser(user: User)
    
    @Query("DELETE FROM users")
    suspend fun deleteAllUsers()
}

/**
 * 服务分类数据访问对象
 */
@Dao
interface ServiceCategoryDao {
    @Query("SELECT * FROM service_categories WHERE is_active = 1 ORDER BY sort_order ASC")
    fun getAllActiveCategories(): Flow<List<ServiceCategory>>
    
    @Query("SELECT * FROM service_categories WHERE id = :categoryId")
    suspend fun getCategoryById(categoryId: String): ServiceCategory?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategories(categories: List<ServiceCategory>)
    
    @Query("DELETE FROM service_categories")
    suspend fun deleteAllCategories()
}

/**
 * 商家数据访问对象
 */
@Dao
interface MerchantDao {
    @Query("SELECT * FROM merchants WHERE id = :merchantId")
    suspend fun getMerchantById(merchantId: String): Merchant?
    
    @Query("SELECT * FROM merchants WHERE is_verified = 1 ORDER BY is_promoted DESC, rating DESC")
    fun getAllVerifiedMerchants(): Flow<List<Merchant>>
    
    @Query("SELECT * FROM merchants WHERE is_verified = 1 AND service_categories LIKE '%' || :categoryId || '%' ORDER BY is_promoted DESC, rating DESC")
    fun getMerchantsByCategory(categoryId: String): Flow<List<Merchant>>
    
    @Query("SELECT * FROM merchants WHERE is_verified = 1 AND (name LIKE '%' || :keyword || '%' OR address LIKE '%' || :keyword || '%') ORDER BY is_promoted DESC, rating DESC")
    fun searchMerchants(keyword: String): Flow<List<Merchant>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMerchants(merchants: List<Merchant>)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMerchant(merchant: Merchant)
    
    @Query("DELETE FROM merchants")
    suspend fun deleteAllMerchants()
}

/**
 * 服务数据访问对象
 */
@Dao
interface ServiceDao {
    @Query("SELECT * FROM services WHERE merchant_id = :merchantId AND is_available = 1")
    fun getServicesByMerchant(merchantId: String): Flow<List<Service>>
    
    @Query("SELECT * FROM services WHERE category_id = :categoryId AND is_available = 1")
    fun getServicesByCategory(categoryId: String): Flow<List<Service>>
    
    @Query("SELECT * FROM services WHERE id = :serviceId")
    suspend fun getServiceById(serviceId: String): Service?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertServices(services: List<Service>)
    
    @Query("DELETE FROM services WHERE merchant_id = :merchantId")
    suspend fun deleteServicesByMerchant(merchantId: String)
}

/**
 * 订单数据访问对象
 */
@Dao
interface OrderDao {
    @Query("SELECT * FROM orders WHERE user_id = :userId ORDER BY created_at DESC")
    fun getOrdersByUser(userId: String): Flow<List<Order>>
    
    @Query("SELECT * FROM orders WHERE id = :orderId")
    suspend fun getOrderById(orderId: String): Order?
    
    @Query("SELECT * FROM orders WHERE user_id = :userId AND status = :status ORDER BY created_at DESC")
    fun getOrdersByStatus(userId: String, status: Int): Flow<List<Order>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrder(order: Order)
    
    @Update
    suspend fun updateOrder(order: Order)
    
    @Delete
    suspend fun deleteOrder(order: Order)
    
    @Query("SELECT COUNT(*) FROM orders WHERE user_id = :userId AND status NOT IN (6, 7)")
    suspend fun getActiveOrderCount(userId: String): Int
}

/**
 * 评价数据访问对象
 */
@Dao
interface ReviewDao {
    @Query("SELECT * FROM reviews WHERE merchant_id = :merchantId ORDER BY created_at DESC")
    fun getReviewsByMerchant(merchantId: String): Flow<List<Review>>
    
    @Query("SELECT * FROM reviews WHERE user_id = :userId ORDER BY created_at DESC")
    fun getReviewsByUser(userId: String): Flow<List<Review>>
    
    @Query("SELECT * FROM reviews WHERE order_id = :orderId")
    suspend fun getReviewByOrder(orderId: String): Review?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReview(review: Review)
    
    @Update
    suspend fun updateReview(review: Review)
    
    @Delete
    suspend fun deleteReview(review: Review)
}

/**
 * 优惠券数据访问对象
 */
@Dao
interface CouponDao {
    @Query("SELECT * FROM coupons WHERE is_used = 0 AND valid_until > :currentTime ORDER BY valid_until ASC")
    fun getAvailableCoupons(currentTime: String): Flow<List<Coupon>>
    
    @Query("SELECT * FROM coupons WHERE id = :couponId")
    suspend fun getCouponById(couponId: String): Coupon?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCoupons(coupons: List<Coupon>)
    
    @Update
    suspend fun updateCoupon(coupon: Coupon)
    
    @Query("DELETE FROM coupons WHERE valid_until < :currentTime")
    suspend fun deleteExpiredCoupons(currentTime: String)
}
