<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 选择框 -->
        <CheckBox
            android:id="@+id/checkboxSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp" />

        <!-- 商品图片 -->
        <ImageView
            android:id="@+id/imageViewProduct"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="12dp"
            android:scaleType="centerCrop"
            android:src="@drawable/placeholder_product" />

        <!-- 商品信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 商品名称 -->
            <TextView
                android:id="@+id/textViewProductName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="普通洗衣服务"
                android:textColor="@color/gray_800"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 规格信息 -->
            <TextView
                android:id="@+id/textViewSpecs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="规格: 标准"
                android:textColor="@color/gray_600"
                android:textSize="12sp"
                android:visibility="gone" />

            <!-- 状态信息 -->
            <TextView
                android:id="@+id/textViewStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:background="@drawable/tag_stock_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:text="库存不足"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:visibility="gone" />

            <!-- 价格信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewCurrentPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥25.00"
                    android:textColor="@color/red_500"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewOriginalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="¥30.00"
                    android:textColor="@color/gray_500"
                    android:textSize="12sp"
                    android:visibility="gone" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/textViewTotalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥50.00"
                    android:textColor="@color/gray_800"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- 数量控制和删除 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- 数量控制 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/quantity_control_background"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageButton
                        android:id="@+id/buttonDecrease"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:src="@drawable/ic_remove"
                        app:tint="@color/gray_600" />

                    <TextView
                        android:id="@+id/textViewQuantity"
                        android:layout_width="40dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="@color/gray_800"
                        android:textSize="14sp" />

                    <ImageButton
                        android:id="@+id/buttonIncrease"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:src="@drawable/ic_add"
                        app:tint="@color/gray_600" />

                </LinearLayout>

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <!-- 删除按钮 -->
                <ImageButton
                    android:id="@+id/buttonRemove"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_delete"
                    app:tint="@color/gray_500" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
