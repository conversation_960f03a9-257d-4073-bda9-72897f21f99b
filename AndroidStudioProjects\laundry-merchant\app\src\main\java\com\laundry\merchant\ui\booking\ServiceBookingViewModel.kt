package com.laundry.merchant.ui.booking

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.model.*
import com.laundry.merchant.data.repository.BookingRepository
import com.laundry.merchant.network.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class ServiceBookingViewModel @Inject constructor(
    private val bookingRepository: BookingRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ServiceBookingUiState())
    val uiState: StateFlow<ServiceBookingUiState> = _uiState.asStateFlow()

    private val _events = MutableSharedFlow<ServiceBookingEvent>()
    val events: SharedFlow<ServiceBookingEvent> = _events.asSharedFlow()

    fun loadAvailableSlots(serviceType: ServiceType, date: Date, isUrgent: Boolean) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            when (val result = bookingRepository.getAvailableSlots(serviceType, date, isUrgent)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        availableSlots = result.data,
                        selectedSlot = null // 重置选择
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                    _events.emit(ServiceBookingEvent.ShowError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    fun loadMerchantAvailability() {
        viewModelScope.launch {
            when (val result = bookingRepository.getMerchantAvailability()) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        merchantAvailability = result.data
                    )
                }
                is NetworkResult.Error -> {
                    _events.emit(ServiceBookingEvent.ShowError("获取商家状态失败"))
                }
                is NetworkResult.Loading -> {
                    // 处理加载状态
                }
            }
        }
    }

    fun selectTimeSlot(slot: ServiceSlot) {
        if (slot.isAvailable && !slot.isFullyBooked()) {
            _uiState.value = _uiState.value.copy(selectedSlot = slot)
        } else {
            viewModelScope.launch {
                _events.emit(ServiceBookingEvent.ShowError("该时段不可用"))
            }
        }
    }

    fun selectMerchant(merchant: MerchantAvailability) {
        _uiState.value = _uiState.value.copy(selectedMerchant = merchant)
    }

    fun createBooking(
        customerName: String,
        customerPhone: String,
        customerAddress: String,
        notes: String
    ) {
        val selectedSlot = _uiState.value.selectedSlot
        if (selectedSlot == null) {
            viewModelScope.launch {
                _events.emit(ServiceBookingEvent.ShowError("请选择服务时段"))
            }
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            // 构建预约时间
            val appointmentTime = combineDateTime(selectedSlot.date, selectedSlot.startTime)
            
            val booking = ServiceBooking(
                id = "", // 服务器生成
                serviceId = "service_${System.currentTimeMillis()}",
                serviceName = getServiceName(),
                serviceType = getSelectedServiceType(),
                customerId = "customer_123", // 从用户信息获取
                customerName = customerName,
                customerPhone = customerPhone,
                customerAddress = customerAddress,
                appointmentTime = appointmentTime,
                estimatedDuration = 120, // 默认2小时
                price = selectedSlot.price,
                urgentFee = if (isUrgentBooking()) selectedSlot.urgentPrice?.minus(selectedSlot.price) ?: 0.0 else 0.0,
                totalPrice = selectedSlot.urgentPrice ?: selectedSlot.price,
                status = BookingStatus.PENDING,
                merchantStatus = MerchantStatus.AVAILABLE,
                notes = notes,
                createdAt = Date(),
                updatedAt = Date(),
                cancellationDeadline = Date(appointmentTime.time - 2 * 60 * 60 * 1000), // 提前2小时
                isUrgent = isUrgentBooking()
            )

            when (val result = bookingRepository.createBooking(booking)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _events.emit(ServiceBookingEvent.BookingCreated(result.data))
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _events.emit(ServiceBookingEvent.ShowError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    fun createUrgentBooking(
        customerLocation: String,
        urgentReason: UrgentReason,
        description: String,
        priority: UrgentPriority
    ) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            val urgentRequest = UrgentServiceRequest(
                id = "urgent_${System.currentTimeMillis()}",
                serviceType = getSelectedServiceType(),
                customerLocation = customerLocation,
                urgentReason = urgentReason,
                additionalFee = calculateUrgentFee(priority),
                requestTime = Date(),
                requiredTime = Date(System.currentTimeMillis() + 2 * 60 * 60 * 1000), // 2小时内
                priority = priority,
                description = description
            )

            when (val result = bookingRepository.createUrgentBooking(urgentRequest)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _events.emit(ServiceBookingEvent.UrgentBookingCreated(result.data))
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _events.emit(ServiceBookingEvent.ShowError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    private fun combineDateTime(date: Date, timeString: String): Date {
        val calendar = Calendar.getInstance()
        calendar.time = date
        
        val timeParts = timeString.split(":")
        if (timeParts.size >= 2) {
            calendar.set(Calendar.HOUR_OF_DAY, timeParts[0].toInt())
            calendar.set(Calendar.MINUTE, timeParts[1].toInt())
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
        }
        
        return calendar.time
    }

    private fun getServiceName(): String {
        return when (getSelectedServiceType()) {
            ServiceType.HOME_SERVICE -> "上门洗护服务"
            ServiceType.STORE_SERVICE -> "到店洗护服务"
            ServiceType.URGENT_SERVICE -> "紧急洗护服务"
            else -> "洗护服务"
        }
    }

    private fun getSelectedServiceType(): ServiceType {
        // 这里应该从UI状态获取，暂时返回默认值
        return ServiceType.HOME_SERVICE
    }

    private fun isUrgentBooking(): Boolean {
        // 这里应该从UI状态获取，暂时返回false
        return false
    }

    private fun calculateUrgentFee(priority: UrgentPriority): Double {
        return when (priority) {
            UrgentPriority.LOW -> 20.0
            UrgentPriority.MEDIUM -> 50.0
            UrgentPriority.HIGH -> 100.0
            UrgentPriority.CRITICAL -> 200.0
        }
    }
}

// UI状态数据类
data class ServiceBookingUiState(
    val isLoading: Boolean = false,
    val availableSlots: List<ServiceSlot> = emptyList(),
    val merchantAvailability: List<MerchantAvailability> = emptyList(),
    val selectedSlot: ServiceSlot? = null,
    val selectedMerchant: MerchantAvailability? = null,
    val error: String? = null
)

// 事件数据类
sealed class ServiceBookingEvent {
    data class ShowError(val message: String) : ServiceBookingEvent()
    data class ShowSuccess(val message: String) : ServiceBookingEvent()
    data class BookingCreated(val booking: ServiceBooking) : ServiceBookingEvent()
    data class UrgentBookingCreated(val booking: ServiceBooking) : ServiceBookingEvent()
}
