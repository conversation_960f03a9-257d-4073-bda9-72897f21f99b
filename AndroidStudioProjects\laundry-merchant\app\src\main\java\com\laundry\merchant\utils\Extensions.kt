package com.laundry.merchant.utils

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.fragment.app.Fragment
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

// Activity扩展函数
fun Activity.showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(this, message, duration).show()
}

fun Activity.hideKeyboard() {
    val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    val view = currentFocus ?: View(this)
    imm.hideSoftInputFromWindow(view.windowToken, 0)
}

fun Activity.showKeyboard(view: View) {
    val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    view.requestFocus()
    imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
}

// Fragment扩展函数
fun Fragment.showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(requireContext(), message, duration).show()
}

fun Fragment.hideKeyboard() {
    val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    val view = activity?.currentFocus ?: View(requireContext())
    imm.hideSoftInputFromWindow(view.windowToken, 0)
}

// View扩展函数
fun View.visible() {
    visibility = View.VISIBLE
}

fun View.invisible() {
    visibility = View.INVISIBLE
}

fun View.gone() {
    visibility = View.GONE
}

fun View.visibleIf(condition: Boolean) {
    visibility = if (condition) View.VISIBLE else View.GONE
}

fun View.invisibleIf(condition: Boolean) {
    visibility = if (condition) View.INVISIBLE else View.VISIBLE
}

// String扩展函数
fun String.isValidPhone(): Boolean {
    return this.matches(Regex("^1[3-9]\\d{9}$"))
}

fun String.isValidEmail(): Boolean {
    return android.util.Patterns.EMAIL_ADDRESS.matcher(this).matches()
}

fun String.maskPhone(): String {
    return if (this.length == 11) {
        "${this.substring(0, 3)}****${this.substring(7)}"
    } else {
        this
    }
}

fun String.toSafeInt(defaultValue: Int = 0): Int {
    return try {
        this.toInt()
    } catch (e: NumberFormatException) {
        defaultValue
    }
}

fun String.toSafeDouble(defaultValue: Double = 0.0): Double {
    return try {
        this.toDouble()
    } catch (e: NumberFormatException) {
        defaultValue
    }
}

// Double扩展函数
fun Double.formatCurrency(): String {
    val formatter = DecimalFormat("#,##0.00")
    return "¥${formatter.format(this)}"
}

fun Double.formatPercentage(): String {
    val formatter = DecimalFormat("#0.0")
    return "${formatter.format(this)}%"
}

fun Double.formatNumber(): String {
    val formatter = DecimalFormat("#,##0")
    return formatter.format(this)
}

// Int扩展函数
fun Int.formatCount(): String {
    return when {
        this >= 10000 -> "${(this / 10000.0).formatDecimal(1)}万"
        this >= 1000 -> "${(this / 1000.0).formatDecimal(1)}k"
        else -> this.toString()
    }
}

private fun Double.formatDecimal(digits: Int): String {
    val formatter = DecimalFormat("#.${"#".repeat(digits)}")
    return formatter.format(this)
}

// Date扩展函数
fun Date.formatTime(): String {
    val formatter = SimpleDateFormat("HH:mm", Locale.getDefault())
    return formatter.format(this)
}

fun Date.formatDate(): String {
    val formatter = SimpleDateFormat("MM-dd", Locale.getDefault())
    return formatter.format(this)
}

fun Date.formatDateTime(): String {
    val formatter = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
    return formatter.format(this)
}

fun Date.formatFullDateTime(): String {
    val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    return formatter.format(this)
}

fun Date.isToday(): Boolean {
    val today = Date()
    val todayFormatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    return todayFormatter.format(this) == todayFormatter.format(today)
}

fun Date.isYesterday(): Boolean {
    val yesterday = Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000)
    val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    return formatter.format(this) == formatter.format(yesterday)
}

fun Date.getRelativeTimeString(): String {
    val now = System.currentTimeMillis()
    val time = this.time
    val diff = now - time

    return when {
        diff < 60 * 1000 -> "刚刚"
        diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
        diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
        diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
        else -> this.formatDate()
    }
}

// List扩展函数
fun <T> List<T>.safeGet(index: Int): T? {
    return if (index >= 0 && index < size) this[index] else null
}

fun <T> MutableList<T>.addIfNotExists(item: T) {
    if (!contains(item)) {
        add(item)
    }
}

fun <T> MutableList<T>.removeIfExists(item: T) {
    if (contains(item)) {
        remove(item)
    }
}

// 其他工具函数
fun getCurrentTimeString(): String {
    val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    return formatter.format(Date())
}

fun generateRandomId(): String {
    return "id_${System.currentTimeMillis()}_${(1000..9999).random()}"
}

fun isNetworkUrl(url: String?): Boolean {
    return url?.startsWith("http://") == true || url?.startsWith("https://") == true
}

fun getFileExtension(fileName: String): String {
    return fileName.substringAfterLast('.', "")
}

fun formatFileSize(bytes: Long): String {
    val kb = bytes / 1024.0
    val mb = kb / 1024.0
    val gb = mb / 1024.0

    return when {
        gb >= 1 -> "${gb.formatDecimal(2)} GB"
        mb >= 1 -> "${mb.formatDecimal(2)} MB"
        kb >= 1 -> "${kb.formatDecimal(2)} KB"
        else -> "$bytes B"
    }
}
