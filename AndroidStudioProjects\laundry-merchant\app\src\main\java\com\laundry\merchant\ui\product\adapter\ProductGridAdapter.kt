package com.laundry.merchant.ui.product.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.data.model.Product
import com.laundry.merchant.data.model.StockStatus
import com.laundry.merchant.databinding.ItemProductGridBinding
import com.laundry.merchant.utils.formatCurrency

class ProductGridAdapter(
    private val onProductClick: (Product) -> Unit,
    private val onAddToCartClick: (Product) -> Unit
) : ListAdapter<Product, ProductGridAdapter.ViewHolder>(ProductDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemProductGridBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<Product>) {
        submitList(newData)
    }

    inner class ViewHolder(private val binding: ItemProductGridBinding) : 
        RecyclerView.ViewHolder(binding.root) {

        fun bind(product: Product) {
            // 设置产品信息
            binding.textViewProductName.text = product.name
            binding.textViewCurrentPrice.text = product.currentPrice.formatCurrency()
            
            // 设置原价和折扣
            if (product.hasDiscount()) {
                binding.textViewOriginalPrice.visibility = View.VISIBLE
                binding.textViewOriginalPrice.text = product.originalPrice.formatCurrency()
                binding.textViewDiscount.visibility = View.VISIBLE
                binding.textViewDiscount.text = "${product.discountPercentage}%OFF"
            } else {
                binding.textViewOriginalPrice.visibility = View.GONE
                binding.textViewDiscount.visibility = View.GONE
            }

            // 设置评分和销量
            binding.ratingBar.rating = product.rating
            binding.textViewSalesCount.text = "已售${product.salesCount}"

            // 设置标签
            binding.textViewHotTag.visibility = if (product.isHot) View.VISIBLE else View.GONE
            binding.textViewNewTag.visibility = if (product.isNew) View.VISIBLE else View.GONE

            // 设置库存状态
            when (product.getStockStatus()) {
                StockStatus.OUT_OF_STOCK -> {
                    binding.textViewStockStatus.visibility = View.VISIBLE
                    binding.textViewStockStatus.text = "缺货"
                    binding.textViewStockStatus.setBackgroundResource(R.color.red_500)
                    binding.buttonAddToCart.isEnabled = false
                    binding.buttonAddToCart.text = "缺货"
                }
                StockStatus.LOW_STOCK -> {
                    binding.textViewStockStatus.visibility = View.VISIBLE
                    binding.textViewStockStatus.text = "仅剩${product.stock}件"
                    binding.textViewStockStatus.setBackgroundResource(R.color.orange_500)
                    binding.buttonAddToCart.isEnabled = true
                    binding.buttonAddToCart.text = "加购物车"
                }
                StockStatus.IN_STOCK -> {
                    binding.textViewStockStatus.visibility = View.GONE
                    binding.buttonAddToCart.isEnabled = true
                    binding.buttonAddToCart.text = "加购物车"
                }
            }

            // 设置服务类型标识
            when (product.serviceType) {
                com.laundry.merchant.data.model.ServiceType.HOME_SERVICE -> {
                    binding.textViewServiceType.visibility = View.VISIBLE
                    binding.textViewServiceType.text = "上门"
                    binding.textViewServiceType.setBackgroundResource(R.color.blue_500)
                }
                com.laundry.merchant.data.model.ServiceType.URGENT_SERVICE -> {
                    binding.textViewServiceType.visibility = View.VISIBLE
                    binding.textViewServiceType.text = "急单"
                    binding.textViewServiceType.setBackgroundResource(R.color.red_500)
                }
                else -> {
                    binding.textViewServiceType.visibility = View.GONE
                }
            }

            // TODO: 加载产品图片
            // Glide.with(binding.imageViewProduct.context)
            //     .load(product.thumbnailUrl)
            //     .placeholder(R.drawable.placeholder_product)
            //     .into(binding.imageViewProduct)

            // 设置点击事件
            binding.root.setOnClickListener {
                onProductClick(product)
            }

            binding.buttonAddToCart.setOnClickListener {
                if (product.getStockStatus() != StockStatus.OUT_OF_STOCK) {
                    onAddToCartClick(product)
                }
            }
        }
    }

    private class ProductDiffCallback : DiffUtil.ItemCallback<Product>() {
        override fun areItemsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem == newItem
        }
    }
}
