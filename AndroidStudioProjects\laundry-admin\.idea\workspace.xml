<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="203fe2ec-052d-44aa-82af-c37ebdf0798a" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="2yOr4s0X2mPpc1nFUbLzX6QEWNn" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/AndroidStudioProjects/laundry-admin",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SwiftWorkspaceSettings">
    <option name="detectedToolchain" value="true" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="203fe2ec-052d-44aa-82af-c37ebdf0798a" name="Changes" comment="" />
      <created>1749716272732</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749716272732</updated>
    </task>
    <servers />
  </component>
</project>