<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗护帮 - 投流数据分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .dashboard-title {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .dashboard-subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .metric-change {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: bold;
        }
        
        .metric-change.positive {
            background: #e8f5e8;
            color: #4caf50;
        }
        
        .metric-change.negative {
            background: #ffeaea;
            color: #f44336;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .ranking-table {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .table-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .rank-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .promotion-badge {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active {
            background: #e8f5e8;
            color: #4caf50;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-paused {
            background: #fff3cd;
            color: #856404;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .real-time-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background: #e8f5e8;
            color: #4caf50;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .pulse {
            width: 8px;
            height: 8px;
            background: #4caf50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">🧺 洗护帮 - 投流数据中心</div>
            <div style="display: flex; gap: 20px;">
                <a href="index.html" style="color: white; text-decoration: none;">首页</a>
                <a href="features.html" style="color: white; text-decoration: none;">功能演示</a>
            </div>
        </div>
    </nav>
    
    <div class="container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">📊 投流数据分析</h1>
            <p class="dashboard-subtitle">实时监控投流效果，优化竞价策略</p>
            <div class="real-time-indicator">
                <div class="pulse"></div>
                实时数据更新中
            </div>
        </div>
        
        <!-- 核心指标 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-icon">💰</div>
                <div class="metric-value" id="totalRevenue">¥125,680</div>
                <div class="metric-label">总投流收入</div>
                <div class="metric-change positive">+12.5% 较上月</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">🏪</div>
                <div class="metric-value" id="activeMerchants">1,245</div>
                <div class="metric-label">活跃投流商家</div>
                <div class="metric-change positive">+8.3% 较上月</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">🎯</div>
                <div class="metric-value" id="avgCPC">¥1.85</div>
                <div class="metric-label">平均点击成本</div>
                <div class="metric-change negative">-5.2% 较上月</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-icon">📈</div>
                <div class="metric-value" id="conversionRate">12.5%</div>
                <div class="metric-label">平均转化率</div>
                <div class="metric-change positive">+3.1% 较上月</div>
            </div>
        </div>
        
        <!-- 图表区域 -->
        <div class="charts-grid">
            <div class="chart-card">
                <h3 class="chart-title">📈 投流收入趋势</h3>
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <h3 class="chart-title">🎯 点击率分析</h3>
                <div class="chart-container">
                    <canvas id="ctrChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <h3 class="chart-title">🏆 分类投流分布</h3>
                <div class="chart-container">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <h3 class="chart-title">⏰ 投流时段分析</h3>
                <div class="chart-container">
                    <canvas id="hourlyChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 商家排行榜 -->
        <div class="ranking-table">
            <h3 class="table-title">🏆 投流效果排行榜</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>商家名称</th>
                        <th>投流状态</th>
                        <th>本月投入</th>
                        <th>点击次数</th>
                        <th>转化率</th>
                        <th>ROI</th>
                    </tr>
                </thead>
                <tbody id="rankingTableBody">
                    <tr>
                        <td><span class="rank-badge">1</span></td>
                        <td>优质洗衣店 <span class="promotion-badge">VIP</span></td>
                        <td><span class="status-active">活跃</span></td>
                        <td>¥8,560</td>
                        <td>2,340</td>
                        <td>15.8%</td>
                        <td>4.2</td>
                    </tr>
                    <tr>
                        <td><span class="rank-badge">2</span></td>
                        <td>专业干洗店 <span class="promotion-badge">高级</span></td>
                        <td><span class="status-active">活跃</span></td>
                        <td>¥6,890</td>
                        <td>1,980</td>
                        <td>13.2%</td>
                        <td>3.8</td>
                    </tr>
                    <tr>
                        <td><span class="rank-badge">3</span></td>
                        <td>快速洗护</td>
                        <td><span class="status-paused">暂停</span></td>
                        <td>¥5,420</td>
                        <td>1,650</td>
                        <td>11.5%</td>
                        <td>3.1</td>
                    </tr>
                    <tr>
                        <td><span class="rank-badge">4</span></td>
                        <td>精品洗衣</td>
                        <td><span class="status-active">活跃</span></td>
                        <td>¥4,780</td>
                        <td>1,420</td>
                        <td>10.8%</td>
                        <td>2.9</td>
                    </tr>
                    <tr>
                        <td><span class="rank-badge">5</span></td>
                        <td>便民洗护</td>
                        <td><span class="status-active">活跃</span></td>
                        <td>¥3,960</td>
                        <td>1,280</td>
                        <td>9.6%</td>
                        <td>2.5</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        // 投流收入趋势图
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '投流收入',
                    data: [85000, 92000, 98000, 105000, 118000, 125680],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + (value / 1000) + 'k';
                            }
                        }
                    }
                }
            }
        });
        
        // 点击率分析图
        const ctrCtx = document.getElementById('ctrChart').getContext('2d');
        const ctrChart = new Chart(ctrCtx, {
            type: 'bar',
            data: {
                labels: ['衣物洗护', '鞋类清洗', '萌宠洗护', '包包清洗', '床品洗护'],
                datasets: [{
                    label: '点击率 (%)',
                    data: [8.5, 6.2, 12.3, 4.8, 7.1],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
        
        // 分类投流分布图
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryChart = new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: ['衣物洗护', '鞋类清洗', '萌宠洗护', '包包清洗', '其他'],
                datasets: [{
                    data: [35, 25, 20, 12, 8],
                    backgroundColor: [
                        '#667eea',
                        '#764ba2',
                        '#f093fb',
                        '#f5576c',
                        '#4facfe'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // 投流时段分析图
        const hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
        const hourlyChart = new Chart(hourlyCtx, {
            type: 'line',
            data: {
                labels: ['6:00', '9:00', '12:00', '15:00', '18:00', '21:00', '24:00'],
                datasets: [{
                    label: '投流活跃度',
                    data: [20, 45, 80, 65, 90, 75, 35],
                    borderColor: '#f5576c',
                    backgroundColor: 'rgba(245, 87, 108, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // 实时数据更新
        function updateRealTimeData() {
            // 更新核心指标
            const totalRevenue = document.getElementById('totalRevenue');
            const currentValue = parseInt(totalRevenue.textContent.replace(/[¥,]/g, ''));
            const newValue = currentValue + Math.floor(Math.random() * 100);
            totalRevenue.textContent = '¥' + newValue.toLocaleString();
            
            // 更新活跃商家数
            const activeMerchants = document.getElementById('activeMerchants');
            const merchantCount = parseInt(activeMerchants.textContent.replace(/,/g, ''));
            if (Math.random() < 0.3) {
                activeMerchants.textContent = (merchantCount + Math.floor(Math.random() * 3)).toLocaleString();
            }
            
            // 更新平均CPC
            const avgCPC = document.getElementById('avgCPC');
            const cpcValue = parseFloat(avgCPC.textContent.replace('¥', ''));
            const newCPC = (cpcValue + (Math.random() - 0.5) * 0.1).toFixed(2);
            avgCPC.textContent = '¥' + newCPC;
            
            // 更新转化率
            const conversionRate = document.getElementById('conversionRate');
            const rateValue = parseFloat(conversionRate.textContent.replace('%', ''));
            const newRate = (rateValue + (Math.random() - 0.5) * 0.5).toFixed(1);
            conversionRate.textContent = newRate + '%';
        }
        
        // 每5秒更新一次数据
        setInterval(updateRealTimeData, 5000);
        
        // 模拟排行榜数据变化
        function updateRankingTable() {
            const tbody = document.getElementById('rankingTableBody');
            const rows = tbody.querySelectorAll('tr');
            
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 7) {
                    // 更新点击次数
                    const clicksCell = cells[4];
                    const currentClicks = parseInt(clicksCell.textContent.replace(/,/g, ''));
                    const newClicks = currentClicks + Math.floor(Math.random() * 10);
                    clicksCell.textContent = newClicks.toLocaleString();
                    
                    // 更新转化率
                    const conversionCell = cells[5];
                    const currentConversion = parseFloat(conversionCell.textContent.replace('%', ''));
                    const newConversion = (currentConversion + (Math.random() - 0.5) * 0.2).toFixed(1);
                    conversionCell.textContent = newConversion + '%';
                    
                    // 更新ROI
                    const roiCell = cells[6];
                    const currentROI = parseFloat(roiCell.textContent);
                    const newROI = (currentROI + (Math.random() - 0.5) * 0.1).toFixed(1);
                    roiCell.textContent = newROI;
                }
            });
        }
        
        // 每10秒更新一次排行榜
        setInterval(updateRankingTable, 10000);
    </script>
</body>
</html>
