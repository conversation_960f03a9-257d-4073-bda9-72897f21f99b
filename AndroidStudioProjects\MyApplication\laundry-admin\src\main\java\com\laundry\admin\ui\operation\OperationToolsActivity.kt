package com.laundry.admin.ui.operation

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.tabs.TabLayout
import com.laundry.admin.R
import com.laundry.admin.databinding.ActivityOperationToolsBinding
import com.laundry.admin.ui.operation.adapter.CampaignAdapter
import com.laundry.admin.ui.operation.adapter.NotificationAdapter
import com.laundry.admin.ui.operation.adapter.ABTestAdapter
import com.laundry.admin.ui.operation.adapter.ContentAdapter
import com.laundry.admin.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class OperationToolsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityOperationToolsBinding
    private val viewModel: OperationToolsViewModel by viewModels()
    
    private lateinit var campaignAdapter: CampaignAdapter
    private lateinit var notificationAdapter: NotificationAdapter
    private lateinit var abTestAdapter: ABTestAdapter
    private lateinit var contentAdapter: ContentAdapter
    private var currentTab = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOperationToolsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerViews()
        setupTabs()
        observeViewModel()
        
        // 加载运营数据
        viewModel.loadCampaigns()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "运营工具"

        // 设置刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            refreshData()
        }

        // 设置快捷操作按钮
        binding.fabCreateCampaign.setOnClickListener {
            when (currentTab) {
                0 -> showCreateCampaignDialog()
                1 -> showCreateNotificationDialog()
                2 -> showCreateABTestDialog()
                3 -> showCreateContentDialog()
            }
        }
    }

    private fun setupRecyclerViews() {
        // 活动管理
        campaignAdapter = CampaignAdapter(
            onCampaignClick = { campaign ->
                showCampaignDetailDialog(campaign)
            },
            onCampaignEdit = { campaign ->
                showEditCampaignDialog(campaign)
            },
            onCampaignToggle = { campaign, enabled ->
                viewModel.toggleCampaign(campaign.id, enabled)
            },
            onViewAnalytics = { campaign ->
                showCampaignAnalyticsDialog(campaign)
            }
        )
        
        binding.recyclerViewCampaigns.apply {
            layoutManager = LinearLayoutManager(this@OperationToolsActivity)
            adapter = campaignAdapter
        }

        // 推送通知
        notificationAdapter = NotificationAdapter(
            onNotificationClick = { notification ->
                showNotificationDetailDialog(notification)
            },
            onSendNow = { notification ->
                showSendNotificationDialog(notification)
            },
            onSchedule = { notification ->
                showScheduleNotificationDialog(notification)
            }
        )
        
        binding.recyclerViewNotifications.apply {
            layoutManager = LinearLayoutManager(this@OperationToolsActivity)
            adapter = notificationAdapter
        }

        // A/B测试
        abTestAdapter = ABTestAdapter(
            onTestClick = { test ->
                showABTestDetailDialog(test)
            },
            onTestControl = { test, action ->
                handleABTestAction(test, action)
            },
            onViewResults = { test ->
                showABTestResultsDialog(test)
            }
        )
        
        binding.recyclerViewABTests.apply {
            layoutManager = LinearLayoutManager(this@OperationToolsActivity)
            adapter = abTestAdapter
        }

        // 内容管理
        contentAdapter = ContentAdapter(
            onContentClick = { content ->
                showContentDetailDialog(content)
            },
            onContentEdit = { content ->
                showEditContentDialog(content)
            },
            onContentPublish = { content ->
                viewModel.publishContent(content.id)
            }
        )
        
        binding.recyclerViewContent.apply {
            layoutManager = GridLayoutManager(this@OperationToolsActivity, 2)
            adapter = contentAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("活动管理"))
            addTab(newTab().setText("推送通知"))
            addTab(newTab().setText("A/B测试"))
            addTab(newTab().setText("内容管理"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    currentTab = tab?.position ?: 0
                    updateTabContent(currentTab)
                    updateFabIcon(currentTab)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun updateTabContent(tabIndex: Int) {
        // 隐藏所有内容
        binding.layoutCampaigns.visibility = View.GONE
        binding.layoutNotifications.visibility = View.GONE
        binding.layoutABTests.visibility = View.GONE
        binding.layoutContent.visibility = View.GONE

        when (tabIndex) {
            0 -> {
                binding.layoutCampaigns.visibility = View.VISIBLE
                viewModel.loadCampaigns()
            }
            1 -> {
                binding.layoutNotifications.visibility = View.VISIBLE
                viewModel.loadNotifications()
            }
            2 -> {
                binding.layoutABTests.visibility = View.VISIBLE
                viewModel.loadABTests()
            }
            3 -> {
                binding.layoutContent.visibility = View.VISIBLE
                viewModel.loadContent()
            }
        }
    }

    private fun updateFabIcon(tabIndex: Int) {
        val iconRes = when (tabIndex) {
            0 -> R.drawable.ic_campaign
            1 -> R.drawable.ic_notification
            2 -> R.drawable.ic_ab_test
            3 -> R.drawable.ic_content
            else -> R.drawable.ic_add
        }
        binding.fabCreateCampaign.setImageResource(iconRes)
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_operation_tools, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_analytics -> {
                showOperationAnalyticsDialog()
                true
            }
            R.id.action_templates -> {
                showTemplatesDialog()
                true
            }
            R.id.action_automation -> {
                showAutomationDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: OperationToolsUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新活动列表
        campaignAdapter.updateData(state.campaigns)

        // 更新通知列表
        notificationAdapter.updateData(state.notifications)

        // 更新A/B测试列表
        abTestAdapter.updateData(state.abTests)

        // 更新内容列表
        contentAdapter.updateData(state.content)

        // 更新统计数据
        state.operationStats?.let { stats ->
            updateOperationStats(stats)
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun updateOperationStats(stats: OperationStats) {
        binding.textViewActiveCampaigns.text = "${stats.activeCampaigns}"
        binding.textViewTotalReach.text = "${stats.totalReach}"
        binding.textViewConversionRate.text = "${String.format("%.2f", stats.conversionRate)}%"
        binding.textViewEngagementRate.text = "${String.format("%.2f", stats.engagementRate)}%"
        
        binding.textViewPendingNotifications.text = "${stats.pendingNotifications}"
        binding.textViewSentNotifications.text = "${stats.sentNotifications}"
        binding.textViewNotificationOpenRate.text = "${String.format("%.2f", stats.notificationOpenRate)}%"
        
        binding.textViewRunningTests.text = "${stats.runningABTests}"
        binding.textViewCompletedTests.text = "${stats.completedABTests}"
        
        binding.textViewPublishedContent.text = "${stats.publishedContent}"
        binding.textViewDraftContent.text = "${stats.draftContent}"
    }

    private fun handleEvent(event: OperationToolsEvent) {
        when (event) {
            is OperationToolsEvent.ShowError -> {
                showError(event.message)
            }
            is OperationToolsEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is OperationToolsEvent.CampaignCreated -> {
                showToast("活动已创建")
                refreshData()
            }
            is OperationToolsEvent.NotificationSent -> {
                showToast("通知已发送")
                refreshData()
            }
            is OperationToolsEvent.ABTestStarted -> {
                showToast("A/B测试已开始")
                refreshData()
            }
            is OperationToolsEvent.ContentPublished -> {
                showToast("内容已发布")
                refreshData()
            }
        }
    }

    // 活动管理相关方法
    private fun showCreateCampaignDialog() {
        val dialog = CreateCampaignDialog.newInstance { campaign ->
            viewModel.createCampaign(campaign)
        }
        dialog.show(supportFragmentManager, "CreateCampaignDialog")
    }

    private fun showCampaignDetailDialog(campaign: Campaign) {
        val dialog = CampaignDetailDialog.newInstance(campaign)
        dialog.show(supportFragmentManager, "CampaignDetailDialog")
    }

    private fun showEditCampaignDialog(campaign: Campaign) {
        val dialog = EditCampaignDialog.newInstance(campaign) { updatedCampaign ->
            viewModel.updateCampaign(updatedCampaign)
        }
        dialog.show(supportFragmentManager, "EditCampaignDialog")
    }

    private fun showCampaignAnalyticsDialog(campaign: Campaign) {
        val dialog = CampaignAnalyticsDialog.newInstance(campaign)
        dialog.show(supportFragmentManager, "CampaignAnalyticsDialog")
    }

    // 推送通知相关方法
    private fun showCreateNotificationDialog() {
        val dialog = CreateNotificationDialog.newInstance { notification ->
            viewModel.createNotification(notification)
        }
        dialog.show(supportFragmentManager, "CreateNotificationDialog")
    }

    private fun showNotificationDetailDialog(notification: PushNotification) {
        val dialog = NotificationDetailDialog.newInstance(notification)
        dialog.show(supportFragmentManager, "NotificationDetailDialog")
    }

    private fun showSendNotificationDialog(notification: PushNotification) {
        MaterialAlertDialogBuilder(this)
            .setTitle("发送通知")
            .setMessage("确定要立即发送通知 \"${notification.title}\" 吗？")
            .setPositiveButton("发送") { _, _ ->
                viewModel.sendNotification(notification.id)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showScheduleNotificationDialog(notification: PushNotification) {
        val dialog = ScheduleNotificationDialog.newInstance(notification) { scheduleTime ->
            viewModel.scheduleNotification(notification.id, scheduleTime)
        }
        dialog.show(supportFragmentManager, "ScheduleNotificationDialog")
    }

    // A/B测试相关方法
    private fun showCreateABTestDialog() {
        val dialog = CreateABTestDialog.newInstance { abTest ->
            viewModel.createABTest(abTest)
        }
        dialog.show(supportFragmentManager, "CreateABTestDialog")
    }

    private fun showABTestDetailDialog(test: ABTest) {
        val dialog = ABTestDetailDialog.newInstance(test)
        dialog.show(supportFragmentManager, "ABTestDetailDialog")
    }

    private fun showABTestResultsDialog(test: ABTest) {
        val dialog = ABTestResultsDialog.newInstance(test)
        dialog.show(supportFragmentManager, "ABTestResultsDialog")
    }

    private fun handleABTestAction(test: ABTest, action: String) {
        when (action) {
            "start" -> {
                MaterialAlertDialogBuilder(this)
                    .setTitle("开始测试")
                    .setMessage("确定要开始A/B测试 \"${test.name}\" 吗？")
                    .setPositiveButton("开始") { _, _ ->
                        viewModel.startABTest(test.id)
                    }
                    .setNegativeButton("取消", null)
                    .show()
            }
            "pause" -> {
                viewModel.pauseABTest(test.id)
            }
            "stop" -> {
                MaterialAlertDialogBuilder(this)
                    .setTitle("停止测试")
                    .setMessage("确定要停止A/B测试 \"${test.name}\" 吗？")
                    .setPositiveButton("停止") { _, _ ->
                        viewModel.stopABTest(test.id)
                    }
                    .setNegativeButton("取消", null)
                    .show()
            }
        }
    }

    // 内容管理相关方法
    private fun showCreateContentDialog() {
        val dialog = CreateContentDialog.newInstance { content ->
            viewModel.createContent(content)
        }
        dialog.show(supportFragmentManager, "CreateContentDialog")
    }

    private fun showContentDetailDialog(content: Content) {
        val dialog = ContentDetailDialog.newInstance(content)
        dialog.show(supportFragmentManager, "ContentDetailDialog")
    }

    private fun showEditContentDialog(content: Content) {
        val dialog = EditContentDialog.newInstance(content) { updatedContent ->
            viewModel.updateContent(updatedContent)
        }
        dialog.show(supportFragmentManager, "EditContentDialog")
    }

    // 其他功能方法
    private fun showOperationAnalyticsDialog() {
        val dialog = OperationAnalyticsDialog.newInstance()
        dialog.show(supportFragmentManager, "OperationAnalyticsDialog")
    }

    private fun showTemplatesDialog() {
        val dialog = TemplatesDialog.newInstance()
        dialog.show(supportFragmentManager, "TemplatesDialog")
    }

    private fun showAutomationDialog() {
        val dialog = AutomationDialog.newInstance()
        dialog.show(supportFragmentManager, "AutomationDialog")
    }

    private fun refreshData() {
        when (currentTab) {
            0 -> viewModel.loadCampaigns()
            1 -> viewModel.loadNotifications()
            2 -> viewModel.loadABTests()
            3 -> viewModel.loadContent()
        }
        viewModel.loadOperationStats()
    }

    private fun showError(message: String) {
        showToast(message)
    }
}
