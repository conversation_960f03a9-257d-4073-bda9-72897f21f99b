package com.laundry.merchant.ui.promotion.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.Switch
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.ui.promotion.PromotionKeyword

class PromotionKeywordAdapter(
    private val onKeywordClick: (PromotionKeyword) -> Unit,
    private val onToggleKeyword: (PromotionKeyword) -> Unit,
    private val onAdjustBid: (PromotionKeyword, Double) -> Unit
) : ListAdapter<PromotionKeyword, PromotionKeywordAdapter.ViewHolder>(KeywordDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_promotion_keyword, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<PromotionKeyword>) {
        submitList(newData)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val keywordTextView: TextView = itemView.findViewById(R.id.textViewKeyword)
        private val bidTextView: TextView = itemView.findViewById(R.id.textViewBid)
        private val qualityTextView: TextView = itemView.findViewById(R.id.textViewQuality)
        private val statusTextView: TextView = itemView.findViewById(R.id.textViewStatus)
        private val clicksTextView: TextView = itemView.findViewById(R.id.textViewClicks)
        private val impressionsTextView: TextView = itemView.findViewById(R.id.textViewImpressions)
        private val positionTextView: TextView = itemView.findViewById(R.id.textViewPosition)
        private val costTextView: TextView = itemView.findViewById(R.id.textViewCost)
        private val statusSwitch: Switch = itemView.findViewById(R.id.switchStatus)
        private val increaseBidButton: Button = itemView.findViewById(R.id.buttonIncreaseBid)
        private val decreaseBidButton: Button = itemView.findViewById(R.id.buttonDecreaseBid)

        fun bind(keyword: PromotionKeyword) {
            keywordTextView.text = keyword.keyword
            bidTextView.text = "出价: ¥${String.format("%.2f", keyword.bid)}"
            clicksTextView.text = "点击: ${keyword.clicks}"
            impressionsTextView.text = "展现: ${keyword.impressions}"
            costTextView.text = "消费: ¥${String.format("%.2f", keyword.cost)}"
            
            // 设置质量分
            qualityTextView.text = "质量分: ${keyword.quality}"
            val qualityColor = when {
                keyword.quality >= 8 -> R.color.green_500
                keyword.quality >= 6 -> R.color.orange_500
                else -> R.color.red_500
            }
            qualityTextView.setTextColor(
                ContextCompat.getColor(itemView.context, qualityColor)
            )
            
            // 设置平均排名
            if (keyword.averagePosition > 0) {
                positionTextView.text = "排名: ${String.format("%.1f", keyword.averagePosition)}"
                positionTextView.visibility = View.VISIBLE
            } else {
                positionTextView.visibility = View.GONE
            }
            
            // 设置状态
            statusTextView.text = when (keyword.status) {
                "active" -> "投放中"
                "paused" -> "已暂停"
                else -> "异常"
            }
            
            val statusColor = when (keyword.status) {
                "active" -> R.color.green_500
                "paused" -> R.color.orange_500
                else -> R.color.gray_500
            }
            statusTextView.setTextColor(
                ContextCompat.getColor(itemView.context, statusColor)
            )
            
            // 设置开关状态
            statusSwitch.isChecked = keyword.status == "active"
            statusSwitch.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked != (keyword.status == "active")) {
                    onToggleKeyword(keyword)
                }
            }
            
            // 设置出价调整按钮
            increaseBidButton.setOnClickListener {
                val newBid = (keyword.bid + 0.1).coerceAtMost(10.0)
                onAdjustBid(keyword, newBid)
            }
            
            decreaseBidButton.setOnClickListener {
                val newBid = (keyword.bid - 0.1).coerceAtLeast(0.1)
                onAdjustBid(keyword, newBid)
            }
            
            itemView.setOnClickListener {
                onKeywordClick(keyword)
            }
        }
    }

    private class KeywordDiffCallback : DiffUtil.ItemCallback<PromotionKeyword>() {
        override fun areItemsTheSame(oldItem: PromotionKeyword, newItem: PromotionKeyword): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: PromotionKeyword, newItem: PromotionKeyword): Boolean {
            return oldItem == newItem
        }
    }
}
