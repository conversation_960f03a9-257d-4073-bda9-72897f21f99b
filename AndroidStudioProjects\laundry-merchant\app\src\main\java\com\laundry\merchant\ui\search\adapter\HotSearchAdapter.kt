package com.laundry.merchant.ui.search.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.data.model.HotSearchKeyword
import com.laundry.merchant.data.model.SearchTrend
import com.laundry.merchant.databinding.ItemHotSearchBinding

class HotSearchAdapter(
    private val onKeywordClick: (HotSearchKeyword) -> Unit
) : ListAdapter<HotSearchKeyword, HotSearchAdapter.ViewHolder>(HotSearchDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemHotSearchBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<HotSearchKeyword>) {
        submitList(newData)
    }

    inner class ViewHolder(private val binding: ItemHotSearchBinding) : 
        RecyclerView.ViewHolder(binding.root) {

        fun bind(keyword: HotSearchKeyword) {
            binding.textViewKeyword.text = keyword.keyword
            binding.textViewSearchCount.text = "${keyword.searchCount}"

            // 设置趋势图标
            when (keyword.trend) {
                SearchTrend.UP -> {
                    binding.imageViewTrend.setImageResource(R.drawable.ic_trending_up)
                    binding.imageViewTrend.setColorFilter(
                        ContextCompat.getColor(binding.root.context, R.color.red_500)
                    )
                }
                SearchTrend.DOWN -> {
                    binding.imageViewTrend.setImageResource(R.drawable.ic_trending_down)
                    binding.imageViewTrend.setColorFilter(
                        ContextCompat.getColor(binding.root.context, R.color.green_500)
                    )
                }
                SearchTrend.STABLE -> {
                    binding.imageViewTrend.setImageResource(R.drawable.ic_trending_flat)
                    binding.imageViewTrend.setColorFilter(
                        ContextCompat.getColor(binding.root.context, R.color.gray_500)
                    )
                }
            }

            // 设置点击事件
            binding.root.setOnClickListener {
                onKeywordClick(keyword)
            }
        }
    }

    private class HotSearchDiffCallback : DiffUtil.ItemCallback<HotSearchKeyword>() {
        override fun areItemsTheSame(oldItem: HotSearchKeyword, newItem: HotSearchKeyword): Boolean {
            return oldItem.keyword == newItem.keyword
        }

        override fun areContentsTheSame(oldItem: HotSearchKeyword, newItem: HotSearchKeyword): Boolean {
            return oldItem == newItem
        }
    }
}
