# 洗护帮用户端 Android 应用

## 📱 项目简介

洗护帮用户端是一个现代化的Android应用，为用户提供便捷的洗护服务预订平台。应用采用MVVM架构，集成了智能搜索、投流排名、实时定位、在线支付等功能。

## 🚀 在VSCode中运行项目

### 前置要求

1. **安装Java Development Kit (JDK)**
   ```bash
   # 推荐使用JDK 11或17
   java -version
   ```

2. **安装Android SDK**
   ```bash
   # 设置环境变量
   export ANDROID_HOME=/path/to/android/sdk
   export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools
   ```

3. **安装VSCode扩展**
   - Extension Pack for Java
   - Kotlin Language
   - Android iOS Emulator
   - Gradle for Java
   - XML Tools

### 🔧 环境配置

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd laundry-user
   ```

2. **配置local.properties**
   ```properties
   # 在项目根目录创建local.properties文件
   sdk.dir=/path/to/android/sdk
   ndk.dir=/path/to/android/ndk
   ```

3. **配置API密钥**
   ```xml
   <!-- 在app/src/main/res/values/strings.xml中配置 -->
   <string name="google_maps_key">YOUR_GOOGLE_MAPS_API_KEY</string>
   <string name="alipay_app_id">YOUR_ALIPAY_APP_ID</string>
   <string name="wechat_app_id">YOUR_WECHAT_APP_ID</string>
   ```

### 📱 运行步骤

#### 方法一：使用VSCode任务

1. **打开命令面板** (`Ctrl+Shift+P`)
2. **运行任务**：
   - `Tasks: Run Task` → `android: build debug`
   - `Tasks: Run Task` → `android: install debug`

#### 方法二：使用终端命令

1. **构建项目**
   ```bash
   ./gradlew assembleDebug
   ```

2. **安装到设备**
   ```bash
   ./gradlew installDebug
   ```

3. **启动应用**
   ```bash
   adb shell am start -n com.laundry.user/.ui.main.MainActivity
   ```

#### 方法三：使用调试器

1. **按F5启动调试**
2. **选择配置**：
   - `Launch Android App` - 构建并启动
   - `Launch on Emulator` - 在模拟器上运行
   - `Launch on Physical Device` - 在真机上运行

### 🔍 调试功能

#### 查看日志
```bash
# 查看应用日志
adb logcat -v time com.laundry.user:V *:S

# 或使用VSCode任务
Tasks: Run Task → adb: logcat
```

#### 设备管理
```bash
# 查看连接的设备
adb devices

# 启动模拟器
emulator -avd Pixel_4_API_30
```

### 🛠️ 开发工具

#### Gradle任务
```bash
# 清理项目
./gradlew clean

# 构建Debug版本
./gradlew assembleDebug

# 构建Release版本
./gradlew assembleRelease

# 运行单元测试
./gradlew test

# 运行集成测试
./gradlew connectedAndroidTest

# 代码检查
./gradlew lint
```

#### 代码格式化
- 保存时自动格式化（已配置）
- 手动格式化：`Shift+Alt+F`
- 组织导入：`Shift+Alt+O`

### 📁 项目结构

```
laundry-user/
├── app/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/laundry/user/
│   │   │   │   ├── data/          # 数据层
│   │   │   │   ├── network/       # 网络层
│   │   │   │   ├── ui/            # UI层
│   │   │   │   ├── utils/         # 工具类
│   │   │   │   └── LaundryApplication.kt
│   │   │   ├── res/               # 资源文件
│   │   │   └── AndroidManifest.xml
│   │   ├── test/                  # 单元测试
│   │   └── androidTest/           # 集成测试
│   ├── build.gradle               # 应用级构建配置
│   └── proguard-rules.pro         # 混淆规则
├── build.gradle                   # 项目级构建配置
├── gradle.properties              # Gradle配置
├── settings.gradle                # 项目设置
└── .vscode/                       # VSCode配置
    ├── launch.json                # 调试配置
    ├── tasks.json                 # 任务配置
    └── settings.json              # 编辑器设置
```

### 🔧 常见问题解决

#### 1. Gradle同步失败
```bash
# 清理Gradle缓存
./gradlew clean
rm -rf .gradle
./gradlew build
```

#### 2. SDK路径问题
```bash
# 检查ANDROID_HOME环境变量
echo $ANDROID_HOME

# 更新local.properties
sdk.dir=/correct/path/to/android/sdk
```

#### 3. 依赖下载失败
```bash
# 使用阿里云镜像（在build.gradle中配置）
repositories {
    maven { url 'https://maven.aliyun.com/repository/google' }
    maven { url 'https://maven.aliyun.com/repository/central' }
    google()
    mavenCentral()
}
```

#### 4. 模拟器启动失败
```bash
# 检查可用的AVD
emulator -list-avds

# 创建新的AVD
avdmanager create avd -n Pixel_4_API_30 -k "system-images;android-30;google_apis;x86_64"
```

### 📊 性能监控

#### 内存分析
```bash
# 使用Android Studio Profiler
# 或者使用命令行工具
adb shell dumpsys meminfo com.laundry.user
```

#### 网络监控
```bash
# 查看网络请求
adb shell tcpdump -i any -w /sdcard/capture.pcap
```

### 🧪 测试

#### 运行单元测试
```bash
./gradlew test
```

#### 运行UI测试
```bash
./gradlew connectedAndroidTest
```

#### 代码覆盖率
```bash
./gradlew jacocoTestReport
```

### 📦 打包发布

#### Debug版本
```bash
./gradlew assembleDebug
```

#### Release版本
```bash
# 需要配置签名
./gradlew assembleRelease
```

### 🔐 安全配置

1. **配置签名密钥**
2. **启用代码混淆**
3. **配置网络安全**
4. **API密钥保护**

### 📚 相关文档

- [Android开发指南](https://developer.android.com/guide)
- [Kotlin官方文档](https://kotlinlang.org/docs/)
- [Material Design](https://material.io/design)
- [Jetpack组件](https://developer.android.com/jetpack)

### 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

### 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

### 📞 支持

如有问题，请联系：
- 邮箱：<EMAIL>
- 技术支持：<EMAIL>
