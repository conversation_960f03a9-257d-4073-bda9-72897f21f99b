package com.laundry.merchant.ui.notification

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.repository.NotificationRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class NotificationViewModel @Inject constructor(
    private val notificationRepository: NotificationRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(NotificationUiState())
    val uiState: StateFlow<NotificationUiState> = _uiState.asStateFlow()

    private val _events = MutableSharedFlow<NotificationEvent>()
    val events: SharedFlow<NotificationEvent> = _events.asSharedFlow()

    private var allNotifications: List<NotificationData> = emptyList()
    private var currentFilter: String? = null

    fun loadNotifications() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                allNotifications = notificationRepository.getNotifications()
                applyFilter()
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载通知失败: ${e.message}"
                )
                _events.emit(NotificationEvent.ShowError("加载通知失败"))
            }
        }
    }

    fun refreshNotifications() {
        loadNotifications()
    }

    fun filterByType(type: String?) {
        currentFilter = type
        applyFilter()
    }

    fun markAsRead(notificationId: String) {
        viewModelScope.launch {
            try {
                notificationRepository.markAsRead(notificationId)
                
                // 更新本地数据
                allNotifications = allNotifications.map { notification ->
                    if (notification.id == notificationId) {
                        notification.copy(isRead = true)
                    } else {
                        notification
                    }
                }
                applyFilter()
                
            } catch (e: Exception) {
                _events.emit(NotificationEvent.ShowError("标记已读失败"))
            }
        }
    }

    fun markAllAsRead() {
        viewModelScope.launch {
            try {
                notificationRepository.markAllAsRead()
                
                // 更新本地数据
                allNotifications = allNotifications.map { notification ->
                    notification.copy(isRead = true)
                }
                applyFilter()
                
                _events.emit(NotificationEvent.ShowSuccess("已全部标记为已读"))
                
            } catch (e: Exception) {
                _events.emit(NotificationEvent.ShowError("标记已读失败"))
            }
        }
    }

    fun deleteNotification(notificationId: String) {
        viewModelScope.launch {
            try {
                notificationRepository.deleteNotification(notificationId)
                
                // 更新本地数据
                allNotifications = allNotifications.filter { it.id != notificationId }
                applyFilter()
                
                _events.emit(NotificationEvent.ShowSuccess("删除成功"))
                
            } catch (e: Exception) {
                _events.emit(NotificationEvent.ShowError("删除失败"))
            }
        }
    }

    fun clearAllNotifications() {
        viewModelScope.launch {
            try {
                notificationRepository.clearAllNotifications()
                
                // 清空本地数据
                allNotifications = emptyList()
                applyFilter()
                
                _events.emit(NotificationEvent.ShowSuccess("已清空所有通知"))
                
            } catch (e: Exception) {
                _events.emit(NotificationEvent.ShowError("清空失败"))
            }
        }
    }

    private fun applyFilter() {
        val filteredNotifications = if (currentFilter == null) {
            allNotifications
        } else {
            allNotifications.filter { it.type == currentFilter }
        }

        _uiState.value = _uiState.value.copy(notifications = filteredNotifications)
    }
}

// UI状态数据类
data class NotificationUiState(
    val isLoading: Boolean = false,
    val notifications: List<NotificationData> = emptyList(),
    val error: String? = null
)

// 事件数据类
sealed class NotificationEvent {
    data class ShowError(val message: String) : NotificationEvent()
    data class ShowSuccess(val message: String) : NotificationEvent()
    data class NavigateToOrder(val orderId: String) : NotificationEvent()
    object NavigateToFinance : NotificationEvent()
    object NavigateToPromotion : NotificationEvent()
}

// 通知数据类
data class NotificationData(
    val id: String,
    val title: String,
    val content: String,
    val type: String, // order, finance, promotion, system
    val isRead: Boolean,
    val createdAt: Date,
    val relatedId: String? = null, // 关联的订单ID、交易ID等
    val priority: String = "normal", // high, normal, low
    val actionText: String? = null,
    val actionUrl: String? = null
)
