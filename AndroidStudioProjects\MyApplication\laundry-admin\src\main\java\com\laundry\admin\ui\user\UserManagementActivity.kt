package com.laundry.admin.ui.user

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.tabs.TabLayout
import com.laundry.admin.R
import com.laundry.admin.data.model.UserStatus
import com.laundry.admin.data.model.RiskLevel
import com.laundry.admin.databinding.ActivityUserManagementBinding
import com.laundry.admin.ui.user.adapter.UserManagementAdapter
import com.laundry.admin.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class UserManagementActivity : AppCompatActivity() {

    private lateinit var binding: ActivityUserManagementBinding
    private val viewModel: UserManagementViewModel by viewModels()
    
    private lateinit var userAdapter: UserManagementAdapter
    private var currentFilter = UserStatus.ACTIVE

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUserManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerView()
        setupTabs()
        observeViewModel()
        
        // 处理从告警跳转的情况
        val alertId = intent.getStringExtra("alert_id")
        if (alertId != null) {
            viewModel.handleAlert(alertId)
        } else {
            viewModel.loadUsers(currentFilter)
        }
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "用户管理"

        // 设置刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.loadUsers(currentFilter)
        }

        // 设置浮动按钮
        binding.fabExportData.setOnClickListener {
            showExportDialog()
        }
    }

    private fun setupRecyclerView() {
        userAdapter = UserManagementAdapter(
            onUserClick = { user ->
                showUserDetailDialog(user)
            },
            onStatusChange = { user, newStatus ->
                showStatusChangeDialog(user, newStatus)
            },
            onViewOrders = { user ->
                viewModel.loadUserOrders(user.user.id)
            },
            onViewViolations = { user ->
                viewModel.loadUserViolations(user.user.id)
            }
        )
        
        binding.recyclerViewUsers.apply {
            layoutManager = LinearLayoutManager(this@UserManagementActivity)
            adapter = userAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("活跃用户"))
            addTab(newTab().setText("已暂停"))
            addTab(newTab().setText("已封禁"))
            addTab(newTab().setText("待验证"))
            addTab(newTab().setText("高风险"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    currentFilter = when (tab?.position) {
                        0 -> UserStatus.ACTIVE
                        1 -> UserStatus.SUSPENDED
                        2 -> UserStatus.BANNED
                        3 -> UserStatus.PENDING_VERIFICATION
                        4 -> UserStatus.ACTIVE // 高风险用户，需要特殊处理
                        else -> UserStatus.ACTIVE
                    }
                    
                    if (tab?.position == 4) {
                        viewModel.loadHighRiskUsers()
                    } else {
                        viewModel.loadUsers(currentFilter)
                    }
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_user_management, menu)
        
        // 设置搜索
        val searchItem = menu?.findItem(R.id.action_search)
        val searchView = searchItem?.actionView as? SearchView
        searchView?.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { viewModel.searchUsers(it) }
                return true
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                if (newText.isNullOrEmpty()) {
                    viewModel.loadUsers(currentFilter)
                }
                return true
            }
        })
        
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_filter -> {
                showFilterDialog()
                true
            }
            R.id.action_batch_operation -> {
                showBatchOperationDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: UserManagementUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新用户列表
        userAdapter.updateData(state.users)

        // 更新统计信息
        binding.textViewTotalUsers.text = "总用户: ${state.totalUsers}"
        binding.textViewActiveUsers.text = "活跃: ${state.activeUsers}"
        binding.textViewSuspendedUsers.text = "暂停: ${state.suspendedUsers}"
        binding.textViewBannedUsers.text = "封禁: ${state.bannedUsers}"

        // 更新空状态
        if (state.users.isEmpty() && !state.isLoading) {
            binding.layoutEmpty.visibility = View.VISIBLE
            binding.recyclerViewUsers.visibility = View.GONE
        } else {
            binding.layoutEmpty.visibility = View.GONE
            binding.recyclerViewUsers.visibility = View.VISIBLE
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: UserManagementEvent) {
        when (event) {
            is UserManagementEvent.ShowError -> {
                showError(event.message)
            }
            is UserManagementEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is UserManagementEvent.ShowUserDetail -> {
                showUserDetailDialog(event.user)
            }
            is UserManagementEvent.StatusChanged -> {
                showToast("用户状态已更新")
                viewModel.loadUsers(currentFilter)
            }
        }
    }

    private fun showUserDetailDialog(user: com.laundry.admin.data.model.UserManagement) {
        val dialog = UserDetailDialog.newInstance(user)
        dialog.show(supportFragmentManager, "UserDetailDialog")
    }

    private fun showStatusChangeDialog(user: com.laundry.admin.data.model.UserManagement, newStatus: UserStatus) {
        val statusText = when (newStatus) {
            UserStatus.ACTIVE -> "激活"
            UserStatus.SUSPENDED -> "暂停"
            UserStatus.BANNED -> "封禁"
            UserStatus.PENDING_VERIFICATION -> "待验证"
        }
        
        MaterialAlertDialogBuilder(this)
            .setTitle("确认操作")
            .setMessage("确定要${statusText}用户 ${user.user.username} 吗？")
            .setPositiveButton("确定") { _, _ ->
                showReasonDialog(user, newStatus)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showReasonDialog(user: com.laundry.admin.data.model.UserManagement, newStatus: UserStatus) {
        val dialog = StatusChangeReasonDialog.newInstance(user.user.id, newStatus) { reason ->
            viewModel.changeUserStatus(user.user.id, newStatus, reason)
        }
        dialog.show(supportFragmentManager, "StatusChangeReasonDialog")
    }

    private fun showFilterDialog() {
        val filters = arrayOf("全部", "高风险", "有违规记录", "新注册", "高消费")
        var selectedFilter = 0
        
        MaterialAlertDialogBuilder(this)
            .setTitle("筛选条件")
            .setSingleChoiceItems(filters, selectedFilter) { _, which ->
                selectedFilter = which
            }
            .setPositiveButton("确定") { _, _ ->
                applyFilter(selectedFilter)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showBatchOperationDialog() {
        val operations = arrayOf("批量激活", "批量暂停", "批量导出", "批量发送通知")
        
        MaterialAlertDialogBuilder(this)
            .setTitle("批量操作")
            .setItems(operations) { _, which ->
                when (which) {
                    0 -> showBatchStatusChangeDialog(UserStatus.ACTIVE)
                    1 -> showBatchStatusChangeDialog(UserStatus.SUSPENDED)
                    2 -> exportSelectedUsers()
                    3 -> showBatchNotificationDialog()
                }
            }
            .show()
    }

    private fun showBatchStatusChangeDialog(newStatus: UserStatus) {
        val selectedUsers = userAdapter.getSelectedUsers()
        if (selectedUsers.isEmpty()) {
            showToast("请先选择要操作的用户")
            return
        }
        
        val statusText = when (newStatus) {
            UserStatus.ACTIVE -> "激活"
            UserStatus.SUSPENDED -> "暂停"
            else -> "更改状态"
        }
        
        MaterialAlertDialogBuilder(this)
            .setTitle("批量${statusText}")
            .setMessage("确定要${statusText} ${selectedUsers.size} 个用户吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.batchChangeUserStatus(selectedUsers.map { it.user.id }, newStatus, "批量操作")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showBatchNotificationDialog() {
        val selectedUsers = userAdapter.getSelectedUsers()
        if (selectedUsers.isEmpty()) {
            showToast("请先选择要发送通知的用户")
            return
        }
        
        val dialog = BatchNotificationDialog.newInstance(selectedUsers.map { it.user.id }) { title, message ->
            viewModel.sendBatchNotification(selectedUsers.map { it.user.id }, title, message)
        }
        dialog.show(supportFragmentManager, "BatchNotificationDialog")
    }

    private fun showExportDialog() {
        val exportTypes = arrayOf("当前列表", "全部用户", "选中用户", "自定义条件")
        
        MaterialAlertDialogBuilder(this)
            .setTitle("导出数据")
            .setItems(exportTypes) { _, which ->
                when (which) {
                    0 -> viewModel.exportCurrentList()
                    1 -> viewModel.exportAllUsers()
                    2 -> exportSelectedUsers()
                    3 -> showCustomExportDialog()
                }
            }
            .show()
    }

    private fun exportSelectedUsers() {
        val selectedUsers = userAdapter.getSelectedUsers()
        if (selectedUsers.isEmpty()) {
            showToast("请先选择要导出的用户")
            return
        }
        
        viewModel.exportSelectedUsers(selectedUsers.map { it.user.id })
    }

    private fun showCustomExportDialog() {
        val dialog = CustomExportDialog.newInstance { criteria ->
            viewModel.exportWithCriteria(criteria)
        }
        dialog.show(supportFragmentManager, "CustomExportDialog")
    }

    private fun applyFilter(filterIndex: Int) {
        when (filterIndex) {
            0 -> viewModel.loadUsers(currentFilter)
            1 -> viewModel.loadHighRiskUsers()
            2 -> viewModel.loadUsersWithViolations()
            3 -> viewModel.loadNewUsers()
            4 -> viewModel.loadHighValueUsers()
        }
    }

    private fun showError(message: String) {
        showToast(message)
    }
}
