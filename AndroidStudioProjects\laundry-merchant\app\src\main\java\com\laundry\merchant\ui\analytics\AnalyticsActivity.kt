package com.laundry.merchant.ui.analytics

import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.*
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.utils.ColorTemplate
import com.google.android.material.tabs.TabLayout
import com.laundry.merchant.R
import com.laundry.merchant.analytics.TimePeriod
import com.laundry.merchant.databinding.ActivityAnalyticsBinding
import com.laundry.merchant.ui.analytics.adapter.TopProductsAdapter
import com.laundry.merchant.utils.formatCurrency
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class AnalyticsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAnalyticsBinding
    private val viewModel: AnalyticsViewModel by viewModels()
    
    private lateinit var topProductsAdapter: TopProductsAdapter
    private var currentPeriod = TimePeriod.WEEKLY

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAnalyticsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupCharts()
        setupRecyclerViews()
        setupTabs()
        observeViewModel()
        
        // 加载数据
        viewModel.loadBusinessStatistics(currentPeriod)
        viewModel.loadRealTimeAnalytics()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "数据分析"

        // 设置刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            refreshData()
        }

        // 设置详情按钮
        binding.buttonViewDetails.setOnClickListener {
            // TODO: 跳转到详细分析页面
            showToast("跳转到详细分析")
        }
    }

    private fun setupCharts() {
        setupRevenueChart()
        setupOrderChart()
        setupPaymentMethodChart()
        setupUserChart()
    }

    private fun setupRevenueChart() {
        binding.chartRevenue.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            setDrawGridBackground(false)
            
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
                granularity = 1f
            }
            
            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }
            
            axisRight.isEnabled = false
            legend.isEnabled = true
        }
    }

    private fun setupOrderChart() {
        binding.chartOrders.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            setDrawGridBackground(false)
            
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
                granularity = 1f
            }
            
            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }
            
            axisRight.isEnabled = false
            legend.isEnabled = true
        }
    }

    private fun setupPaymentMethodChart() {
        binding.chartPaymentMethods.apply {
            description.isEnabled = false
            setUsePercentValues(true)
            setDrawHoleEnabled(true)
            setHoleColor(Color.WHITE)
            setTransparentCircleColor(Color.WHITE)
            setTransparentCircleAlpha(110)
            holeRadius = 58f
            transparentCircleRadius = 61f
            setDrawCenterText(true)
            centerText = "支付方式\n分布"
            isRotationEnabled = true
            isHighlightPerTapEnabled = true
            legend.isEnabled = true
        }
    }

    private fun setupUserChart() {
        binding.chartUsers.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            setDrawGridBackground(false)
            
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
                granularity = 1f
            }
            
            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }
            
            axisRight.isEnabled = false
            legend.isEnabled = true
        }
    }

    private fun setupRecyclerViews() {
        topProductsAdapter = TopProductsAdapter { product ->
            // TODO: 跳转到产品详细分析
            showToast("查看${product.productName}详细分析")
        }
        
        binding.recyclerViewTopProducts.apply {
            layoutManager = LinearLayoutManager(this@AnalyticsActivity)
            adapter = topProductsAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("今日"))
            addTab(newTab().setText("本周"))
            addTab(newTab().setText("本月"))
            addTab(newTab().setText("本季"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    currentPeriod = when (tab?.position) {
                        0 -> TimePeriod.DAILY
                        1 -> TimePeriod.WEEKLY
                        2 -> TimePeriod.MONTHLY
                        3 -> TimePeriod.QUARTERLY
                        else -> TimePeriod.WEEKLY
                    }
                    viewModel.loadBusinessStatistics(currentPeriod)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
            
            // 默认选中本周
            getTabAt(1)?.select()
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: AnalyticsUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新业务统计
        state.businessStatistics?.let { stats ->
            updateBusinessStatistics(stats)
            updateRevenueChart(stats)
            updateOrderChart(stats)
            updatePaymentMethodChart(stats)
        }

        // 更新实时数据
        state.realTimeAnalytics?.let { realTime ->
            updateRealTimeData(realTime)
            updateUserChart(realTime)
        }

        // 更新热门产品
        topProductsAdapter.updateData(state.topProducts)

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun updateBusinessStatistics(stats: com.laundry.merchant.analytics.BusinessStatistics) {
        // 更新关键指标
        binding.textViewTotalRevenue.text = stats.totalRevenue.formatCurrency()
        binding.textViewTotalOrders.text = "${stats.totalOrders}"
        binding.textViewActiveUsers.text = "${stats.activeUsers}"
        binding.textViewConversionRate.text = "${String.format("%.1f", stats.conversionRate * 100)}%"
        
        binding.textViewAverageOrderValue.text = stats.averageOrderValue.formatCurrency()
        binding.textViewRetentionRate.text = "${String.format("%.1f", stats.retentionRate * 100)}%"
        binding.textViewNewUsers.text = "${stats.newUsers}"
        binding.textViewCustomerLifetimeValue.text = stats.customerLifetimeValue.formatCurrency()
    }

    private fun updateRealTimeData(realTime: com.laundry.merchant.analytics.RealTimeAnalytics) {
        binding.textViewActiveUsersRealtime.text = "${realTime.activeUsers}"
        binding.textViewCurrentSessions.text = "${realTime.currentSessions}"
        binding.textViewRealtimeRevenue.text = realTime.realtimeRevenue.formatCurrency()
        binding.textViewRealtimeOrders.text = "${realTime.realtimeOrders}"
    }

    private fun updateRevenueChart(stats: com.laundry.merchant.analytics.BusinessStatistics) {
        // 模拟收入趋势数据
        val entries = mutableListOf<Entry>()
        val labels = mutableListOf<String>()
        
        for (i in 0..6) {
            entries.add(Entry(i.toFloat(), (stats.totalRevenue * (0.8 + Math.random() * 0.4) / 7).toFloat()))
            labels.add("${i + 1}日")
        }
        
        val dataSet = LineDataSet(entries, "收入趋势").apply {
            color = ContextCompat.getColor(this@AnalyticsActivity, R.color.colorPrimary)
            setCircleColor(ContextCompat.getColor(this@AnalyticsActivity, R.color.colorPrimary))
            lineWidth = 2f
            circleRadius = 4f
            setDrawCircleHole(false)
            valueTextSize = 10f
        }
        
        val lineData = LineData(dataSet)
        binding.chartRevenue.apply {
            data = lineData
            xAxis.valueFormatter = IndexAxisValueFormatter(labels)
            invalidate()
        }
    }

    private fun updateOrderChart(stats: com.laundry.merchant.analytics.BusinessStatistics) {
        // 模拟订单趋势数据
        val entries = mutableListOf<BarEntry>()
        val labels = mutableListOf<String>()
        
        for (i in 0..6) {
            entries.add(BarEntry(i.toFloat(), (stats.totalOrders * (0.8 + Math.random() * 0.4) / 7).toFloat()))
            labels.add("${i + 1}日")
        }
        
        val dataSet = BarDataSet(entries, "订单数量").apply {
            color = ContextCompat.getColor(this@AnalyticsActivity, R.color.colorAccent)
            valueTextSize = 10f
        }
        
        val barData = BarData(dataSet)
        binding.chartOrders.apply {
            data = barData
            xAxis.valueFormatter = IndexAxisValueFormatter(labels)
            invalidate()
        }
    }

    private fun updatePaymentMethodChart(stats: com.laundry.merchant.analytics.BusinessStatistics) {
        val entries = mutableListOf<PieEntry>()
        val colors = mutableListOf<Int>()
        
        stats.paymentMethodDistribution.forEach { (method, percentage) ->
            entries.add(PieEntry((percentage * 100).toFloat(), method))
        }
        
        colors.addAll(ColorTemplate.MATERIAL_COLORS.toList())
        
        val dataSet = PieDataSet(entries, "").apply {
            this.colors = colors
            valueTextSize = 12f
            valueTextColor = Color.WHITE
        }
        
        val pieData = PieData(dataSet)
        binding.chartPaymentMethods.apply {
            data = pieData
            invalidate()
        }
    }

    private fun updateUserChart(realTime: com.laundry.merchant.analytics.RealTimeAnalytics) {
        // 模拟用户活跃度数据
        val entries = mutableListOf<Entry>()
        val labels = mutableListOf<String>()
        
        for (i in 0..23) {
            val hour = if (i < 10) "0$i" else "$i"
            entries.add(Entry(i.toFloat(), (realTime.activeUsers * (0.5 + Math.random() * 0.5)).toFloat()))
            labels.add("${hour}:00")
        }
        
        val dataSet = LineDataSet(entries, "活跃用户").apply {
            color = ContextCompat.getColor(this@AnalyticsActivity, R.color.green_500)
            setCircleColor(ContextCompat.getColor(this@AnalyticsActivity, R.color.green_500))
            lineWidth = 2f
            circleRadius = 3f
            setDrawCircleHole(false)
            valueTextSize = 8f
            setDrawValues(false)
        }
        
        val lineData = LineData(dataSet)
        binding.chartUsers.apply {
            data = lineData
            xAxis.valueFormatter = IndexAxisValueFormatter(labels)
            invalidate()
        }
    }

    private fun handleEvent(event: AnalyticsEvent) {
        when (event) {
            is AnalyticsEvent.ShowError -> {
                showError(event.message)
            }
            is AnalyticsEvent.ShowSuccess -> {
                showToast(event.message)
            }
        }
    }

    private fun refreshData() {
        viewModel.loadBusinessStatistics(currentPeriod)
        viewModel.loadRealTimeAnalytics()
        viewModel.loadTopProducts(currentPeriod)
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
