@echo off
chcp 65001 >nul
echo.
echo ================================
echo    洗护帮用户端 - 启动助手
echo ================================
echo.

:: 设置颜色
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%正在检查开发环境...%NC%
echo.

:: 检查当前目录
echo %BLUE%[1/6] 检查项目目录...%NC%
if exist "MyApplication\gradlew.bat" (
    echo %GREEN%✅ 找到Kotlin Multiplatform项目%NC%
    set "PROJECT_TYPE=KMP"
    set "PROJECT_DIR=MyApplication"
) else if exist "laundry-user\gradlew.bat" (
    echo %GREEN%✅ 找到Android项目%NC%
    set "PROJECT_TYPE=Android"
    set "PROJECT_DIR=laundry-user"
) else (
    echo %RED%❌ 未找到有效的Android项目%NC%
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

:: 检查Java环境
echo %BLUE%[2/6] 检查Java环境...%NC%
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Java未安装或配置错误%NC%
    echo.
    echo 请按以下步骤配置Java:
    echo 1. 下载并安装JDK 11或17: https://adoptium.net/
    echo 2. 设置JAVA_HOME环境变量
    echo 3. 将Java bin目录添加到PATH
    echo.
    echo 当前JAVA_HOME: %JAVA_HOME%
    pause
    exit /b 1
) else (
    echo %GREEN%✅ Java环境正常%NC%
    java -version 2>&1 | findstr "version"
)

:: 检查Android SDK
echo %BLUE%[3/6] 检查Android SDK...%NC%
if not defined ANDROID_HOME (
    echo %YELLOW%⚠️  ANDROID_HOME未设置%NC%
    echo.
    echo 请设置ANDROID_HOME环境变量:
    echo set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    echo.
    echo 或者使用Android Studio自动配置
    pause
) else (
    echo %GREEN%✅ Android SDK: %ANDROID_HOME%%NC%
)

:: 检查ADB
echo %BLUE%[4/6] 检查ADB工具...%NC%
if defined ANDROID_HOME (
    if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
        echo %GREEN%✅ ADB工具正常%NC%
    ) else (
        echo %YELLOW%⚠️  ADB工具未找到%NC%
    )
) else (
    adb version >nul 2>&1
    if %errorlevel% equ 0 (
        echo %GREEN%✅ ADB工具正常%NC%
    ) else (
        echo %YELLOW%⚠️  ADB工具未找到%NC%
    )
)

:: 检查设备连接
echo %BLUE%[5/6] 检查设备连接...%NC%
if defined ANDROID_HOME (
    "%ANDROID_HOME%\platform-tools\adb.exe" devices 2>nul | findstr "device$" >nul
) else (
    adb devices 2>nul | findstr "device$" >nul
)

if %errorlevel% neq 0 (
    echo %YELLOW%⚠️  没有检测到连接的设备%NC%
    echo.
    echo 请选择以下选项:
    echo 1. 连接Android设备并启用USB调试
    echo 2. 启动Android模拟器
    echo 3. 使用Android Studio启动项目
    echo.
) else (
    echo %GREEN%✅ 设备连接正常%NC%
    if defined ANDROID_HOME (
        "%ANDROID_HOME%\platform-tools\adb.exe" devices
    ) else (
        adb devices
    )
)

:: 显示启动选项
echo %BLUE%[6/6] 启动选项...%NC%
echo.
echo ================================
echo    🚀 启动方式选择
echo ================================
echo.
echo 项目类型: %PROJECT_TYPE%
echo 项目目录: %PROJECT_DIR%
echo.
echo 推荐启动方式:
echo.
echo %GREEN%1. Android Studio (最佳体验)%NC%
echo    - 打开Android Studio
echo    - 导入项目: %PROJECT_DIR%
echo    - 等待Gradle同步
echo    - 点击运行按钮
echo.
echo %GREEN%2. VSCode (轻量级)%NC%
echo    - 安装Java和Android扩展
echo    - 打开项目文件夹
echo    - 按F5调试
echo.
echo %GREEN%3. 命令行 (高级用户)%NC%
echo    - cd %PROJECT_DIR%
echo    - gradlew assembleDebug
echo    - gradlew installDebug
echo.
echo ================================
echo    📋 环境检查完成
echo ================================
echo.

:: 询问是否尝试构建
echo 是否要尝试构建项目？ (y/n)
set /p build_project=
if /i "%build_project%"=="y" (
    echo.
    echo %BLUE%正在构建项目...%NC%
    cd "%PROJECT_DIR%"
    if "%PROJECT_TYPE%"=="KMP" (
        call gradlew.bat :app:assembleDebug
    ) else (
        call gradlew.bat assembleDebug
    )
    
    if %errorlevel% equ 0 (
        echo %GREEN%✅ 项目构建成功%NC%
        echo.
        echo APK文件位置:
        if "%PROJECT_TYPE%"=="KMP" (
            echo app\build\outputs\apk\debug\app-debug.apk
        ) else (
            echo app\build\outputs\apk\debug\app-debug.apk
        )
    ) else (
        echo %RED%❌ 项目构建失败%NC%
        echo 请检查错误信息并修复问题
    )
)

echo.
echo ================================
echo    感谢使用洗护帮开发工具
echo ================================
pause
