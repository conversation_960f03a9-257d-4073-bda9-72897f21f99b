package com.laundry.merchant.data.repository

import com.laundry.merchant.ui.favorite.FavoriteData
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

interface FavoriteRepository {
    suspend fun getFavorites(): List<FavoriteData>
    suspend fun addFavorite(favorite: FavoriteData)
    suspend fun removeFavorite(favoriteId: String)
    suspend fun clearAllFavorites()
    suspend fun isFavorite(relatedId: String, type: String): Boolean
}

@Singleton
class FavoriteRepositoryImpl @Inject constructor(
    // private val apiService: ApiService,
    // private val favoriteDao: FavoriteDao
) : FavoriteRepository {

    // 临时使用内存存储，实际应用中应该使用数据库或网络存储
    private val favorites = mutableListOf<FavoriteData>()

    init {
        // 添加一些模拟数据
        favorites.addAll(getMockFavorites())
    }

    override suspend fun getFavorites(): List<FavoriteData> {
        return favorites.sortedByDescending { it.createdAt }
    }

    override suspend fun addFavorite(favorite: FavoriteData) {
        // 检查是否已存在
        val exists = favorites.any { 
            it.relatedId == favorite.relatedId && it.type == favorite.type 
        }
        
        if (!exists) {
            favorites.add(favorite)
        }
    }

    override suspend fun removeFavorite(favoriteId: String) {
        favorites.removeAll { it.id == favoriteId }
    }

    override suspend fun clearAllFavorites() {
        favorites.clear()
    }

    override suspend fun isFavorite(relatedId: String, type: String): Boolean {
        return favorites.any { it.relatedId == relatedId && it.type == type }
    }

    private fun getMockFavorites(): List<FavoriteData> {
        return listOf(
            FavoriteData(
                id = "fav_001",
                title = "张三的洗衣订单",
                description = "普通洗衣服务，金额：25元",
                type = "order",
                relatedId = "ORD001",
                createdAt = Date(System.currentTimeMillis() - 86400000), // 1天前
                tags = listOf("常客", "好评"),
                imageUrl = null
            ),
            FavoriteData(
                id = "fav_002",
                title = "李四 - 优质客户",
                description = "电话：138****5678，地址：朝阳区某某小区",
                type = "customer",
                relatedId = "CUST001",
                createdAt = Date(System.currentTimeMillis() - 172800000), // 2天前
                tags = listOf("VIP", "月卡用户"),
                imageUrl = null
            ),
            FavoriteData(
                id = "fav_003",
                title = "高端干洗服务",
                description = "适用于西装、礼服等高档衣物",
                type = "service",
                relatedId = "SRV001",
                createdAt = Date(System.currentTimeMillis() - 259200000), // 3天前
                tags = listOf("高端", "专业"),
                imageUrl = null
            ),
            FavoriteData(
                id = "fav_004",
                title = "王五的大单订单",
                description = "床上用品清洗，金额：180元",
                type = "order",
                relatedId = "ORD005",
                createdAt = Date(System.currentTimeMillis() - 345600000), // 4天前
                tags = listOf("大单", "床品"),
                imageUrl = null
            ),
            FavoriteData(
                id = "fav_005",
                title = "赵六 - 企业客户",
                description = "某公司制服清洗，长期合作客户",
                type = "customer",
                relatedId = "CUST002",
                createdAt = Date(System.currentTimeMillis() - 432000000), // 5天前
                tags = listOf("企业", "长期合作"),
                imageUrl = null
            ),
            FavoriteData(
                id = "fav_006",
                title = "投流关键词优化方案",
                description = "提高"洗衣服务"关键词排名的策略",
                type = "other",
                relatedId = null,
                createdAt = Date(System.currentTimeMillis() - 518400000), // 6天前
                tags = listOf("投流", "优化"),
                imageUrl = null
            )
        )
    }
}
