package com.laundry.user.data.api

import com.laundry.user.data.model.*
import retrofit2.Response
import retrofit2.http.*
import okhttp3.MultipartBody
import okhttp3.RequestBody

/**
 * API服务接口
 */
interface ApiService {
    
    // ==================== 用户相关 ====================
    
    /**
     * 发送验证码
     */
    @POST("auth/send-code")
    suspend fun sendVerificationCode(@Body request: SendCodeRequest): Response<ApiResponse<String>>
    
    /**
     * 验证码登录
     */
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<ApiResponse<LoginResponse>>
    
    /**
     * 刷新Token
     */
    @POST("auth/refresh")
    suspend fun refreshToken(@Body request: RefreshTokenRequest): Response<ApiResponse<LoginResponse>>
    
    /**
     * 获取用户信息
     */
    @GET("user/profile")
    suspend fun getUserProfile(): Response<ApiResponse<User>>
    
    /**
     * 更新用户信息
     */
    @PUT("user/profile")
    suspend fun updateUserProfile(@Body user: User): Response<ApiResponse<User>>
    
    /**
     * 上传头像
     */
    @Multipart
    @POST("user/avatar")
    suspend fun uploadAvatar(@Part avatar: MultipartBody.Part): Response<ApiResponse<String>>
    
    // ==================== 服务分类 ====================
    
    /**
     * 获取服务分类列表
     */
    @GET("categories")
    suspend fun getServiceCategories(): Response<ApiResponse<List<ServiceCategory>>>
    
    // ==================== 商家相关 ====================
    
    /**
     * 获取商家列表
     */
    @GET("merchants")
    suspend fun getMerchants(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20,
        @Query("category_id") categoryId: String? = null,
        @Query("latitude") latitude: Double? = null,
        @Query("longitude") longitude: Double? = null,
        @Query("radius") radius: Int = 10000, // 搜索半径（米）
        @Query("keyword") keyword: String? = null,
        @Query("sort") sort: String = "distance" // distance, rating, price
    ): Response<ApiResponse<PagedResponse<Merchant>>>
    
    /**
     * 获取商家详情
     */
    @GET("merchants/{merchantId}")
    suspend fun getMerchantDetail(@Path("merchantId") merchantId: String): Response<ApiResponse<Merchant>>
    
    /**
     * 获取商家服务列表
     */
    @GET("merchants/{merchantId}/services")
    suspend fun getMerchantServices(@Path("merchantId") merchantId: String): Response<ApiResponse<List<Service>>>
    
    /**
     * 获取商家评价列表
     */
    @GET("merchants/{merchantId}/reviews")
    suspend fun getMerchantReviews(
        @Path("merchantId") merchantId: String,
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20
    ): Response<ApiResponse<PagedResponse<Review>>>
    
    // ==================== 订单相关 ====================
    
    /**
     * 创建订单
     */
    @POST("orders")
    suspend fun createOrder(@Body request: CreateOrderRequest): Response<ApiResponse<Order>>
    
    /**
     * 获取订单列表
     */
    @GET("orders")
    suspend fun getOrders(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20,
        @Query("status") status: Int? = null
    ): Response<ApiResponse<PagedResponse<Order>>>
    
    /**
     * 获取订单详情
     */
    @GET("orders/{orderId}")
    suspend fun getOrderDetail(@Path("orderId") orderId: String): Response<ApiResponse<Order>>
    
    /**
     * 取消订单
     */
    @PUT("orders/{orderId}/cancel")
    suspend fun cancelOrder(@Path("orderId") orderId: String, @Body reason: CancelOrderRequest): Response<ApiResponse<String>>
    
    /**
     * 确认收货
     */
    @PUT("orders/{orderId}/confirm")
    suspend fun confirmOrder(@Path("orderId") orderId: String): Response<ApiResponse<String>>
    
    // ==================== 支付相关 ====================
    
    /**
     * 创建支付订单
     */
    @POST("payment/create")
    suspend fun createPayment(@Body request: CreatePaymentRequest): Response<ApiResponse<PaymentResponse>>
    
    /**
     * 查询支付状态
     */
    @GET("payment/{paymentId}/status")
    suspend fun getPaymentStatus(@Path("paymentId") paymentId: String): Response<ApiResponse<PaymentStatusResponse>>
    
    // ==================== 评价相关 ====================
    
    /**
     * 提交评价
     */
    @POST("reviews")
    suspend fun submitReview(@Body request: SubmitReviewRequest): Response<ApiResponse<Review>>
    
    /**
     * 上传评价图片
     */
    @Multipart
    @POST("reviews/images")
    suspend fun uploadReviewImages(@Part images: List<MultipartBody.Part>): Response<ApiResponse<List<String>>>
    
    // ==================== 优惠券相关 ====================
    
    /**
     * 获取可用优惠券
     */
    @GET("coupons/available")
    suspend fun getAvailableCoupons(): Response<ApiResponse<List<Coupon>>>
    
    /**
     * 领取优惠券
     */
    @POST("coupons/{couponId}/claim")
    suspend fun claimCoupon(@Path("couponId") couponId: String): Response<ApiResponse<String>>
    
    // ==================== 收藏相关 ====================
    
    /**
     * 添加收藏
     */
    @POST("favorites")
    suspend fun addFavorite(@Body request: AddFavoriteRequest): Response<ApiResponse<String>>
    
    /**
     * 取消收藏
     */
    @DELETE("favorites/{merchantId}")
    suspend fun removeFavorite(@Path("merchantId") merchantId: String): Response<ApiResponse<String>>
    
    /**
     * 获取收藏列表
     */
    @GET("favorites")
    suspend fun getFavorites(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20
    ): Response<ApiResponse<PagedResponse<Favorite>>>
    
    // ==================== 地址相关 ====================
    
    /**
     * 获取地址列表
     */
    @GET("addresses")
    suspend fun getAddresses(): Response<ApiResponse<List<Address>>>
    
    /**
     * 添加地址
     */
    @POST("addresses")
    suspend fun addAddress(@Body address: Address): Response<ApiResponse<Address>>
    
    /**
     * 更新地址
     */
    @PUT("addresses/{addressId}")
    suspend fun updateAddress(@Path("addressId") addressId: String, @Body address: Address): Response<ApiResponse<Address>>
    
    /**
     * 删除地址
     */
    @DELETE("addresses/{addressId}")
    suspend fun deleteAddress(@Path("addressId") addressId: String): Response<ApiResponse<String>>
    
    /**
     * 设置默认地址
     */
    @PUT("addresses/{addressId}/default")
    suspend fun setDefaultAddress(@Path("addressId") addressId: String): Response<ApiResponse<String>>
    
    // ==================== 聊天相关 ====================
    
    /**
     * 获取聊天会话列表
     */
    @GET("chat/sessions")
    suspend fun getChatSessions(): Response<ApiResponse<List<ChatSession>>>
    
    /**
     * 获取聊天消息
     */
    @GET("chat/sessions/{sessionId}/messages")
    suspend fun getChatMessages(
        @Path("sessionId") sessionId: String,
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 50
    ): Response<ApiResponse<PagedResponse<ChatMessage>>>
    
    /**
     * 发送消息
     */
    @POST("chat/messages")
    suspend fun sendMessage(@Body request: SendMessageRequest): Response<ApiResponse<ChatMessage>>
    
    /**
     * 上传聊天图片/语音
     */
    @Multipart
    @POST("chat/upload")
    suspend fun uploadChatMedia(@Part media: MultipartBody.Part): Response<ApiResponse<String>>
    
    // ==================== 通知相关 ====================
    
    /**
     * 获取通知列表
     */
    @GET("notifications")
    suspend fun getNotifications(
        @Query("page") page: Int = 1,
        @Query("size") size: Int = 20
    ): Response<ApiResponse<PagedResponse<Notification>>>
    
    /**
     * 标记通知为已读
     */
    @PUT("notifications/{notificationId}/read")
    suspend fun markNotificationAsRead(@Path("notificationId") notificationId: String): Response<ApiResponse<String>>
    
    /**
     * 标记所有通知为已读
     */
    @PUT("notifications/read-all")
    suspend fun markAllNotificationsAsRead(): Response<ApiResponse<String>>
    
    // ==================== 其他 ====================
    
    /**
     * 上传文件
     */
    @Multipart
    @POST("upload")
    suspend fun uploadFile(@Part file: MultipartBody.Part): Response<ApiResponse<String>>
    
    /**
     * 获取系统配置
     */
    @GET("config")
    suspend fun getSystemConfig(): Response<ApiResponse<SystemConfig>>
    
    /**
     * 反馈问题
     */
    @POST("feedback")
    suspend fun submitFeedback(@Body request: FeedbackRequest): Response<ApiResponse<String>>
}
