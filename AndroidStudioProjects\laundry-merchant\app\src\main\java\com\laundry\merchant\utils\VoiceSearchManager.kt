package com.laundry.merchant.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import java.util.Locale

class VoiceSearchManager(
    private val context: Context,
    private val callback: VoiceSearchCallback
) {

    interface VoiceSearchCallback {
        fun onVoiceSearchStart()
        fun onVoiceSearchResult(text: String)
        fun onVoiceSearchError(error: String)
        fun onVoiceSearchEnd()
        fun onPermissionRequired()
    }

    private var speechRecognizer: SpeechRecognizer? = null
    private var isListening = false

    companion object {
        const val REQUEST_RECORD_AUDIO_PERMISSION = 200
        private const val VOICE_SEARCH_REQUEST_CODE = 1001
    }

    /**
     * 检查是否有录音权限
     */
    fun hasRecordAudioPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 请求录音权限
     */
    fun requestRecordAudioPermission(activity: Activity) {
        ActivityCompat.requestPermissions(
            activity,
            arrayOf(Manifest.permission.RECORD_AUDIO),
            REQUEST_RECORD_AUDIO_PERMISSION
        )
    }

    /**
     * 检查设备是否支持语音识别
     */
    fun isSpeechRecognitionAvailable(): Boolean {
        return SpeechRecognizer.isRecognitionAvailable(context)
    }

    /**
     * 开始语音搜索
     */
    fun startVoiceSearch() {
        if (!hasRecordAudioPermission()) {
            callback.onPermissionRequired()
            return
        }

        if (!isSpeechRecognitionAvailable()) {
            callback.onVoiceSearchError("设备不支持语音识别")
            return
        }

        if (isListening) {
            stopVoiceSearch()
            return
        }

        try {
            initializeSpeechRecognizer()
            startListening()
        } catch (e: Exception) {
            callback.onVoiceSearchError("启动语音识别失败: ${e.message}")
        }
    }

    /**
     * 停止语音搜索
     */
    fun stopVoiceSearch() {
        speechRecognizer?.stopListening()
        isListening = false
    }

    /**
     * 取消语音搜索
     */
    fun cancelVoiceSearch() {
        speechRecognizer?.cancel()
        isListening = false
    }

    /**
     * 释放资源
     */
    fun destroy() {
        speechRecognizer?.destroy()
        speechRecognizer = null
        isListening = false
    }

    private fun initializeSpeechRecognizer() {
        speechRecognizer?.destroy()
        speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
        speechRecognizer?.setRecognitionListener(createRecognitionListener())
    }

    private fun startListening() {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
            putExtra(RecognizerIntent.EXTRA_PROMPT, "请说出您要搜索的内容")
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
            putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
        }

        speechRecognizer?.startListening(intent)
        isListening = true
        callback.onVoiceSearchStart()
    }

    private fun createRecognitionListener(): RecognitionListener {
        return object : RecognitionListener {
            override fun onReadyForSpeech(params: Bundle?) {
                // 准备好接收语音输入
            }

            override fun onBeginningOfSpeech() {
                // 开始说话
            }

            override fun onRmsChanged(rmsdB: Float) {
                // 音量变化，可以用来显示音量指示器
            }

            override fun onBufferReceived(buffer: ByteArray?) {
                // 接收到音频数据
            }

            override fun onEndOfSpeech() {
                // 说话结束
                isListening = false
            }

            override fun onError(error: Int) {
                isListening = false
                val errorMessage = getErrorMessage(error)
                callback.onVoiceSearchError(errorMessage)
                callback.onVoiceSearchEnd()
            }

            override fun onResults(results: Bundle?) {
                isListening = false
                val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    val recognizedText = matches[0]
                    callback.onVoiceSearchResult(recognizedText)
                } else {
                    callback.onVoiceSearchError("未识别到语音内容")
                }
                callback.onVoiceSearchEnd()
            }

            override fun onPartialResults(partialResults: Bundle?) {
                // 部分识别结果，可以用来实时显示识别内容
                val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                if (!matches.isNullOrEmpty()) {
                    // 可以在这里处理实时识别结果
                }
            }

            override fun onEvent(eventType: Int, params: Bundle?) {
                // 其他事件
            }
        }
    }

    private fun getErrorMessage(error: Int): String {
        return when (error) {
            SpeechRecognizer.ERROR_AUDIO -> "音频录制错误"
            SpeechRecognizer.ERROR_CLIENT -> "客户端错误"
            SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "权限不足"
            SpeechRecognizer.ERROR_NETWORK -> "网络错误"
            SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "网络超时"
            SpeechRecognizer.ERROR_NO_MATCH -> "未找到匹配的语音"
            SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "识别服务忙碌"
            SpeechRecognizer.ERROR_SERVER -> "服务器错误"
            SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "语音输入超时"
            else -> "语音识别错误"
        }
    }

    /**
     * 使用系统语音搜索Activity（备用方案）
     */
    fun startSystemVoiceSearch(activity: Activity) {
        val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
            putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
            putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
            putExtra(RecognizerIntent.EXTRA_PROMPT, "请说出您要搜索的内容")
        }

        try {
            activity.startActivityForResult(intent, VOICE_SEARCH_REQUEST_CODE)
        } catch (e: Exception) {
            callback.onVoiceSearchError("启动系统语音搜索失败")
        }
    }

    /**
     * 处理系统语音搜索结果
     */
    fun handleSystemVoiceSearchResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == VOICE_SEARCH_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            val results = data?.getStringArrayListExtra(RecognizerIntent.EXTRA_RESULTS)
            if (!results.isNullOrEmpty()) {
                callback.onVoiceSearchResult(results[0])
            } else {
                callback.onVoiceSearchError("未识别到语音内容")
            }
        }
    }

    /**
     * 处理权限请求结果
     */
    fun handlePermissionResult(requestCode: Int, grantResults: IntArray): Boolean {
        if (requestCode == REQUEST_RECORD_AUDIO_PERMISSION) {
            return grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED
        }
        return false
    }
}
