package com.laundry.user.ui.merchant

import android.os.Bundle
import android.widget.*
import androidx.appcompat.app.AppCompatActivity

class MerchantListActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val categoryId = intent.getStringExtra("category_id") ?: ""
        val categoryName = intent.getStringExtra("category_name") ?: ""
        
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(20, 20, 20, 20)
        }
        
        val titleText = TextView(this).apply {
            text = "$categoryName 商家列表"
            textSize = 20f
            setPadding(0, 0, 0, 20)
        }
        
        val contentText = TextView(this).apply {
            text = "商家列表功能开发中..."
            textSize = 16f
        }
        
        layout.addView(titleText)
        layout.addView(contentText)
        
        setContentView(layout)
    }
}
