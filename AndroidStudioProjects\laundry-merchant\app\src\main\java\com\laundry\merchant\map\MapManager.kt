package com.laundry.merchant.map

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import androidx.core.app.ActivityCompat
import com.google.android.gms.location.*
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MapManager @Inject constructor(
    private val context: Context
) {
    
    private var googleMap: GoogleMap? = null
    private var fusedLocationClient: FusedLocationProviderClient? = null
    private var locationCallback: LocationCallback? = null
    
    private val _currentLocation = MutableStateFlow<Location?>(null)
    val currentLocation: StateFlow<Location?> = _currentLocation.asStateFlow()
    
    private val _merchantMarkers = MutableStateFlow<List<MerchantMarker>>(emptyList())
    val merchantMarkers: StateFlow<List<MerchantMarker>> = _merchantMarkers.asStateFlow()
    
    private val _deliveryRoute = MutableStateFlow<DeliveryRoute?>(null)
    val deliveryRoute: StateFlow<DeliveryRoute?> = _deliveryRoute.asStateFlow()
    
    private val markers = mutableMapOf<String, Marker>()
    private var deliveryPolyline: Polyline? = null

    fun initializeMap(map: GoogleMap) {
        googleMap = map
        setupMap()
    }

    fun initializeLocationClient(context: Context) {
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(context)
    }

    private fun setupMap() {
        googleMap?.apply {
            // 设置地图类型
            mapType = GoogleMap.MAP_TYPE_NORMAL
            
            // 启用定位
            if (hasLocationPermission()) {
                isMyLocationEnabled = true
                uiSettings.isMyLocationButtonEnabled = true
            }
            
            // 设置UI控件
            uiSettings.apply {
                isZoomControlsEnabled = true
                isCompassEnabled = true
                isMapToolbarEnabled = true
                isRotateGesturesEnabled = true
                isScrollGesturesEnabled = true
                isTiltGesturesEnabled = true
                isZoomGesturesEnabled = true
            }
            
            // 设置地图点击监听
            setOnMarkerClickListener { marker ->
                handleMarkerClick(marker)
                true
            }
            
            // 设置信息窗口点击监听
            setOnInfoWindowClickListener { marker ->
                handleInfoWindowClick(marker)
            }
        }
    }

    fun startLocationUpdates() {
        if (!hasLocationPermission()) return
        
        val locationRequest = LocationRequest.create().apply {
            interval = 10000 // 10秒
            fastestInterval = 5000 // 5秒
            priority = LocationRequest.PRIORITY_HIGH_ACCURACY
        }
        
        locationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                locationResult.lastLocation?.let { location ->
                    _currentLocation.value = location
                    updateCurrentLocationOnMap(location)
                }
            }
        }
        
        fusedLocationClient?.requestLocationUpdates(
            locationRequest,
            locationCallback!!,
            null
        )
    }

    fun stopLocationUpdates() {
        locationCallback?.let { callback ->
            fusedLocationClient?.removeLocationUpdates(callback)
        }
    }

    fun showMerchants(merchants: List<MerchantLocation>) {
        // 清除现有商家标记
        clearMerchantMarkers()
        
        val merchantMarkers = merchants.map { merchant ->
            val position = LatLng(merchant.latitude, merchant.longitude)
            val marker = googleMap?.addMarker(
                MarkerOptions()
                    .position(position)
                    .title(merchant.name)
                    .snippet(merchant.getInfoSnippet())
                    .icon(getMerchantIcon(merchant.status))
            )
            
            marker?.tag = merchant.id
            
            MerchantMarker(
                id = merchant.id,
                marker = marker,
                merchant = merchant
            )
        } ?: emptyList()
        
        _merchantMarkers.value = merchantMarkers
        
        // 调整地图视角以显示所有商家
        if (merchants.isNotEmpty()) {
            val bounds = LatLngBounds.Builder()
            merchants.forEach { merchant ->
                bounds.include(LatLng(merchant.latitude, merchant.longitude))
            }
            
            googleMap?.animateCamera(
                CameraUpdateFactory.newLatLngBounds(bounds.build(), 100)
            )
        }
    }

    fun showDeliveryRoute(route: DeliveryRoute) {
        // 清除现有路线
        deliveryPolyline?.remove()
        
        // 绘制配送路线
        val polylineOptions = PolylineOptions()
            .addAll(route.points)
            .width(8f)
            .color(android.graphics.Color.BLUE)
            .pattern(listOf(Dot(), Gap(10f)))
        
        deliveryPolyline = googleMap?.addPolyline(polylineOptions)
        
        // 添加起点和终点标记
        googleMap?.addMarker(
            MarkerOptions()
                .position(route.startPoint)
                .title("取件地址")
                .snippet(route.startAddress)
                .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN))
        )
        
        googleMap?.addMarker(
            MarkerOptions()
                .position(route.endPoint)
                .title("送达地址")
                .snippet(route.endAddress)
                .icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED))
        )
        
        // 如果有配送员位置，添加配送员标记
        route.deliveryPersonLocation?.let { location ->
            googleMap?.addMarker(
                MarkerOptions()
                    .position(location)
                    .title("配送员")
                    .snippet("正在配送中")
                    .icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_delivery_person))
            )
        }
        
        _deliveryRoute.value = route
        
        // 调整地图视角
        val bounds = LatLngBounds.Builder()
            .include(route.startPoint)
            .include(route.endPoint)
            .build()
        
        googleMap?.animateCamera(
            CameraUpdateFactory.newLatLngBounds(bounds, 150)
        )
    }

    fun updateDeliveryPersonLocation(location: LatLng) {
        val currentRoute = _deliveryRoute.value
        if (currentRoute != null) {
            val updatedRoute = currentRoute.copy(deliveryPersonLocation = location)
            _deliveryRoute.value = updatedRoute
            
            // 更新配送员标记位置
            // 这里需要保存配送员标记的引用来更新位置
        }
    }

    fun showServiceArea(center: LatLng, radiusInMeters: Double) {
        // 绘制服务范围圆圈
        val circleOptions = CircleOptions()
            .center(center)
            .radius(radiusInMeters)
            .strokeColor(android.graphics.Color.BLUE)
            .strokeWidth(2f)
            .fillColor(android.graphics.Color.argb(50, 0, 0, 255))
        
        googleMap?.addCircle(circleOptions)
    }

    fun calculateDistance(start: LatLng, end: LatLng): Float {
        val results = FloatArray(1)
        Location.distanceBetween(
            start.latitude, start.longitude,
            end.latitude, end.longitude,
            results
        )
        return results[0]
    }

    fun moveToLocation(location: LatLng, zoom: Float = 15f) {
        googleMap?.animateCamera(
            CameraUpdateFactory.newLatLngZoom(location, zoom)
        )
    }

    private fun updateCurrentLocationOnMap(location: Location) {
        val latLng = LatLng(location.latitude, location.longitude)
        // 如果是第一次获取位置，移动地图到当前位置
        if (_currentLocation.value == null) {
            moveToLocation(latLng)
        }
    }

    private fun clearMerchantMarkers() {
        _merchantMarkers.value.forEach { merchantMarker ->
            merchantMarker.marker?.remove()
        }
        _merchantMarkers.value = emptyList()
    }

    private fun getMerchantIcon(status: MerchantStatus): BitmapDescriptor {
        return when (status) {
            MerchantStatus.AVAILABLE -> BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN)
            MerchantStatus.BUSY -> BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_ORANGE)
            MerchantStatus.OFFLINE -> BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED)
            else -> BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_BLUE)
        }
    }

    private fun handleMarkerClick(marker: Marker): Boolean {
        // 处理标记点击事件
        val merchantId = marker.tag as? String
        if (merchantId != null) {
            // 可以触发显示商家详情等操作
        }
        return false
    }

    private fun handleInfoWindowClick(marker: Marker) {
        // 处理信息窗口点击事件
        val merchantId = marker.tag as? String
        if (merchantId != null) {
            // 可以跳转到商家详情页面
        }
    }

    private fun hasLocationPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
}

// 数据类
data class MerchantLocation(
    val id: String,
    val name: String,
    val latitude: Double,
    val longitude: Double,
    val address: String,
    val phone: String,
    val status: MerchantStatus,
    val rating: Float,
    val distance: Float? = null,
    val serviceTypes: List<String> = emptyList()
) {
    fun getInfoSnippet(): String {
        val statusText = when (status) {
            MerchantStatus.AVAILABLE -> "空闲"
            MerchantStatus.BUSY -> "忙碌"
            MerchantStatus.OFFLINE -> "离线"
            else -> "未知"
        }
        return "状态: $statusText | 评分: $rating | ${distance?.let { "距离: ${String.format("%.1f", it/1000)}km" } ?: ""}"
    }
}

data class MerchantMarker(
    val id: String,
    val marker: Marker?,
    val merchant: MerchantLocation
)

data class DeliveryRoute(
    val orderId: String,
    val startPoint: LatLng,
    val endPoint: LatLng,
    val startAddress: String,
    val endAddress: String,
    val points: List<LatLng>,
    val distance: Float,
    val estimatedDuration: Int, // 分钟
    val deliveryPersonLocation: LatLng? = null,
    val deliveryPersonName: String? = null
)
