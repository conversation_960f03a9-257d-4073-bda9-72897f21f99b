<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/coupon_background"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左侧优惠券值 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewCouponValue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="20"
                    android:textColor="@color/red_500"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewCouponUnit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="2dp"
                    android:text="减"
                    android:textColor="@color/red_500"
                    android:textSize="12sp" />

            </LinearLayout>

            <TextView
                android:id="@+id/textViewMinAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="满100可用"
                android:textColor="@color/gray_500"
                android:textSize="10sp" />

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="16dp"
            android:background="@color/gray_300" />

        <!-- 右侧优惠券信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewCouponName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="新用户专享券"
                android:textColor="@color/gray_800"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textViewCouponDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="满100减20"
                android:textColor="@color/gray_600"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/textViewValidDays"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="有效期30天"
                android:textColor="@color/gray_500"
                android:textSize="10sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
