package com.laundry.merchant.ui.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.repository.AuthRepository
import com.laundry.merchant.network.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(AuthUiState())
    val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()

    private val _events = MutableSharedFlow<AuthEvent>()
    val events: SharedFlow<AuthEvent> = _events.asSharedFlow()

    fun login(phone: String, password: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            when (val result = authRepository.login(phone, password)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _events.emit(AuthEvent.LoginSuccess)
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                    _events.emit(AuthEvent.LoginError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    fun register(phone: String, password: String, confirmPassword: String, verificationCode: String, merchantName: String) {
        viewModelScope.launch {
            // 验证输入
            if (!validateRegisterInput(phone, password, confirmPassword, verificationCode, merchantName)) {
                return@launch
            }

            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            when (val result = authRepository.register(phone, password, verificationCode, merchantName)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _events.emit(AuthEvent.RegisterSuccess)
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                    _events.emit(AuthEvent.RegisterError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    fun sendVerificationCode(phone: String) {
        viewModelScope.launch {
            if (!isValidPhone(phone)) {
                _events.emit(AuthEvent.ValidationError("phone", "请输入正确的手机号"))
                return@launch
            }

            _uiState.value = _uiState.value.copy(isSendingCode = true)

            when (val result = authRepository.sendVerificationCode(phone)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(isSendingCode = false)
                    _events.emit(AuthEvent.VerificationCodeSent)
                    startCountdown()
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(isSendingCode = false)
                    _events.emit(AuthEvent.SendCodeError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isSendingCode = true)
                }
            }
        }
    }

    fun resetPassword(phone: String, verificationCode: String, newPassword: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            when (val result = authRepository.resetPassword(phone, verificationCode, newPassword)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _events.emit(AuthEvent.ResetPasswordSuccess)
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                    _events.emit(AuthEvent.ResetPasswordError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    fun quickLogin(phone: String, verificationCode: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            when (val result = authRepository.quickLogin(phone, verificationCode)) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(isLoading = false)
                    _events.emit(AuthEvent.LoginSuccess)
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                    _events.emit(AuthEvent.LoginError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    suspend fun isLoggedIn(): Boolean {
        return authRepository.isLoggedIn().first()
    }

    fun logout() {
        viewModelScope.launch {
            authRepository.logout()
            _events.emit(AuthEvent.LogoutSuccess)
        }
    }

    private fun validateRegisterInput(
        phone: String,
        password: String,
        confirmPassword: String,
        verificationCode: String,
        merchantName: String
    ): Boolean {
        if (!isValidPhone(phone)) {
            viewModelScope.launch {
                _events.emit(AuthEvent.ValidationError("phone", "请输入正确的手机号"))
            }
            return false
        }

        if (password.length < 6) {
            viewModelScope.launch {
                _events.emit(AuthEvent.ValidationError("password", "密码至少6位"))
            }
            return false
        }

        if (password != confirmPassword) {
            viewModelScope.launch {
                _events.emit(AuthEvent.ValidationError("confirmPassword", "两次密码输入不一致"))
            }
            return false
        }

        if (verificationCode.length != 6) {
            viewModelScope.launch {
                _events.emit(AuthEvent.ValidationError("verificationCode", "请输入6位验证码"))
            }
            return false
        }

        if (merchantName.trim().isEmpty()) {
            viewModelScope.launch {
                _events.emit(AuthEvent.ValidationError("merchantName", "请输入商家名称"))
            }
            return false
        }

        return true
    }

    private fun isValidPhone(phone: String): Boolean {
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }

    private fun startCountdown() {
        viewModelScope.launch {
            for (i in 60 downTo 1) {
                _uiState.value = _uiState.value.copy(countdown = i)
                kotlinx.coroutines.delay(1000)
            }
            _uiState.value = _uiState.value.copy(countdown = 0)
        }
    }
}

// UI状态数据类
data class AuthUiState(
    val isLoading: Boolean = false,
    val isSendingCode: Boolean = false,
    val countdown: Int = 0,
    val error: String? = null
)

// 事件数据类
sealed class AuthEvent {
    object LoginSuccess : AuthEvent()
    data class LoginError(val message: String) : AuthEvent()
    object RegisterSuccess : AuthEvent()
    data class RegisterError(val message: String) : AuthEvent()
    object VerificationCodeSent : AuthEvent()
    data class SendCodeError(val message: String) : AuthEvent()
    object ResetPasswordSuccess : AuthEvent()
    data class ResetPasswordError(val message: String) : AuthEvent()
    object LogoutSuccess : AuthEvent()
    data class ValidationError(val field: String, val message: String) : AuthEvent()
}
