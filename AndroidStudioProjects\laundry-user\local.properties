# Android SDK路径配置
# 请根据您的实际安装路径修改以下配置

# Windows示例路径
sdk.dir=C\:\\Users\\%USERNAME%\\AppData\\Local\\Android\\Sdk
ndk.dir=C\:\\Users\\%USERNAME%\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393

# macOS示例路径（注释掉Windows路径，取消注释以下路径）
# sdk.dir=/Users/<USER>/Library/Android/sdk
# ndk.dir=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393

# Linux示例路径（注释掉其他路径，取消注释以下路径）
# sdk.dir=/home/<USER>/Android/Sdk
# ndk.dir=/home/<USER>/Android/Sdk/ndk/25.1.8937393

# 构建配置
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true

# Android构建配置
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true
android.enableBuildCache=true

# Kotlin配置
kotlin.code.style=official
kotlin.incremental=true
kotlin.incremental.android=true

# 开发者配置
# 签名配置（生产环境使用）
# KEYSTORE_FILE=release.keystore
# KEYSTORE_PASSWORD=your_keystore_password
# KEY_ALIAS=your_key_alias
# KEY_PASSWORD=your_key_password

# API配置
# API_BASE_URL=https://api.laundryhelp.com/v1/
# DEV_API_BASE_URL=https://dev-api.laundryhelp.com/v1/

# 第三方服务配置
# GOOGLE_MAPS_API_KEY=your_google_maps_api_key
# ALIPAY_APP_ID=your_alipay_app_id
# WECHAT_APP_ID=your_wechat_app_id
# FIREBASE_PROJECT_ID=your_firebase_project_id
