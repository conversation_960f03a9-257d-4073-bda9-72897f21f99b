package com.laundry.merchant.ui.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.animation.AnimationUtils
import androidx.recyclerview.widget.LinearLayoutManager
import com.laundry.merchant.R
import com.laundry.merchant.databinding.DialogWelcomeGiftBinding
import com.laundry.merchant.ui.dialog.adapter.GiftCouponAdapter
import com.laundry.merchant.utils.WelcomeGift

class WelcomeGiftDialog(
    context: Context,
    private val gift: WelcomeGift,
    private val onClaim: () -> Unit
) : Dialog(context, R.style.Dialog_FullScreen) {

    private lateinit var binding: DialogWelcomeGiftBinding
    private lateinit var couponAdapter: GiftCouponAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogWelcomeGiftBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupAnimation()
    }

    private fun setupViews() {
        // 设置不可取消
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        // 设置礼包内容
        binding.textViewDescription.text = gift.description
        binding.textViewPoints.text = "+${gift.points}积分"

        // 设置优惠券列表
        setupCouponList()

        // 设置按钮点击事件
        binding.buttonClaim.setOnClickListener {
            claimGift()
        }

        binding.buttonClose.setOnClickListener {
            dismiss()
        }
    }

    private fun setupCouponList() {
        couponAdapter = GiftCouponAdapter(gift.coupons)
        binding.recyclerViewCoupons.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = couponAdapter
        }
    }

    private fun setupAnimation() {
        // 添加入场动画
        val slideInAnimation = AnimationUtils.loadAnimation(context, R.anim.slide_in_bottom)
        binding.layoutContent.startAnimation(slideInAnimation)

        // 添加礼品盒动画
        val scaleAnimation = AnimationUtils.loadAnimation(context, R.anim.scale_bounce)
        binding.imageViewGiftBox.startAnimation(scaleAnimation)
    }

    private fun claimGift() {
        // 显示领取动画
        binding.buttonClaim.isEnabled = false
        binding.progressBar.visibility = View.VISIBLE
        binding.textViewClaimStatus.text = "正在领取..."

        // 模拟领取过程
        binding.root.postDelayed({
            binding.progressBar.visibility = View.GONE
            binding.textViewClaimStatus.text = "领取成功！"
            binding.buttonClaim.text = "已领取"
            
            // 调用回调
            onClaim()
            
            // 延迟关闭对话框
            binding.root.postDelayed({
                dismiss()
            }, 1500)
        }, 2000)
    }

    override fun show() {
        super.show()
        // 添加背景动画
        window?.decorView?.alpha = 0f
        window?.decorView?.animate()?.alpha(1f)?.setDuration(300)?.start()
    }

    override fun dismiss() {
        // 添加退出动画
        window?.decorView?.animate()?.alpha(0f)?.setDuration(200)?.withEndAction {
            super.dismiss()
        }?.start()
    }
}
