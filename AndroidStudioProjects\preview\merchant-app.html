<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗护帮 - 商家端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .phone-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .app-header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 20px 20px 100px;
        }
        
        .merchant-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .merchant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .merchant-name {
            font-weight: bold;
            font-size: 18px;
        }
        
        .merchant-ranking {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            text-align: center;
            background: #f8f9fa;
            padding: 12px;
            border-radius: 10px;
        }
        
        .stat-value {
            font-weight: bold;
            color: #2196F3;
            font-size: 16px;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 10px;
            color: #666;
        }
        
        .promotion-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .promotion-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .promotion-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .control-input {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 6px 10px;
            width: 70px;
            font-size: 12px;
        }
        
        .btn {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 12px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .ranking-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .ranking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .ranking-position {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .ranking-change {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: bold;
        }
        
        .ranking-change.up {
            background: #e8f5e8;
            color: #4caf50;
        }
        
        .ranking-change.down {
            background: #ffeaea;
            color: #f44336;
        }
        
        .competitor-list {
            margin-top: 15px;
        }
        
        .competitor-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .competitor-item:last-child {
            border-bottom: none;
        }
        
        .competitor-rank {
            background: #f0f0f0;
            color: #666;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .order-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .order-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .status-new {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .status-processing {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .status-completed {
            background: #e8f5e8;
            color: #4caf50;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 80px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-radius: 0 0 32px 32px;
        }
        
        .nav-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            padding: 8px;
        }
        
        .nav-item.active {
            color: #2196F3;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .chart-placeholder {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            color: #666;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🏪 商家管理</span>
                    <span>🔋100%</span>
                </div>
                
                <div class="app-header">
                    <div class="app-title">商家管理中心</div>
                    <div class="app-subtitle">投流竞价 · 排名提升</div>
                </div>
                
                <div class="content-area" id="contentArea">
                    <!-- 首页内容 -->
                    <div id="homeContent">
                        <div class="merchant-card">
                            <div class="merchant-header">
                                <div class="merchant-name">我的洗衣店</div>
                                <div class="merchant-ranking">排名 #3</div>
                            </div>
                            
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">¥1,280</div>
                                    <div class="stat-label">账户余额</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">156</div>
                                    <div class="stat-label">今日点击</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">3.2</div>
                                    <div class="stat-label">ROI</div>
                                </div>
                            </div>
                            
                            <div class="promotion-section">
                                <div class="promotion-title">🎯 推广设置</div>
                                <div class="promotion-controls">
                                    <span>关键词出价:</span>
                                    <input type="text" class="control-input" value="¥2.50">
                                </div>
                                <div class="promotion-controls">
                                    <span>日预算:</span>
                                    <input type="text" class="control-input" value="¥200">
                                </div>
                                <div class="promotion-controls">
                                    <span>投放状态:</span>
                                    <button class="btn">已启用</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="merchant-card">
                            <div class="section-title">📊 今日数据</div>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">12.5%</div>
                                    <div class="stat-label">转化率</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">¥1.85</div>
                                    <div class="stat-label">平均CPC</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">2,340</div>
                                    <div class="stat-label">总点击</div>
                                </div>
                            </div>
                            <div class="chart-placeholder">
                                📈 点击趋势图
                            </div>
                        </div>
                    </div>
                    
                    <!-- 排行榜内容 -->
                    <div id="rankingContent" style="display: none;">
                        <div class="section-title">
                            🏆 竞争排行榜
                        </div>
                        
                        <div class="ranking-card">
                            <div class="ranking-header">
                                <div>
                                    <div style="font-weight: bold;">我的排名</div>
                                    <div style="font-size: 12px; color: #666;">衣物洗护分类</div>
                                </div>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <div class="ranking-position">3</div>
                                    <div class="ranking-change up">↑ 1</div>
                                </div>
                            </div>
                            
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">4.8⭐</div>
                                    <div class="stat-label">评分</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">156</div>
                                    <div class="stat-label">订单数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">¥8,560</div>
                                    <div class="stat-label">月投入</div>
                                </div>
                            </div>
                            
                            <div class="competitor-list">
                                <div style="font-weight: bold; margin-bottom: 10px;">竞争对手</div>
                                <div class="competitor-item">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="competitor-rank">1</div>
                                        <span>优质洗衣店</span>
                                    </div>
                                    <span style="color: #2196F3; font-weight: bold;">¥12,340</span>
                                </div>
                                <div class="competitor-item">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="competitor-rank">2</div>
                                        <span>专业干洗店</span>
                                    </div>
                                    <span style="color: #2196F3; font-weight: bold;">¥9,870</span>
                                </div>
                                <div class="competitor-item">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="competitor-rank" style="background: #2196F3; color: white;">3</div>
                                        <span style="font-weight: bold;">我的洗衣店</span>
                                    </div>
                                    <span style="color: #2196F3; font-weight: bold;">¥8,560</span>
                                </div>
                                <div class="competitor-item">
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div class="competitor-rank">4</div>
                                        <span>快速洗护</span>
                                    </div>
                                    <span style="color: #2196F3; font-weight: bold;">¥7,230</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 订单内容 -->
                    <div id="orderContent" style="display: none;">
                        <div class="section-title">
                            📋 订单管理
                        </div>
                        
                        <div class="order-item">
                            <div class="order-header">
                                <div>
                                    <div style="font-weight: bold;">张三</div>
                                    <div style="font-size: 12px; color: #666;">订单号: LH202412120001</div>
                                </div>
                                <div class="order-status status-new">新订单</div>
                            </div>
                            <div style="margin-bottom: 8px;">衣物洗护 × 3件</div>
                            <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
                                <span>下单时间: 10:30</span>
                                <span style="color: #2196F3; font-weight: bold;">¥45</span>
                            </div>
                        </div>
                        
                        <div class="order-item">
                            <div class="order-header">
                                <div>
                                    <div style="font-weight: bold;">李四</div>
                                    <div style="font-size: 12px; color: #666;">订单号: LH202412120002</div>
                                </div>
                                <div class="order-status status-processing">服务中</div>
                            </div>
                            <div style="margin-bottom: 8px;">高端西装干洗 × 1件</div>
                            <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
                                <span>预计完成: 16:00</span>
                                <span style="color: #2196F3; font-weight: bold;">¥80</span>
                            </div>
                        </div>
                        
                        <div class="order-item">
                            <div class="order-header">
                                <div>
                                    <div style="font-weight: bold;">王五</div>
                                    <div style="font-size: 12px; color: #666;">订单号: LH202412110003</div>
                                </div>
                                <div class="order-status status-completed">已完成</div>
                            </div>
                            <div style="margin-bottom: 8px;">运动鞋清洗 × 2双</div>
                            <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666;">
                                <span>完成时间: 昨天 18:30</span>
                                <span style="color: #2196F3; font-weight: bold;">¥60</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 财务内容 -->
                    <div id="financeContent" style="display: none;">
                        <div class="section-title">
                            💰 财务管理
                        </div>
                        
                        <div class="merchant-card">
                            <div style="font-weight: bold; margin-bottom: 15px;">账户概览</div>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">¥1,280</div>
                                    <div class="stat-label">推广余额</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">¥3,560</div>
                                    <div class="stat-label">营业收入</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">¥156</div>
                                    <div class="stat-label">今日消费</div>
                                </div>
                            </div>
                            <button class="btn" style="width: 100%; margin-top: 15px;">立即充值</button>
                        </div>
                        
                        <div class="merchant-card">
                            <div style="font-weight: bold; margin-bottom: 15px;">消费记录</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">今日 12:30 - 关键词竞价 -¥12.50</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">今日 11:45 - 关键词竞价 -¥8.30</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">今日 10:20 - 关键词竞价 -¥15.60</div>
                            <div style="font-size: 12px; color: #666;">昨日 19:30 - 关键词竞价 -¥22.40</div>
                        </div>
                    </div>
                </div>
                
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="switchContent('home')">
                        <div class="nav-icon">🏠</div>
                        <div class="nav-label">首页</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('ranking')">
                        <div class="nav-icon">🏆</div>
                        <div class="nav-label">排行榜</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('order')">
                        <div class="nav-icon">📋</div>
                        <div class="nav-label">订单</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('finance')">
                        <div class="nav-icon">💰</div>
                        <div class="nav-label">财务</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchContent(content) {
            // 隐藏所有内容
            document.querySelectorAll('#contentArea > div').forEach(div => {
                div.style.display = 'none';
            });
            
            // 移除所有导航项的active状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示对应内容
            document.getElementById(content + 'Content').style.display = 'block';
            
            // 激活对应导航项
            event.target.closest('.nav-item').classList.add('active');
        }
        
        // 模拟实时数据更新
        function updateRealTimeData() {
            // 更新点击数
            const clickElements = document.querySelectorAll('.stat-value');
            clickElements.forEach(element => {
                if (element.textContent === '156') {
                    const newValue = 156 + Math.floor(Math.random() * 5);
                    element.textContent = newValue.toString();
                }
            });
            
            // 更新排名
            const rankingElements = document.querySelectorAll('.merchant-ranking');
            rankingElements.forEach(element => {
                if (Math.random() < 0.1) {
                    const currentRank = parseInt(element.textContent.match(/\d+/)[0]);
                    const change = Math.random() < 0.5 ? -1 : 1;
                    const newRank = Math.max(1, Math.min(10, currentRank + change));
                    element.textContent = `排名 #${newRank}`;
                }
            });
            
            // 更新余额
            const balanceElement = document.querySelector('.stat-value');
            if (balanceElement && balanceElement.textContent.includes('¥1,280')) {
                const currentBalance = 1280;
                const change = Math.floor(Math.random() * 20) - 10;
                const newBalance = Math.max(0, currentBalance + change);
                balanceElement.textContent = `¥${newBalance.toLocaleString()}`;
            }
        }
        
        // 每3秒更新一次数据
        setInterval(updateRealTimeData, 3000);
        
        // 推广设置按钮点击事件
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent === '已启用') {
                    this.textContent = '已暂停';
                    this.style.background = '#f44336';
                } else if (this.textContent === '已暂停') {
                    this.textContent = '已启用';
                    this.style.background = 'linear-gradient(45deg, #2196F3, #1976D2)';
                } else if (this.textContent === '立即充值') {
                    alert('跳转到充值页面');
                }
            });
        });
    </script>
</body>
</html>
