<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/swipeRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 统计卡片区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 今日订单 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardTodayOrders"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/textTodayOrders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="12"
                            android:textColor="@color/blue_500"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="今日订单"
                            android:textColor="@color/gray_600"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- 今日收入 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardTodayRevenue"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/textTodayRevenue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥580"
                            android:textColor="@color/green_500"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="今日收入"
                            android:textColor="@color/gray_600"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- 第二行统计 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <!-- 月度订单 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardMonthOrders"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/textMonthOrders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="156"
                            android:textColor="@color/purple_500"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="月度订单"
                            android:textColor="@color/gray_600"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- 月度收入 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardMonthRevenue"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/textMonthRevenue"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥7850"
                            android:textColor="@color/orange_500"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="月度收入"
                            android:textColor="@color/gray_600"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- 评分 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardRating"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_marginStart="4dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/textRating"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.6"
                            android:textColor="@color/red_500"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="评分"
                            android:textColor="@color/gray_600"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- 投流状态卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardPromotionStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="投流状态"
                        android:textColor="@color/gray_800"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/textPromotionStatus"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="投流中"
                            android:textColor="@color/green_500"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/textPromotionBalance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="余额: ¥1280"
                            android:textColor="@color/gray_600"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/textTodaySpent"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="今日消费: ¥156"
                        android:textColor="@color/gray_600"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/textCompletionRate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="完成率: 98%"
                        android:textColor="@color/gray_600"
                        android:textSize="12sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 快捷操作 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="8dp"
                android:text="快捷操作"
                android:textColor="@color/gray_800"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewQuickActions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal" />

            <!-- 最近订单 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:layout_marginBottom="8dp"
                android:text="最近订单"
                android:textColor="@color/gray_800"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewRecentOrders"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <!-- 空状态 -->
            <LinearLayout
                android:id="@+id/emptyView"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_empty_orders"
                    android:tint="@color/gray_400" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="暂无订单数据"
                    android:textColor="@color/gray_500"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
