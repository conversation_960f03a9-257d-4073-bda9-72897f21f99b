<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:orientation="vertical">

    <!-- 工具栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/colorPrimary"
        android:elevation="4dp"
        android:paddingHorizontal="16dp">

        <ImageView
            android:id="@+id/imageViewClose"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:src="@drawable/ic_close"
            app:tint="@android:color/white" />

        <TextView
            android:id="@+id/textViewTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="协议详情"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </RelativeLayout>

    <!-- 内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <TextView
            android:id="@+id/textViewContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:lineSpacingExtra="4dp"
            android:textColor="@color/gray_700"
            android:textSize="14sp" />

    </ScrollView>

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:elevation="4dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <Button
            android:id="@+id/buttonClose"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="@string/close" />

    </LinearLayout>

</LinearLayout>
