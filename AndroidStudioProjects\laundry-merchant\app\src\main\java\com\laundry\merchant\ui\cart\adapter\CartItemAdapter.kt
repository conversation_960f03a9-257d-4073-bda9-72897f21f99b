package com.laundry.merchant.ui.cart.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.data.model.CartItem
import com.laundry.merchant.databinding.ItemCartBinding
import com.laundry.merchant.utils.formatCurrency

class CartItemAdapter(
    private val onQuantityChanged: (CartItem, Int) -> Unit,
    private val onItemRemoved: (CartItem) -> Unit,
    private val onItemSelected: (CartItem, Boolean) -> Unit
) : ListAdapter<CartItemWithSelection, CartItemAdapter.ViewHolder>(CartItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemCartBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<CartItemWithSelection>) {
        submitList(newData)
    }

    inner class ViewHolder(private val binding: ItemCartBinding) : 
        RecyclerView.ViewHolder(binding.root) {

        fun bind(itemWithSelection: CartItemWithSelection) {
            val item = itemWithSelection.item
            val isSelected = itemWithSelection.isSelected
            
            // 设置商品信息
            binding.textViewProductName.text = item.productName
            binding.textViewCurrentPrice.text = item.currentPrice.formatCurrency()
            binding.textViewQuantity.text = item.quantity.toString()
            
            // 设置原价（如果有折扣）
            if (item.originalPrice > item.currentPrice) {
                binding.textViewOriginalPrice.visibility = View.VISIBLE
                binding.textViewOriginalPrice.text = item.originalPrice.formatCurrency()
            } else {
                binding.textViewOriginalPrice.visibility = View.GONE
            }
            
            // 设置总价
            binding.textViewTotalPrice.text = item.getTotalPrice().formatCurrency()
            
            // 设置规格信息
            if (item.selectedSpecs.isNotEmpty()) {
                binding.textViewSpecs.visibility = View.VISIBLE
                binding.textViewSpecs.text = item.selectedSpecs.values.joinToString(", ")
            } else {
                binding.textViewSpecs.visibility = View.GONE
            }
            
            // 设置库存状态
            when {
                !item.isAvailable -> {
                    binding.textViewStatus.visibility = View.VISIBLE
                    binding.textViewStatus.text = "商品已下架"
                    binding.textViewStatus.setTextColor(
                        binding.root.context.getColor(android.R.color.holo_red_dark)
                    )
                }
                item.isOutOfStock() -> {
                    binding.textViewStatus.visibility = View.VISIBLE
                    binding.textViewStatus.text = "库存不足"
                    binding.textViewStatus.setTextColor(
                        binding.root.context.getColor(android.R.color.holo_orange_dark)
                    )
                }
                else -> {
                    binding.textViewStatus.visibility = View.GONE
                }
            }
            
            // 设置选择状态
            binding.checkboxSelect.isChecked = isSelected
            binding.checkboxSelect.setOnCheckedChangeListener { _, checked ->
                onItemSelected(item, checked)
            }
            
            // 设置数量控制
            binding.buttonDecrease.isEnabled = item.canDecreaseQuantity()
            binding.buttonIncrease.isEnabled = item.canIncreaseQuantity()
            
            binding.buttonDecrease.setOnClickListener {
                if (item.canDecreaseQuantity()) {
                    onQuantityChanged(item, item.quantity - 1)
                }
            }
            
            binding.buttonIncrease.setOnClickListener {
                if (item.canIncreaseQuantity()) {
                    onQuantityChanged(item, item.quantity + 1)
                }
            }
            
            // 设置删除按钮
            binding.buttonRemove.setOnClickListener {
                onItemRemoved(item)
            }
            
            // TODO: 加载商品图片
            // Glide.with(binding.imageViewProduct.context)
            //     .load(item.productImage)
            //     .placeholder(R.drawable.placeholder_product)
            //     .into(binding.imageViewProduct)
        }
    }

    private class CartItemDiffCallback : DiffUtil.ItemCallback<CartItemWithSelection>() {
        override fun areItemsTheSame(
            oldItem: CartItemWithSelection,
            newItem: CartItemWithSelection
        ): Boolean {
            return oldItem.item.id == newItem.item.id
        }

        override fun areContentsTheSame(
            oldItem: CartItemWithSelection,
            newItem: CartItemWithSelection
        ): Boolean {
            return oldItem == newItem
        }
    }
}

data class CartItemWithSelection(
    val item: CartItem,
    val isSelected: Boolean = true
)
