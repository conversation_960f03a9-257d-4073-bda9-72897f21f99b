plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'kotlin-parcelize'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
}

android {
    namespace 'com.laundry.user'
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        applicationId "com.laundry.user"
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // 多语言支持
        resConfigs "zh", "en"
        
        // 构建配置字段
        buildConfigField "String", "API_BASE_URL", "\"https://api.laundryhelp.com/v1/\""
        buildConfigField "String", "DEV_API_BASE_URL", "\"https://dev-api.laundryhelp.com/v1/\""
        buildConfigField "boolean", "ENABLE_LOGGING", "true"
        
        // 原生库架构
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
            buildConfigField "boolean", "DEBUG", "true"
            buildConfigField "String", "ENVIRONMENT", "\"development\""
            
            // 签名配置
            signingConfig signingConfigs.debug
            
            // ProGuard配置
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            buildConfigField "boolean", "DEBUG", "false"
            buildConfigField "String", "ENVIRONMENT", "\"production\""
            
            // 签名配置（需要配置release keystore）
            // signingConfig signingConfigs.release
            
            // ProGuard配置
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        
        staging {
            debuggable true
            minifyEnabled false
            shrinkResources false
            buildConfigField "boolean", "DEBUG", "true"
            buildConfigField "String", "ENVIRONMENT", "\"staging\""
            buildConfigField "String", "API_BASE_URL", "\"https://staging-api.laundryhelp.com/v1/\""
            
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
        freeCompilerArgs += [
            "-opt-in=kotlin.RequiresOptIn",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api"
        ]
    }
    
    buildFeatures {
        buildConfig true
        viewBinding true
        dataBinding true
        compose true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion rootProject.ext.compose_compiler_version
    }
    
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            excludes += '/META-INF/DEPENDENCIES'
            excludes += '/META-INF/LICENSE'
            excludes += '/META-INF/LICENSE.txt'
            excludes += '/META-INF/NOTICE'
            excludes += '/META-INF/NOTICE.txt'
        }
    }
    
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
    }
}

dependencies {
    // Core Android
    implementation "androidx.core:core-ktx:$rootProject.ext.coreKtxVersion"
    implementation "androidx.appcompat:appcompat:$rootProject.ext.appCompatVersion"
    implementation "com.google.android.material:material:$rootProject.ext.materialVersion"
    implementation "androidx.constraintlayout:constraintlayout:$rootProject.ext.constraintLayoutVersion"
    
    // Lifecycle
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$rootProject.ext.lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$rootProject.ext.lifecycleVersion"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$rootProject.ext.lifecycleVersion"
    
    // Activity & Fragment
    implementation "androidx.activity:activity-compose:$rootProject.ext.activityComposeVersion"
    implementation "androidx.fragment:fragment-ktx:$rootProject.ext.fragmentVersion"
    
    // Navigation
    implementation "androidx.navigation:navigation-fragment-ktx:$rootProject.ext.navigationVersion"
    implementation "androidx.navigation:navigation-ui-ktx:$rootProject.ext.navigationVersion"
    implementation "androidx.navigation:navigation-compose:$rootProject.ext.navigationVersion"
    
    // Compose
    implementation platform("androidx.compose:compose-bom:2023.10.01")
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-tooling-preview"
    implementation "androidx.compose.material3:material3"
    implementation "androidx.compose.runtime:runtime-livedata"
    
    // RecyclerView & UI Components
    implementation "androidx.recyclerview:recyclerview:$rootProject.ext.recyclerViewVersion"
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:$rootProject.ext.swipeRefreshLayoutVersion"
    implementation "androidx.viewpager2:viewpager2:$rootProject.ext.viewPager2Version"
    
    // Dependency Injection
    implementation "com.google.dagger:hilt-android:$rootProject.ext.hilt_version"
    kapt "com.google.dagger:hilt-compiler:$rootProject.ext.hilt_version"
    
    // Network
    implementation "com.squareup.retrofit2:retrofit:$rootProject.ext.retrofitVersion"
    implementation "com.squareup.retrofit2:converter-gson:$rootProject.ext.retrofitVersion"
    implementation "com.squareup.okhttp3:okhttp:$rootProject.ext.okhttpVersion"
    implementation "com.squareup.okhttp3:logging-interceptor:$rootProject.ext.okhttpVersion"
    implementation "com.google.code.gson:gson:$rootProject.ext.gsonVersion"
    
    // Database
    implementation "androidx.room:room-runtime:$rootProject.ext.roomVersion"
    implementation "androidx.room:room-ktx:$rootProject.ext.roomVersion"
    kapt "androidx.room:room-compiler:$rootProject.ext.roomVersion"
    
    // Image Loading
    implementation "com.github.bumptech.glide:glide:$rootProject.ext.glideVersion"
    kapt "com.github.bumptech.glide:compiler:$rootProject.ext.glideVersion"
    
    // Work Manager
    implementation "androidx.work:work-runtime-ktx:$rootProject.ext.workManagerVersion"
    
    // Logging
    implementation "com.jakewharton.timber:timber:$rootProject.ext.timberVersion"
    
    // Google Play Services
    implementation "com.google.android.gms:play-services-location:$rootProject.ext.playServicesLocationVersion"
    implementation "com.google.android.gms:play-services-maps:$rootProject.ext.playServicesMapsVersion"
    
    // Firebase
    implementation platform("com.google.firebase:firebase-bom:$rootProject.ext.firebaseBomVersion")
    implementation "com.google.firebase:firebase-analytics-ktx"
    implementation "com.google.firebase:firebase-crashlytics-ktx"
    implementation "com.google.firebase:firebase-messaging-ktx"
    implementation "com.google.firebase:firebase-config-ktx"
    
    // Payment SDKs (需要手动添加AAR文件)
    // implementation files('libs/alipaySdk-15.8.11-20221027210830.aar')
    // implementation files('libs/wechat-sdk-android-without-mta-6.8.0.aar')
    
    // Utilities
    implementation 'com.blankj:utilcodex:1.31.1'
    implementation 'com.tencent:mmkv-static:1.3.1'
    implementation 'com.airbnb.android:lottie:6.1.0'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    
    // Desugaring
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    
    // Testing
    testImplementation "junit:junit:$rootProject.ext.junitVersion"
    testImplementation "org.mockito:mockito-core:$rootProject.ext.mockitoVersion"
    testImplementation "androidx.arch.core:core-testing:2.2.0"
    testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3"
    
    androidTestImplementation "androidx.test.ext:junit:$rootProject.ext.androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.ext.espressoVersion"
    androidTestImplementation "androidx.compose.ui:ui-test-junit4"
    
    debugImplementation "androidx.compose.ui:ui-tooling"
    debugImplementation "androidx.compose.ui:ui-test-manifest"
}
