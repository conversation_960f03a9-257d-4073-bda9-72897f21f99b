package com.laundry.merchant.utils

import android.content.Context
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.laundry.merchant.data.local.PreferencesManager
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AccountSecurityManager @Inject constructor(
    private val preferencesManager: PreferencesManager
) {

    companion object {
        private const val MAX_LOGIN_ATTEMPTS = 5
        private const val LOCK_DURATION_MINUTES = 30
        private val LOGIN_ATTEMPTS_KEY = intPreferencesKey("login_attempts")
        private val LAST_FAILED_LOGIN_KEY = longPreferencesKey("last_failed_login")
        private val ACCOUNT_LOCKED_KEY = stringPreferencesKey("account_locked")
    }

    /**
     * 记录登录失败
     */
    suspend fun recordLoginFailure(account: String): LoginSecurityResult {
        val currentAttempts = getLoginAttempts(account)
        val newAttempts = currentAttempts + 1
        
        // 保存失败次数和时间
        saveLoginAttempts(account, newAttempts)
        saveLastFailedLoginTime(account, System.currentTimeMillis())
        
        return if (newAttempts >= MAX_LOGIN_ATTEMPTS) {
            // 锁定账号
            lockAccount(account)
            LoginSecurityResult.AccountLocked(getRemainingLockTime(account))
        } else {
            val remainingAttempts = MAX_LOGIN_ATTEMPTS - newAttempts
            LoginSecurityResult.AttemptsRemaining(remainingAttempts)
        }
    }

    /**
     * 记录登录成功
     */
    suspend fun recordLoginSuccess(account: String) {
        // 清除失败记录
        clearLoginAttempts(account)
        clearAccountLock(account)
    }

    /**
     * 检查账号是否被锁定
     */
    suspend fun isAccountLocked(account: String): Boolean {
        val lockTime = getAccountLockTime(account)
        if (lockTime == 0L) return false
        
        val currentTime = System.currentTimeMillis()
        val lockDuration = LOCK_DURATION_MINUTES * 60 * 1000L
        
        return if (currentTime - lockTime < lockDuration) {
            true
        } else {
            // 锁定时间已过，自动解锁
            clearAccountLock(account)
            false
        }
    }

    /**
     * 获取剩余锁定时间（分钟）
     */
    suspend fun getRemainingLockTime(account: String): Long {
        val lockTime = getAccountLockTime(account)
        if (lockTime == 0L) return 0L
        
        val currentTime = System.currentTimeMillis()
        val lockDuration = LOCK_DURATION_MINUTES * 60 * 1000L
        val elapsed = currentTime - lockTime
        
        return if (elapsed < lockDuration) {
            (lockDuration - elapsed) / (60 * 1000L)
        } else {
            0L
        }
    }

    /**
     * 通过验证码解锁账号
     */
    suspend fun unlockAccountWithVerificationCode(account: String, verificationCode: String): Boolean {
        // TODO: 验证验证码
        // 这里应该调用服务器验证验证码
        
        // 模拟验证成功
        if (verificationCode.length == 6) {
            clearAccountLock(account)
            clearLoginAttempts(account)
            return true
        }
        
        return false
    }

    /**
     * 获取登录失败次数
     */
    private suspend fun getLoginAttempts(account: String): Int {
        return try {
            // 这里简化处理，实际应该按账号分别存储
            preferencesManager.getDataStore().data.first()[LOGIN_ATTEMPTS_KEY] ?: 0
        } catch (e: Exception) {
            0
        }
    }

    /**
     * 保存登录失败次数
     */
    private suspend fun saveLoginAttempts(account: String, attempts: Int) {
        preferencesManager.getDataStore().edit { preferences ->
            preferences[LOGIN_ATTEMPTS_KEY] = attempts
        }
    }

    /**
     * 清除登录失败次数
     */
    private suspend fun clearLoginAttempts(account: String) {
        preferencesManager.getDataStore().edit { preferences ->
            preferences.remove(LOGIN_ATTEMPTS_KEY)
        }
    }

    /**
     * 保存最后失败登录时间
     */
    private suspend fun saveLastFailedLoginTime(account: String, time: Long) {
        preferencesManager.getDataStore().edit { preferences ->
            preferences[LAST_FAILED_LOGIN_KEY] = time
        }
    }

    /**
     * 锁定账号
     */
    private suspend fun lockAccount(account: String) {
        preferencesManager.getDataStore().edit { preferences ->
            preferences[ACCOUNT_LOCKED_KEY] = "${account}_${System.currentTimeMillis()}"
        }
    }

    /**
     * 获取账号锁定时间
     */
    private suspend fun getAccountLockTime(account: String): Long {
        return try {
            val lockInfo = preferencesManager.getDataStore().data.first()[ACCOUNT_LOCKED_KEY]
            if (lockInfo?.startsWith(account) == true) {
                lockInfo.substringAfter("_").toLongOrNull() ?: 0L
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * 清除账号锁定
     */
    private suspend fun clearAccountLock(account: String) {
        preferencesManager.getDataStore().edit { preferences ->
            preferences.remove(ACCOUNT_LOCKED_KEY)
        }
    }

    /**
     * 获取具体的登录错误信息
     */
    fun getLoginErrorMessage(errorCode: String): String {
        return when (errorCode) {
            "USER_NOT_FOUND" -> "账号未注册"
            "INVALID_PASSWORD" -> "密码错误"
            "ACCOUNT_DISABLED" -> "账号已被禁用"
            "ACCOUNT_LOCKED" -> "账号已被锁定"
            "TOKEN_EXPIRED" -> "登录已过期，请重新登录"
            "NETWORK_ERROR" -> "网络连接失败"
            "SERVER_ERROR" -> "服务器错误"
            else -> "登录失败，请稍后重试"
        }
    }
}

sealed class LoginSecurityResult {
    data class AttemptsRemaining(val remainingAttempts: Int) : LoginSecurityResult()
    data class AccountLocked(val remainingLockTimeMinutes: Long) : LoginSecurityResult()
}
