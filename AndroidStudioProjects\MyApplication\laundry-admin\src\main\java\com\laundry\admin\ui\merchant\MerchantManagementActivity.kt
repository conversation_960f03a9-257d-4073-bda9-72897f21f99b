package com.laundry.admin.ui.merchant

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.tabs.TabLayout
import com.laundry.admin.R
import com.laundry.admin.data.model.ApprovalStatus
import com.laundry.admin.databinding.ActivityMerchantManagementBinding
import com.laundry.admin.ui.merchant.adapter.MerchantManagementAdapter
import com.laundry.admin.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MerchantManagementActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMerchantManagementBinding
    private val viewModel: MerchantManagementViewModel by viewModels()
    
    private lateinit var merchantAdapter: MerchantManagementAdapter
    private var currentFilter = ApprovalStatus.APPROVED

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMerchantManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerView()
        setupTabs()
        observeViewModel()
        
        // 处理从告警跳转的情况
        val alertId = intent.getStringExtra("alert_id")
        if (alertId != null) {
            viewModel.handleAlert(alertId)
        } else {
            viewModel.loadMerchants(currentFilter)
        }
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "商家管理"

        // 设置刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.loadMerchants(currentFilter)
        }

        // 设置浮动按钮
        binding.fabBatchApproval.setOnClickListener {
            showBatchApprovalDialog()
        }
    }

    private fun setupRecyclerView() {
        merchantAdapter = MerchantManagementAdapter(
            onMerchantClick = { merchant ->
                showMerchantDetailDialog(merchant)
            },
            onApprovalAction = { merchant, action ->
                when (action) {
                    "approve" -> showApprovalDialog(merchant, ApprovalStatus.APPROVED)
                    "reject" -> showApprovalDialog(merchant, ApprovalStatus.REJECTED)
                    "suspend" -> showApprovalDialog(merchant, ApprovalStatus.SUSPENDED)
                }
            },
            onViewDocuments = { merchant ->
                showDocumentsDialog(merchant)
            },
            onViewMetrics = { merchant ->
                showBusinessMetricsDialog(merchant)
            }
        )
        
        binding.recyclerViewMerchants.apply {
            layoutManager = LinearLayoutManager(this@MerchantManagementActivity)
            adapter = merchantAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("已通过"))
            addTab(newTab().setText("待审核"))
            addTab(newTab().setText("审核中"))
            addTab(newTab().setText("已拒绝"))
            addTab(newTab().setText("已暂停"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    currentFilter = when (tab?.position) {
                        0 -> ApprovalStatus.APPROVED
                        1 -> ApprovalStatus.PENDING
                        2 -> ApprovalStatus.UNDER_REVIEW
                        3 -> ApprovalStatus.REJECTED
                        4 -> ApprovalStatus.SUSPENDED
                        else -> ApprovalStatus.APPROVED
                    }
                    viewModel.loadMerchants(currentFilter)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_merchant_management, menu)
        
        // 设置搜索
        val searchItem = menu?.findItem(R.id.action_search)
        val searchView = searchItem?.actionView as? SearchView
        searchView?.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { viewModel.searchMerchants(it) }
                return true
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                if (newText.isNullOrEmpty()) {
                    viewModel.loadMerchants(currentFilter)
                }
                return true
            }
        })
        
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_filter -> {
                showFilterDialog()
                true
            }
            R.id.action_export -> {
                showExportDialog()
                true
            }
            R.id.action_audit_log -> {
                showAuditLogDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: MerchantManagementUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新商家列表
        merchantAdapter.updateData(state.merchants)

        // 更新统计信息
        binding.textViewTotalMerchants.text = "总商家: ${state.totalMerchants}"
        binding.textViewPendingApproval.text = "待审核: ${state.pendingApproval}"
        binding.textViewApproved.text = "已通过: ${state.approved}"
        binding.textViewSuspended.text = "已暂停: ${state.suspended}"

        // 更新空状态
        if (state.merchants.isEmpty() && !state.isLoading) {
            binding.layoutEmpty.visibility = View.VISIBLE
            binding.recyclerViewMerchants.visibility = View.GONE
        } else {
            binding.layoutEmpty.visibility = View.GONE
            binding.recyclerViewMerchants.visibility = View.VISIBLE
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: MerchantManagementEvent) {
        when (event) {
            is MerchantManagementEvent.ShowError -> {
                showError(event.message)
            }
            is MerchantManagementEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is MerchantManagementEvent.ShowMerchantDetail -> {
                showMerchantDetailDialog(event.merchant)
            }
            is MerchantManagementEvent.ApprovalCompleted -> {
                showToast("审核操作已完成")
                viewModel.loadMerchants(currentFilter)
            }
        }
    }

    private fun showMerchantDetailDialog(merchant: com.laundry.admin.data.model.MerchantManagement) {
        val dialog = MerchantDetailDialog.newInstance(merchant)
        dialog.show(supportFragmentManager, "MerchantDetailDialog")
    }

    private fun showApprovalDialog(merchant: com.laundry.admin.data.model.MerchantManagement, newStatus: ApprovalStatus) {
        val statusText = when (newStatus) {
            ApprovalStatus.APPROVED -> "通过"
            ApprovalStatus.REJECTED -> "拒绝"
            ApprovalStatus.SUSPENDED -> "暂停"
            else -> "更改状态"
        }
        
        MaterialAlertDialogBuilder(this)
            .setTitle("确认操作")
            .setMessage("确定要${statusText}商家 ${merchant.merchant.businessName} 吗？")
            .setPositiveButton("确定") { _, _ ->
                showApprovalReasonDialog(merchant, newStatus)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showApprovalReasonDialog(merchant: com.laundry.admin.data.model.MerchantManagement, newStatus: ApprovalStatus) {
        val dialog = ApprovalReasonDialog.newInstance(merchant.merchant.id, newStatus) { reason ->
            viewModel.updateMerchantStatus(merchant.merchant.id, newStatus, reason)
        }
        dialog.show(supportFragmentManager, "ApprovalReasonDialog")
    }

    private fun showDocumentsDialog(merchant: com.laundry.admin.data.model.MerchantManagement) {
        val dialog = MerchantDocumentsDialog.newInstance(merchant.documents)
        dialog.show(supportFragmentManager, "MerchantDocumentsDialog")
    }

    private fun showBusinessMetricsDialog(merchant: com.laundry.admin.data.model.MerchantManagement) {
        val dialog = BusinessMetricsDialog.newInstance(merchant.businessMetrics)
        dialog.show(supportFragmentManager, "BusinessMetricsDialog")
    }

    private fun showBatchApprovalDialog() {
        val selectedMerchants = merchantAdapter.getSelectedMerchants()
        if (selectedMerchants.isEmpty()) {
            showToast("请先选择要操作的商家")
            return
        }
        
        val operations = arrayOf("批量通过", "批量拒绝", "批量暂停")
        
        MaterialAlertDialogBuilder(this)
            .setTitle("批量审核")
            .setItems(operations) { _, which ->
                val newStatus = when (which) {
                    0 -> ApprovalStatus.APPROVED
                    1 -> ApprovalStatus.REJECTED
                    2 -> ApprovalStatus.SUSPENDED
                    else -> return@setItems
                }
                
                showBatchApprovalConfirmDialog(selectedMerchants, newStatus)
            }
            .show()
    }

    private fun showBatchApprovalConfirmDialog(
        merchants: List<com.laundry.admin.data.model.MerchantManagement>,
        newStatus: ApprovalStatus
    ) {
        val statusText = when (newStatus) {
            ApprovalStatus.APPROVED -> "通过"
            ApprovalStatus.REJECTED -> "拒绝"
            ApprovalStatus.SUSPENDED -> "暂停"
            else -> "更改状态"
        }
        
        MaterialAlertDialogBuilder(this)
            .setTitle("批量${statusText}")
            .setMessage("确定要${statusText} ${merchants.size} 个商家吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.batchUpdateMerchantStatus(
                    merchants.map { it.merchant.id },
                    newStatus,
                    "批量操作"
                )
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showFilterDialog() {
        val filters = arrayOf("全部", "高评分", "低评分", "新申请", "有违规记录", "高收入")
        var selectedFilter = 0
        
        MaterialAlertDialogBuilder(this)
            .setTitle("筛选条件")
            .setSingleChoiceItems(filters, selectedFilter) { _, which ->
                selectedFilter = which
            }
            .setPositiveButton("确定") { _, _ ->
                applyFilter(selectedFilter)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showExportDialog() {
        val exportTypes = arrayOf("当前列表", "全部商家", "选中商家", "审核报告")
        
        MaterialAlertDialogBuilder(this)
            .setTitle("导出数据")
            .setItems(exportTypes) { _, which ->
                when (which) {
                    0 -> viewModel.exportCurrentList()
                    1 -> viewModel.exportAllMerchants()
                    2 -> exportSelectedMerchants()
                    3 -> viewModel.exportAuditReport()
                }
            }
            .show()
    }

    private fun showAuditLogDialog() {
        val dialog = AuditLogDialog.newInstance("merchant_management")
        dialog.show(supportFragmentManager, "AuditLogDialog")
    }

    private fun exportSelectedMerchants() {
        val selectedMerchants = merchantAdapter.getSelectedMerchants()
        if (selectedMerchants.isEmpty()) {
            showToast("请先选择要导出的商家")
            return
        }
        
        viewModel.exportSelectedMerchants(selectedMerchants.map { it.merchant.id })
    }

    private fun applyFilter(filterIndex: Int) {
        when (filterIndex) {
            0 -> viewModel.loadMerchants(currentFilter)
            1 -> viewModel.loadHighRatedMerchants()
            2 -> viewModel.loadLowRatedMerchants()
            3 -> viewModel.loadNewApplications()
            4 -> viewModel.loadMerchantsWithViolations()
            5 -> viewModel.loadHighRevenueMerchants()
        }
    }

    private fun showError(message: String) {
        showToast(message)
    }
}
