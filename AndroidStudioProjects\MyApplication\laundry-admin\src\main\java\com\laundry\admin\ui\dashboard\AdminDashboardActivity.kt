package com.laundry.admin.ui.dashboard

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.laundry.admin.R
import com.laundry.admin.databinding.ActivityAdminDashboardBinding
import com.laundry.admin.ui.merchant.MerchantManagementActivity
import com.laundry.admin.ui.order.OrderManagementActivity
import com.laundry.admin.ui.user.UserManagementActivity
import com.laundry.admin.ui.finance.FinanceManagementActivity
import com.laundry.admin.ui.system.SystemConfigActivity
import com.laundry.admin.ui.dashboard.adapter.QuickStatsAdapter
import com.laundry.admin.ui.dashboard.adapter.RecentAlertsAdapter
import com.laundry.admin.utils.formatCurrency
import com.laundry.admin.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class AdminDashboardActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAdminDashboardBinding
    private val viewModel: AdminDashboardViewModel by viewModels()
    
    private lateinit var quickStatsAdapter: QuickStatsAdapter
    private lateinit var recentAlertsAdapter: RecentAlertsAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAdminDashboardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerViews()
        setupTabs()
        observeViewModel()
        
        // 加载仪表板数据
        viewModel.loadDashboardData()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.title = "管理后台"

        // 设置刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.loadDashboardData()
        }

        // 设置快捷操作按钮
        binding.cardUserManagement.setOnClickListener {
            startActivity(Intent(this, UserManagementActivity::class.java))
        }

        binding.cardMerchantManagement.setOnClickListener {
            startActivity(Intent(this, MerchantManagementActivity::class.java))
        }

        binding.cardOrderManagement.setOnClickListener {
            startActivity(Intent(this, OrderManagementActivity::class.java))
        }

        binding.cardFinanceManagement.setOnClickListener {
            startActivity(Intent(this, FinanceManagementActivity::class.java))
        }

        binding.cardSystemConfig.setOnClickListener {
            startActivity(Intent(this, SystemConfigActivity::class.java))
        }

        // 设置查看详情按钮
        binding.buttonViewUserDetails.setOnClickListener {
            startActivity(Intent(this, UserManagementActivity::class.java))
        }

        binding.buttonViewMerchantDetails.setOnClickListener {
            startActivity(Intent(this, MerchantManagementActivity::class.java))
        }

        binding.buttonViewOrderDetails.setOnClickListener {
            startActivity(Intent(this, OrderManagementActivity::class.java))
        }
    }

    private fun setupRecyclerViews() {
        // 快速统计
        quickStatsAdapter = QuickStatsAdapter()
        binding.recyclerViewQuickStats.apply {
            layoutManager = LinearLayoutManager(this@AdminDashboardActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = quickStatsAdapter
        }

        // 最近告警
        recentAlertsAdapter = RecentAlertsAdapter { alert ->
            // 处理告警点击
            handleAlertClick(alert)
        }
        binding.recyclerViewRecentAlerts.apply {
            layoutManager = LinearLayoutManager(this@AdminDashboardActivity)
            adapter = recentAlertsAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("今日"))
            addTab(newTab().setText("本周"))
            addTab(newTab().setText("本月"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    val period = when (tab?.position) {
                        0 -> "today"
                        1 -> "week"
                        2 -> "month"
                        else -> "today"
                    }
                    viewModel.loadDashboardData(period)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: AdminDashboardUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新概览数据
        state.overviewData?.let { overview ->
            binding.textViewTotalUsers.text = "${overview.totalUsers}"
            binding.textViewActiveUsers.text = "${overview.activeUsers}"
            binding.textViewTotalMerchants.text = "${overview.totalMerchants}"
            binding.textViewActiveMerchants.text = "${overview.activeMerchants}"
            binding.textViewTotalOrders.text = "${overview.totalOrders}"
            binding.textViewPendingOrders.text = "${overview.pendingOrders}"
            binding.textViewTotalRevenue.text = overview.totalRevenue.formatCurrency()
            binding.textViewPlatformRevenue.text = overview.platformRevenue.formatCurrency()
        }

        // 更新用户统计
        state.userStats?.let { stats ->
            binding.textViewNewUsers.text = "${stats.newUsers}"
            binding.textViewUserGrowthRate.text = "${String.format("%.1f", stats.growthRate)}%"
            binding.progressBarUserGrowth.progress = (stats.growthRate * 10).toInt()
        }

        // 更新商家统计
        state.merchantStats?.let { stats ->
            binding.textViewNewMerchants.text = "${stats.newMerchants}"
            binding.textViewMerchantGrowthRate.text = "${String.format("%.1f", stats.growthRate)}%"
            binding.progressBarMerchantGrowth.progress = (stats.growthRate * 10).toInt()
            binding.textViewPendingApprovals.text = "${stats.pendingApprovals}"
        }

        // 更新订单统计
        state.orderStats?.let { stats ->
            binding.textViewOrderGrowthRate.text = "${String.format("%.1f", stats.growthRate)}%"
            binding.progressBarOrderGrowth.progress = (stats.growthRate * 10).toInt()
            binding.textViewAverageOrderValue.text = stats.averageOrderValue.formatCurrency()
            binding.textViewCompletionRate.text = "${String.format("%.1f", stats.completionRate)}%"
        }

        // 更新快速统计
        quickStatsAdapter.updateData(state.quickStats)

        // 更新最近告警
        recentAlertsAdapter.updateData(state.recentAlerts)

        // 更新系统状态
        state.systemStatus?.let { status ->
            updateSystemStatus(status)
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun updateSystemStatus(status: SystemStatus) {
        // 更新系统健康状态
        val healthColor = when (status.healthStatus) {
            "healthy" -> R.color.green_500
            "warning" -> R.color.orange_500
            "critical" -> R.color.red_500
            else -> R.color.gray_500
        }
        
        binding.viewSystemHealthIndicator.setBackgroundResource(healthColor)
        binding.textViewSystemHealth.text = when (status.healthStatus) {
            "healthy" -> "系统正常"
            "warning" -> "系统警告"
            "critical" -> "系统异常"
            else -> "状态未知"
        }

        // 更新服务器状态
        binding.textViewServerLoad.text = "${status.serverLoad}%"
        binding.progressBarServerLoad.progress = status.serverLoad
        
        binding.textViewMemoryUsage.text = "${status.memoryUsage}%"
        binding.progressBarMemoryUsage.progress = status.memoryUsage
        
        binding.textViewDiskUsage.text = "${status.diskUsage}%"
        binding.progressBarDiskUsage.progress = status.diskUsage
    }

    private fun handleEvent(event: AdminDashboardEvent) {
        when (event) {
            is AdminDashboardEvent.ShowError -> {
                showError(event.message)
            }
            is AdminDashboardEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is AdminDashboardEvent.NavigateToAlert -> {
                // 跳转到相应的告警处理页面
                handleAlertNavigation(event.alertType, event.alertId)
            }
        }
    }

    private fun handleAlertClick(alert: SystemAlert) {
        when (alert.type) {
            "user_violation" -> {
                val intent = Intent(this, UserManagementActivity::class.java)
                intent.putExtra("alert_id", alert.id)
                startActivity(intent)
            }
            "merchant_violation" -> {
                val intent = Intent(this, MerchantManagementActivity::class.java)
                intent.putExtra("alert_id", alert.id)
                startActivity(intent)
            }
            "order_dispute" -> {
                val intent = Intent(this, OrderManagementActivity::class.java)
                intent.putExtra("alert_id", alert.id)
                startActivity(intent)
            }
            "system_error" -> {
                val intent = Intent(this, SystemConfigActivity::class.java)
                intent.putExtra("alert_id", alert.id)
                startActivity(intent)
            }
        }
    }

    private fun handleAlertNavigation(alertType: String, alertId: String) {
        // 根据告警类型跳转到相应页面
        handleAlertClick(SystemAlert(alertId, alertType, "", "", "", System.currentTimeMillis()))
    }

    private fun showError(message: String) {
        showToast(message)
    }
}
