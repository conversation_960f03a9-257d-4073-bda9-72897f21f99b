package com.laundry.merchant.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.google.gson.Gson
import com.laundry.merchant.R
import com.laundry.merchant.ui.main.MainActivity
import com.laundry.merchant.websocket.*
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class RealTimeUpdateService : Service() {

    @Inject
    lateinit var webSocketManager: WebSocketManager
    
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val gson = Gson()
    
    private val _orderUpdates = MutableSharedFlow<OrderStatusUpdateData>()
    val orderUpdates: SharedFlow<OrderStatusUpdateData> = _orderUpdates.asSharedFlow()
    
    private val _bookingUpdates = MutableSharedFlow<BookingStatusUpdateData>()
    val bookingUpdates: SharedFlow<BookingStatusUpdateData> = _bookingUpdates.asSharedFlow()
    
    private val _merchantUpdates = MutableSharedFlow<MerchantStatusUpdateData>()
    val merchantUpdates: SharedFlow<MerchantStatusUpdateData> = _merchantUpdates.asSharedFlow()
    
    private val _deliveryUpdates = MutableSharedFlow<DeliveryStatusUpdateData>()
    val deliveryUpdates: SharedFlow<DeliveryStatusUpdateData> = _deliveryUpdates.asSharedFlow()
    
    private val _systemNotifications = MutableSharedFlow<SystemNotificationData>()
    val systemNotifications: SharedFlow<SystemNotificationData> = _systemNotifications.asSharedFlow()

    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "realtime_updates"
        private const val CHANNEL_NAME = "实时更新"
        
        fun start(context: Context) {
            val intent = Intent(context, RealTimeUpdateService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stop(context: Context) {
            val intent = Intent(context, RealTimeUpdateService::class.java)
            context.stopService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 监听WebSocket消息
        scope.launch {
            webSocketManager.messages.collect { message ->
                handleWebSocketMessage(message)
            }
        }
        
        // 监听连接状态
        scope.launch {
            webSocketManager.connectionState.collect { state ->
                handleConnectionState(state)
            }
        }
        
        // 连接WebSocket
        connectWebSocket()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY // 服务被杀死后自动重启
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        webSocketManager.disconnect()
    }

    private fun connectWebSocket() {
        // TODO: 从SharedPreferences或其他地方获取用户token
        val token = "user_token_here"
        val wsUrl = "wss://api.laundry-merchant.com/ws"
        webSocketManager.connect(wsUrl, token)
    }

    private fun handleWebSocketMessage(message: WebSocketMessage) {
        scope.launch {
            try {
                when (message.type) {
                    MessageType.ORDER_STATUS_UPDATE -> {
                        val data = gson.fromJson(gson.toJson(message.data), OrderStatusUpdateData::class.java)
                        _orderUpdates.emit(data)
                        showOrderNotification(data)
                    }
                    
                    MessageType.BOOKING_STATUS_UPDATE -> {
                        val data = gson.fromJson(gson.toJson(message.data), BookingStatusUpdateData::class.java)
                        _bookingUpdates.emit(data)
                        showBookingNotification(data)
                    }
                    
                    MessageType.MERCHANT_STATUS_UPDATE -> {
                        val data = gson.fromJson(gson.toJson(message.data), MerchantStatusUpdateData::class.java)
                        _merchantUpdates.emit(data)
                    }
                    
                    MessageType.DELIVERY_STATUS_UPDATE -> {
                        val data = gson.fromJson(gson.toJson(message.data), DeliveryStatusUpdateData::class.java)
                        _deliveryUpdates.emit(data)
                        showDeliveryNotification(data)
                    }
                    
                    MessageType.SYSTEM_NOTIFICATION -> {
                        val data = gson.fromJson(gson.toJson(message.data), SystemNotificationData::class.java)
                        _systemNotifications.emit(data)
                        showSystemNotification(data)
                    }
                    
                    MessageType.PING -> {
                        // 响应心跳
                        webSocketManager.sendMessage(
                            WebSocketMessage(MessageType.PONG, "pong")
                        )
                    }
                    
                    else -> {
                        // 处理其他类型的消息
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("RealTimeUpdateService", "Error handling message", e)
            }
        }
    }

    private fun handleConnectionState(state: ConnectionState) {
        when (state) {
            is ConnectionState.CONNECTED -> {
                updateNotification("实时更新服务已连接")
            }
            is ConnectionState.DISCONNECTED -> {
                updateNotification("实时更新服务已断开")
            }
            is ConnectionState.CONNECTING -> {
                updateNotification("正在连接实时更新服务...")
            }
            is ConnectionState.ERROR -> {
                updateNotification("连接错误: ${state.message}")
            }
        }
    }

    private fun showOrderNotification(data: OrderStatusUpdateData) {
        val title = "订单状态更新"
        val message = "订单 ${data.orderId} 状态已更新为: ${data.newStatus}"
        
        showNotification(title, message, data.orderId.hashCode())
    }

    private fun showBookingNotification(data: BookingStatusUpdateData) {
        val title = "预约状态更新"
        val message = when (data.newStatus) {
            "CONFIRMED" -> "您的预约已确认"
            "IN_PROGRESS" -> "服务正在进行中"
            "COMPLETED" -> "服务已完成"
            "CANCELLED" -> "预约已取消"
            else -> "预约状态已更新"
        }
        
        showNotification(title, message, data.bookingId.hashCode())
    }

    private fun showDeliveryNotification(data: DeliveryStatusUpdateData) {
        val title = "配送状态更新"
        val message = when (data.status) {
            "PICKED_UP" -> "您的订单已取件"
            "IN_TRANSIT" -> "订单正在配送中"
            "OUT_FOR_DELIVERY" -> "订单即将送达"
            "DELIVERED" -> "订单已送达"
            else -> "配送状态已更新"
        }
        
        showNotification(title, message, data.deliveryId.hashCode())
    }

    private fun showSystemNotification(data: SystemNotificationData) {
        showNotification(data.title, data.message, data.title.hashCode())
    }

    private fun showNotification(title: String, message: String, notificationId: Int) {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_notifications)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(notificationId, notification)
    }

    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("洗护商家")
            .setContentText("实时更新服务运行中")
            .setSmallIcon(R.drawable.ic_notifications)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    private fun updateNotification(text: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("洗护商家")
            .setContentText(text)
            .setSmallIcon(R.drawable.ic_notifications)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "实时更新通知"
                setShowBadge(true)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
}
