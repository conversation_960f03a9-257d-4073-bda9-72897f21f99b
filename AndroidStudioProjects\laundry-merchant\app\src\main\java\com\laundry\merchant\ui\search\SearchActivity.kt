package com.laundry.merchant.ui.search

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.EditorInfo
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.laundry.merchant.databinding.ActivitySearchBinding
import com.laundry.merchant.ui.product.ProductDetailActivity
import com.laundry.merchant.ui.product.adapter.ProductGridAdapter
import com.laundry.merchant.ui.search.adapter.HotSearchAdapter
import com.laundry.merchant.ui.search.adapter.SearchHistoryAdapter
import com.laundry.merchant.ui.search.adapter.SearchSuggestionAdapter
import com.laundry.merchant.utils.VoiceSearchManager
import com.laundry.merchant.utils.hideKeyboard
import com.laundry.merchant.utils.showKeyboard
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class SearchActivity : AppCompatActivity(), VoiceSearchManager.VoiceSearchCallback {

    private lateinit var binding: ActivitySearchBinding
    private val viewModel: SearchViewModel by viewModels()
    
    private lateinit var hotSearchAdapter: HotSearchAdapter
    private lateinit var searchHistoryAdapter: SearchHistoryAdapter
    private lateinit var suggestionAdapter: SearchSuggestionAdapter
    private lateinit var productAdapter: ProductGridAdapter
    private lateinit var voiceSearchManager: VoiceSearchManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySearchBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeManagers()
        setupViews()
        setupRecyclerViews()
        observeViewModel()
        
        // 加载初始数据
        viewModel.loadHotSearchKeywords()
        viewModel.loadSearchHistory()
        
        // 自动弹出键盘
        binding.editTextSearch.requestFocus()
        showKeyboard(binding.editTextSearch)
    }

    private fun initializeManagers() {
        voiceSearchManager = VoiceSearchManager(this, this)
    }

    private fun setupViews() {
        // 设置返回按钮
        binding.imageViewBack.setOnClickListener {
            onBackPressed()
        }

        // 设置清除按钮
        binding.imageViewClear.setOnClickListener {
            binding.editTextSearch.text?.clear()
        }

        // 设置语音搜索按钮
        binding.imageViewVoice.setOnClickListener {
            startVoiceSearch()
        }

        // 设置搜索框
        binding.editTextSearch.apply {
            addTextChangedListener(createTextWatcher())
            setOnEditorActionListener { _, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    performSearch()
                    true
                } else {
                    false
                }
            }
        }

        // 设置搜索按钮
        binding.buttonSearch.setOnClickListener {
            performSearch()
        }

        // 设置清空历史按钮
        binding.buttonClearHistory.setOnClickListener {
            showClearHistoryDialog()
        }
    }

    private fun setupRecyclerViews() {
        // 热门搜索
        hotSearchAdapter = HotSearchAdapter { keyword ->
            binding.editTextSearch.setText(keyword.keyword)
            performSearch()
        }
        binding.recyclerViewHotSearch.apply {
            layoutManager = LinearLayoutManager(this@SearchActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = hotSearchAdapter
        }

        // 搜索历史
        searchHistoryAdapter = SearchHistoryAdapter(
            onItemClick = { keyword ->
                binding.editTextSearch.setText(keyword)
                performSearch()
            },
            onDeleteClick = { keyword ->
                viewModel.removeSearchHistory(keyword)
            }
        )
        binding.recyclerViewSearchHistory.apply {
            layoutManager = LinearLayoutManager(this@SearchActivity)
            adapter = searchHistoryAdapter
        }

        // 搜索建议
        suggestionAdapter = SearchSuggestionAdapter { suggestion ->
            binding.editTextSearch.setText(suggestion.text)
            performSearch()
        }
        binding.recyclerViewSuggestions.apply {
            layoutManager = LinearLayoutManager(this@SearchActivity)
            adapter = suggestionAdapter
        }

        // 搜索结果
        productAdapter = ProductGridAdapter(
            onProductClick = { product ->
                val intent = Intent(this, ProductDetailActivity::class.java)
                intent.putExtra("product_id", product.id)
                startActivity(intent)
            },
            onAddToCartClick = { product ->
                viewModel.addToCart(product)
            }
        )
        binding.recyclerViewSearchResults.apply {
            layoutManager = GridLayoutManager(this@SearchActivity, 2)
            adapter = productAdapter
        }
    }

    private fun createTextWatcher(): TextWatcher {
        return object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val query = s.toString().trim()
                
                // 更新清除按钮显示
                binding.imageViewClear.visibility = if (query.isNotEmpty()) View.VISIBLE else View.GONE
                
                // 获取搜索建议
                if (query.isNotEmpty()) {
                    viewModel.getSearchSuggestions(query)
                    showSuggestions()
                } else {
                    showSearchHome()
                }
            }
        }
    }

    private fun performSearch() {
        val query = binding.editTextSearch.text.toString().trim()
        if (query.isNotEmpty()) {
            hideKeyboard()
            viewModel.searchProducts(query)
            showSearchResults()
        }
    }

    private fun startVoiceSearch() {
        if (!voiceSearchManager.hasRecordAudioPermission()) {
            voiceSearchManager.requestRecordAudioPermission(this)
            return
        }

        if (!voiceSearchManager.isSpeechRecognitionAvailable()) {
            showToast("设备不支持语音识别")
            return
        }

        voiceSearchManager.startVoiceSearch()
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: SearchUiState) {
        // 更新热门搜索
        hotSearchAdapter.updateData(state.hotKeywords)

        // 更新搜索历史
        searchHistoryAdapter.updateData(state.searchHistory)
        binding.layoutSearchHistory.visibility = if (state.searchHistory.isNotEmpty()) View.VISIBLE else View.GONE

        // 更新搜索建议
        suggestionAdapter.updateData(state.suggestions)

        // 更新搜索结果
        productAdapter.updateData(state.searchResults)
        binding.textViewResultCount.text = "找到 ${state.searchResults.size} 个结果"

        // 更新加载状态
        binding.progressBar.visibility = if (state.isLoading) View.VISIBLE else View.GONE

        // 更新空状态
        binding.emptyView.visibility = if (state.searchResults.isEmpty() && 
            !state.isLoading && 
            state.currentQuery.isNotEmpty()) {
            View.VISIBLE
        } else {
            View.GONE
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: SearchEvent) {
        when (event) {
            is SearchEvent.ShowError -> {
                showError(event.message)
            }
            is SearchEvent.ShowSuccess -> {
                showToast(event.message)
            }
        }
    }

    private fun showSearchHome() {
        binding.layoutSearchHome.visibility = View.VISIBLE
        binding.layoutSuggestions.visibility = View.GONE
        binding.layoutSearchResults.visibility = View.GONE
    }

    private fun showSuggestions() {
        binding.layoutSearchHome.visibility = View.GONE
        binding.layoutSuggestions.visibility = View.VISIBLE
        binding.layoutSearchResults.visibility = View.GONE
    }

    private fun showSearchResults() {
        binding.layoutSearchHome.visibility = View.GONE
        binding.layoutSuggestions.visibility = View.GONE
        binding.layoutSearchResults.visibility = View.VISIBLE
    }

    private fun showClearHistoryDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("清空搜索历史")
            .setMessage("确定要清空所有搜索历史吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.clearSearchHistory()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showError(message: String) {
        showToast(message)
    }

    // VoiceSearchCallback 实现
    override fun onVoiceSearchStart() {
        binding.imageViewVoice.setImageResource(android.R.drawable.ic_btn_speak_now)
        showToast("请说话...")
    }

    override fun onVoiceSearchResult(text: String) {
        binding.editTextSearch.setText(text)
        performSearch()
    }

    override fun onVoiceSearchError(error: String) {
        showToast(error)
    }

    override fun onVoiceSearchEnd() {
        binding.imageViewVoice.setImageResource(android.R.drawable.ic_btn_speak_now)
    }

    override fun onPermissionRequired() {
        voiceSearchManager.requestRecordAudioPermission(this)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (voiceSearchManager.handlePermissionResult(requestCode, grantResults)) {
            startVoiceSearch()
        } else {
            showToast("需要录音权限才能使用语音搜索")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        voiceSearchManager.destroy()
    }
}
