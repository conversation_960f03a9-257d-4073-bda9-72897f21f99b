<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.search.SearchFragment">

    <!-- 搜索栏 -->
    <androidx.appcompat.widget.SearchView
        android:id="@+id/search_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/category_background"
        android:elevation="2dp"
        android:queryHint="搜索洗护服务..."
        app:iconifiedByDefault="false"
        app:layout_constraintEnd_toStartOf="@+id/button_filter"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 筛选按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_filter"
        style="@style/Widget.Material3.Button.OutlinedButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:text="筛选"
        app:icon="@drawable/ic_arrow_right_24"
        app:layout_constraintBottom_toBottomOf="@+id/search_view"
        app:layout_constraintEnd_toStartOf="@+id/button_sort"
        app:layout_constraintTop_toTopOf="@+id/search_view" />

    <!-- 排序按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_sort"
        style="@style/Widget.Material3.Button.OutlinedButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="排序"
        app:icon="@drawable/ic_arrow_right_24"
        app:layout_constraintBottom_toBottomOf="@+id/search_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/search_view" />

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/search_view" />

    <!-- 热门搜索区域 -->
    <LinearLayout
        android:id="@+id/layout_hot_searches"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:orientation="vertical"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/search_view">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="热门搜索"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_hot_searches"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:chipSpacingHorizontal="8dp"
            app:chipSpacingVertical="4dp" />

    </LinearLayout>

    <!-- 搜索结果列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_search_results"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_hot_searches"
        tools:listitem="@layout/item_service" />

    <!-- 空状态布局 -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/search_view">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="16dp"
            android:alpha="0.5"
            android:src="@drawable/ic_placeholder"
            app:tint="@color/text_hint" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="暂无搜索结果"
            android:textColor="@color/text_secondary"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="试试其他关键词吧"
            android:textColor="@color/text_hint"
            android:textSize="14sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
