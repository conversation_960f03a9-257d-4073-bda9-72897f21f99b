package com.laundry.merchant.network

import com.google.gson.annotations.SerializedName

/**
 * 统一的API响应格式
 */
data class ApiResponse<T>(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("message")
    val message: String,
    
    @SerializedName("data")
    val data: T? = null,
    
    @SerializedName("success")
    val success: Boolean = code == 200,
    
    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis()
) {
    fun isSuccess(): Boolean = success && code == 200
    
    fun isError(): Boolean = !success || code != 200
    
    fun getErrorMessage(): String = if (message.isNotBlank()) message else "未知错误"
}

/**
 * 分页响应格式
 */
data class PagedResponse<T>(
    @SerializedName("items")
    val items: List<T>,
    
    @SerializedName("total")
    val total: Int,
    
    @SerializedName("page")
    val page: Int,
    
    @SerializedName("limit")
    val limit: Int,
    
    @SerializedName("hasMore")
    val hasMore: Boolean
)

/**
 * 网络请求结果封装
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val message: String, val code: Int = -1) : NetworkResult<T>()
    data class Loading<T>(val isLoading: Boolean = true) : NetworkResult<T>()
}

/**
 * API错误码常量
 */
object ApiErrorCodes {
    const val SUCCESS = 200
    const val BAD_REQUEST = 400
    const val UNAUTHORIZED = 401
    const val FORBIDDEN = 403
    const val NOT_FOUND = 404
    const val INTERNAL_SERVER_ERROR = 500
    const val NETWORK_ERROR = -1
    const val TIMEOUT_ERROR = -2
    const val UNKNOWN_ERROR = -999
}

/**
 * 业务错误码常量
 */
object BusinessErrorCodes {
    const val INVALID_CREDENTIALS = 1001
    const val ACCOUNT_LOCKED = 1002
    const val TOKEN_EXPIRED = 1003
    const val INSUFFICIENT_BALANCE = 2001
    const val ORDER_NOT_FOUND = 3001
    const val ORDER_STATUS_INVALID = 3002
    const val PROMOTION_BUDGET_EXCEEDED = 4001
    const val KEYWORD_BID_TOO_LOW = 4002
}
