package com.laundry.merchant.ui.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import com.laundry.merchant.R
import com.laundry.merchant.databinding.DialogAgreementDetailBinding

class AgreementDetailDialog(
    context: Context,
    private val title: String,
    private val content: String
) : Dialog(context, R.style.Dialog_FullScreen) {

    private lateinit var binding: DialogAgreementDetailBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogAgreementDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
    }

    private fun setupViews() {
        // 设置标题
        binding.textViewTitle.text = title
        
        // 设置内容
        binding.textViewContent.text = content
        
        // 设置关闭按钮
        binding.buttonClose.setOnClickListener {
            dismiss()
        }
        
        // 设置工具栏关闭按钮
        binding.imageViewClose.setOnClickListener {
            dismiss()
        }
    }
}
