<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗护帮 - 用户端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .phone-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .app-header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .search-section {
            padding: 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-bar {
            background: #f0f0f0;
            border-radius: 25px;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .search-input {
            border: none;
            background: none;
            flex: 1;
            font-size: 16px;
            outline: none;
        }
        
        .filter-tabs {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 5px;
        }
        
        .filter-tab {
            background: #f0f0f0;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-tab.active {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 100px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .service-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-2px);
        }
        
        .promoted-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .ranking-number {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 12px;
            font-size: 14px;
        }
        
        .service-info {
            flex: 1;
        }
        
        .service-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 4px;
        }
        
        .service-details {
            font-size: 12px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .rating {
            color: #ff9500;
        }
        
        .price {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .service-features {
            margin-top: 8px;
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .feature-tag {
            background: #e8f5e8;
            color: #4CAF50;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 80px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-radius: 0 0 32px 32px;
        }
        
        .nav-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            padding: 8px;
        }
        
        .nav-item.active {
            color: #4CAF50;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .floating-btn {
            position: fixed;
            bottom: 100px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .floating-btn:hover {
            transform: scale(1.1);
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .category-item {
            text-align: center;
            padding: 15px 10px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .category-item:hover {
            transform: translateY(-2px);
        }
        
        .category-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .category-name {
            font-size: 12px;
            font-weight: 500;
        }
        
        .order-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .order-status {
            background: #e8f5e8;
            color: #4CAF50;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .order-progress {
            background: #f0f0f0;
            height: 4px;
            border-radius: 2px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .order-progress-bar {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            height: 100%;
            width: 60%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🧺 洗护帮</span>
                    <span>🔋100%</span>
                </div>
                
                <div class="app-header">
                    <div class="app-title">洗护帮</div>
                    <div class="app-subtitle">智能搜索 · 投流排名</div>
                </div>
                
                <div class="search-section">
                    <div class="search-bar">
                        <span>🔍</span>
                        <input type="text" class="search-input" placeholder="搜索洗护服务..." value="衣物洗护">
                        <span>🎤</span>
                    </div>
                    <div class="filter-tabs">
                        <button class="filter-tab active">全部</button>
                        <button class="filter-tab">推广</button>
                        <button class="filter-tab">附近</button>
                        <button class="filter-tab">高评分</button>
                        <button class="filter-tab">优惠</button>
                    </div>
                </div>
                
                <div class="content-area" id="contentArea">
                    <!-- 首页内容 -->
                    <div id="homeContent">
                        <div class="section-title">
                            🔥 推荐服务
                        </div>
                        
                        <div class="service-card">
                            <div class="promoted-badge">推广</div>
                            <div class="service-header">
                                <div class="ranking-number">1</div>
                                <div class="service-info">
                                    <div class="service-name">优质洗衣店</div>
                                    <div class="service-details">
                                        <span class="rating">⭐ 4.9</span>
                                        <span>距离500m</span>
                                        <span class="price">¥15起</span>
                                    </div>
                                    <div class="service-features">
                                        <span class="feature-tag">快速取送</span>
                                        <span class="feature-tag">专业洗护</span>
                                        <span class="feature-tag">24小时</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="service-card">
                            <div class="promoted-badge">推广</div>
                            <div class="service-header">
                                <div class="ranking-number">2</div>
                                <div class="service-info">
                                    <div class="service-name">专业干洗店</div>
                                    <div class="service-details">
                                        <span class="rating">⭐ 4.8</span>
                                        <span>距离800m</span>
                                        <span class="price">¥20起</span>
                                    </div>
                                    <div class="service-features">
                                        <span class="feature-tag">高端面料</span>
                                        <span class="feature-tag">无损清洗</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="service-card">
                            <div class="service-header">
                                <div class="ranking-number">3</div>
                                <div class="service-info">
                                    <div class="service-name">快速洗护</div>
                                    <div class="service-details">
                                        <span class="rating">⭐ 4.7</span>
                                        <span>距离1.2km</span>
                                        <span class="price">¥12起</span>
                                    </div>
                                    <div class="service-features">
                                        <span class="feature-tag">1小时取送</span>
                                        <span class="feature-tag">价格实惠</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="section-title">
                            📂 服务分类
                        </div>
                        
                        <div class="category-grid">
                            <div class="category-item">
                                <div class="category-icon">👔</div>
                                <div class="category-name">衣物洗护</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">👟</div>
                                <div class="category-name">鞋类清洗</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">🐕</div>
                                <div class="category-name">萌宠洗护</div>
                            </div>
                            <div class="category-item">
                                <div class="category-icon">👜</div>
                                <div class="category-name">包包清洗</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 订单内容 -->
                    <div id="orderContent" style="display: none;">
                        <div class="section-title">
                            📋 我的订单
                        </div>
                        
                        <div class="order-card">
                            <div class="order-header">
                                <div>
                                    <div style="font-weight: bold;">优质洗衣店</div>
                                    <div style="font-size: 12px; color: #666;">订单号: LH202412120001</div>
                                </div>
                                <div class="order-status">服务中</div>
                            </div>
                            <div>衣物洗护 × 3件</div>
                            <div class="order-progress">
                                <div class="order-progress-bar"></div>
                            </div>
                            <div style="font-size: 12px; color: #666;">预计完成: 2小时后</div>
                        </div>
                        
                        <div class="order-card">
                            <div class="order-header">
                                <div>
                                    <div style="font-weight: bold;">专业干洗店</div>
                                    <div style="font-size: 12px; color: #666;">订单号: LH202412110002</div>
                                </div>
                                <div class="order-status" style="background: #fff3cd; color: #856404;">待评价</div>
                            </div>
                            <div>高端西装干洗 × 1件</div>
                            <div style="font-size: 12px; color: #666;">已完成</div>
                        </div>
                    </div>
                    
                    <!-- 收藏内容 -->
                    <div id="favoriteContent" style="display: none;">
                        <div class="section-title">
                            ❤️ 我的收藏
                        </div>
                        
                        <div class="service-card">
                            <div class="service-header">
                                <div class="service-info">
                                    <div class="service-name">优质洗衣店</div>
                                    <div class="service-details">
                                        <span class="rating">⭐ 4.9</span>
                                        <span>距离500m</span>
                                        <span class="price">¥15起</span>
                                    </div>
                                </div>
                                <span style="color: #ff6b6b; font-size: 20px;">❤️</span>
                            </div>
                        </div>
                        
                        <div class="service-card">
                            <div class="service-header">
                                <div class="service-info">
                                    <div class="service-name">专业干洗店</div>
                                    <div class="service-details">
                                        <span class="rating">⭐ 4.8</span>
                                        <span>距离800m</span>
                                        <span class="price">¥20起</span>
                                    </div>
                                </div>
                                <span style="color: #ff6b6b; font-size: 20px;">❤️</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 个人中心内容 -->
                    <div id="profileContent" style="display: none;">
                        <div class="section-title">
                            👤 个人中心
                        </div>
                        
                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; text-align: center;">
                            <div style="width: 60px; height: 60px; background: linear-gradient(45deg, #4CAF50, #45a049); border-radius: 50%; margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">👤</div>
                            <div style="font-weight: bold; margin-bottom: 5px;">张三</div>
                            <div style="font-size: 12px; color: #666;">会员等级: 黄金会员</div>
                        </div>
                        
                        <div style="background: white; border-radius: 15px; padding: 15px; margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span>积分余额</span>
                                <span style="color: #4CAF50; font-weight: bold;">1,280分</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span>优惠券</span>
                                <span style="color: #4CAF50; font-weight: bold;">5张</span>
                            </div>
                            <div style="display: flex; justify-content: space-between;">
                                <span>历史订单</span>
                                <span style="color: #4CAF50; font-weight: bold;">23单</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="switchContent('home')">
                        <div class="nav-icon">🏠</div>
                        <div class="nav-label">首页</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('order')">
                        <div class="nav-icon">📋</div>
                        <div class="nav-label">订单</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('favorite')">
                        <div class="nav-icon">❤️</div>
                        <div class="nav-label">收藏</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('profile')">
                        <div class="nav-icon">👤</div>
                        <div class="nav-label">我的</div>
                    </div>
                </div>
                
                <div class="floating-btn">
                    💬
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchContent(content) {
            // 隐藏所有内容
            document.querySelectorAll('#contentArea > div').forEach(div => {
                div.style.display = 'none';
            });
            
            // 移除所有导航项的active状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示对应内容
            document.getElementById(content + 'Content').style.display = 'block';
            
            // 激活对应导航项
            event.target.closest('.nav-item').classList.add('active');
        }
        
        // 模拟实时数据更新
        function updateRealTimeData() {
            // 更新排名
            const rankings = document.querySelectorAll('.ranking-number');
            rankings.forEach(ranking => {
                if (Math.random() < 0.1) {
                    const currentRank = parseInt(ranking.textContent);
                    const change = Math.random() < 0.5 ? -1 : 1;
                    const newRank = Math.max(1, Math.min(10, currentRank + change));
                    ranking.textContent = newRank.toString();
                }
            });
            
            // 更新订单进度
            const progressBar = document.querySelector('.order-progress-bar');
            if (progressBar) {
                const currentWidth = parseInt(progressBar.style.width) || 60;
                const newWidth = Math.min(100, currentWidth + Math.floor(Math.random() * 5));
                progressBar.style.width = newWidth + '%';
            }
        }
        
        // 每5秒更新一次数据
        setInterval(updateRealTimeData, 5000);
        
        // 筛选标签切换
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 根据筛选条件更新服务列表
                if (this.textContent === '推广') {
                    // 只显示推广服务
                    document.querySelectorAll('.service-card').forEach(card => {
                        const hasBadge = card.querySelector('.promoted-badge');
                        card.style.display = hasBadge ? 'block' : 'none';
                    });
                } else {
                    // 显示所有服务
                    document.querySelectorAll('.service-card').forEach(card => {
                        card.style.display = 'block';
                    });
                }
            });
        });
        
        // 服务卡片点击事件
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('click', function() {
                alert('跳转到服务详情页面');
            });
        });
        
        // 分类点击事件
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', function() {
                const categoryName = this.querySelector('.category-name').textContent;
                alert(`搜索 ${categoryName} 相关服务`);
            });
        });
    </script>
</body>
</html>
