@echo off
chcp 65001 >nul
echo.
echo ================================
echo    洗护帮用户端 - 快速启动
echo ================================
echo.

:: 检查Java环境
echo [1/5] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java未安装，请先安装JDK 11或17
    echo 下载地址: https://adoptium.net/
    pause
    exit /b 1
) else (
    echo ✅ Java环境正常
)

:: 检查Android SDK
echo [2/5] 检查Android SDK...
if not defined ANDROID_HOME (
    echo ❌ ANDROID_HOME环境变量未设置
    echo 请设置ANDROID_HOME环境变量指向Android SDK目录
    echo 例如: set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    pause
    exit /b 1
) else (
    echo ✅ Android SDK: %ANDROID_HOME%
)

:: 检查ADB
echo [3/5] 检查ADB工具...
"%ANDROID_HOME%\platform-tools\adb.exe" version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ADB工具未找到
    echo 请确保Android SDK已正确安装
    pause
    exit /b 1
) else (
    echo ✅ ADB工具正常
)

:: 检查设备
echo [4/5] 检查设备连接...
"%ANDROID_HOME%\platform-tools\adb.exe" devices | findstr "device$" >nul
if %errorlevel% neq 0 (
    echo ⚠️  没有检测到连接的设备
    echo.
    echo 请选择以下选项:
    echo 1. 连接Android设备并启用USB调试
    echo 2. 启动Android模拟器
    echo 3. 使用Android Studio启动
    echo.
    echo 推荐使用Android Studio进行开发和调试
    pause
    exit /b 0
) else (
    echo ✅ 设备连接正常
    "%ANDROID_HOME%\platform-tools\adb.exe" devices
)

:: 启动说明
echo [5/5] 启动应用...
echo.
echo 🚀 用户端应用启动选项:
echo.
echo 方案1: Android Studio (推荐)
echo   1. 打开Android Studio
echo   2. 导入 laundry-user 项目
echo   3. 等待Gradle同步
echo   4. 点击运行按钮
echo.
echo 方案2: VSCode
echo   1. 打开VSCode
echo   2. 安装Android扩展
echo   3. 导入项目
echo   4. 按F5调试
echo.
echo 方案3: 命令行
echo   1. cd laundry-user
echo   2. gradlew assembleDebug
echo   3. gradlew installDebug
echo.
echo ================================
echo    选择您喜欢的开发环境
echo ================================

pause
