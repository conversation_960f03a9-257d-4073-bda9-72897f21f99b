<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- 电话权限 -->
    <uses-permission android:name="android.permission.CALL_PHONE" />
    
    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    
    <!-- 相机权限（扫码功能） -->
    <uses-permission android:name="android.permission.CAMERA" />
    
    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- 录音权限（语音搜索） -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 振动权限 -->
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- 唤醒锁权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
        android:name=".LaundryMerchantApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.LaundryMerchant"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <!-- 启动页Activity -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LaundryMerchant.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 主Activity -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- 登录Activity -->
        <activity
            android:name=".ui.auth.LoginActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <!-- 订单详情Activity -->
        <activity
            android:name=".ui.order.OrderDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:parentActivityName=".ui.main.MainActivity" />

        <!-- 设置Activity -->
        <activity
            android:name=".ui.settings.SettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:parentActivityName=".ui.main.MainActivity" />

        <!-- 扫码Activity -->
        <activity
            android:name=".ui.scan.ScanActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:parentActivityName=".ui.main.MainActivity" />

        <!-- 产品浏览Activity -->
        <activity
            android:name=".ui.product.ProductBrowseActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:parentActivityName=".ui.main.MainActivity" />

        <!-- 搜索Activity -->
        <activity
            android:name=".ui.search.SearchActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize"
            android:parentActivityName=".ui.product.ProductBrowseActivity" />

        <!-- 产品详情Activity -->
        <activity
            android:name=".ui.product.ProductDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:parentActivityName=".ui.product.ProductBrowseActivity" />

        <!-- 地图Activity -->
        <activity
            android:name=".ui.map.MapActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:parentActivityName=".ui.main.MainActivity" />

        <!-- 数据分析Activity -->
        <activity
            android:name=".ui.analytics.AnalyticsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:parentActivityName=".ui.main.MainActivity" />

        <!-- 实时更新服务 -->
        <service
            android:name=".service.RealTimeUpdateService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- Google Maps API Key -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="YOUR_GOOGLE_MAPS_API_KEY" />

        <!-- 文件提供者 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Firebase消息服务 -->
        <service
            android:name=".service.LaundryFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- 后台同步服务 -->
        <service
            android:name=".service.SyncService"
            android:exported="false" />

        <!-- 通知渠道元数据 -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notifications" />
        
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorPrimary" />

        <!-- 网络安全配置 -->
        <meta-data
            android:name="android.webkit.WebView.MetricsOptOut"
            android:value="true" />

    </application>

</manifest>
