<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:background="?attr/selectableItemBackground"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="12dp">

    <ImageView
        android:id="@+id/imageViewCategoryIcon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/circle_background"
        android:padding="12dp"
        android:src="@drawable/ic_category_placeholder"
        android:scaleType="centerInside" />

    <TextView
        android:id="@+id/textViewCategoryName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:text="洗衣服务"
        android:textColor="@color/gray_800"
        android:textSize="12sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/textViewProductCount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="25个商品"
        android:textColor="@color/gray_500"
        android:textSize="10sp" />

</LinearLayout>
