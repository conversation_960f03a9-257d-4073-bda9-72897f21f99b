<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.LaundryMerchant" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryDark</item>
        <item name="colorOnPrimary">@android:color/white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/colorAccent</item>
        <item name="colorSecondaryVariant">@color/colorAccent</item>
        <item name="colorOnSecondary">@android:color/white</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        <item name="android:navigationBarColor">@android:color/white</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o">true</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorOnBackground">@color/text_primary_light</item>
        <item name="colorOnSurface">@color/text_primary_light</item>
        
        <!-- Action bar -->
        <item name="actionBarStyle">@style/ActionBar.LaundryMerchant</item>
        <item name="android:actionBarStyle">@style/ActionBar.LaundryMerchant</item>
        
        <!-- Toolbar -->
        <item name="toolbarStyle">@style/Toolbar.LaundryMerchant</item>
        
        <!-- Button styles -->
        <item name="materialButtonStyle">@style/Button.LaundryMerchant</item>
        <item name="borderlessButtonStyle">@style/Button.LaundryMerchant.Borderless</item>
        
        <!-- Card style -->
        <item name="materialCardViewStyle">@style/CardView.LaundryMerchant</item>
        
        <!-- Text styles -->
        <item name="textAppearanceHeadline1">@style/TextAppearance.LaundryMerchant.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.LaundryMerchant.Headline2</item>
        <item name="textAppearanceBody1">@style/TextAppearance.LaundryMerchant.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.LaundryMerchant.Body2</item>
        
        <!-- Bottom navigation -->
        <item name="bottomNavigationStyle">@style/BottomNavigation.LaundryMerchant</item>
        
        <!-- Tab layout -->
        <item name="tabStyle">@style/Tab.LaundryMerchant</item>
        
        <!-- Chip style -->
        <item name="chipStyle">@style/Chip.LaundryMerchant</item>
        
        <!-- FAB style -->
        <item name="floatingActionButtonStyle">@style/FAB.LaundryMerchant</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.LaundryMerchant.Splash" parent="Theme.LaundryMerchant">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Action Bar Style -->
    <style name="ActionBar.LaundryMerchant" parent="Widget.AppCompat.ActionBar">
        <item name="background">@color/colorPrimary</item>
        <item name="titleTextStyle">@style/ActionBar.LaundryMerchant.Title</item>
        <item name="elevation">4dp</item>
    </style>

    <style name="ActionBar.LaundryMerchant.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- Toolbar Style -->
    <style name="Toolbar.LaundryMerchant" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/colorPrimary</item>
        <item name="titleTextColor">@android:color/white</item>
        <item name="subtitleTextColor">@android:color/white</item>
        <item name="android:theme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="popupTheme">@style/ThemeOverlay.AppCompat.Light</item>
    </style>

    <!-- Button Styles -->
    <style name="Button.LaundryMerchant" parent="Widget.Material3.Button">
        <item name="android:textColor">@android:color/white</item>
        <item name="backgroundTint">@color/colorPrimary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="Button.LaundryMerchant.Borderless" parent="Widget.Material3.Button.TextButton">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="Button.LaundryMerchant.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="strokeColor">@color/colorPrimary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <!-- Card Style -->
    <style name="CardView.LaundryMerchant" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">2dp</item>
        <item name="cardBackgroundColor">@android:color/white</item>
        <item name="contentPadding">16dp</item>
    </style>

    <!-- Text Styles -->
    <style name="TextAppearance.LaundryMerchant.Headline1" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.LaundryMerchant.Headline2" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.LaundryMerchant.Body1" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary_light</item>
    </style>

    <style name="TextAppearance.LaundryMerchant.Body2" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/text_secondary_light</item>
    </style>

    <!-- Bottom Navigation Style -->
    <style name="BottomNavigation.LaundryMerchant" parent="Widget.Material3.BottomNavigationView">
        <item name="android:background">@android:color/white</item>
        <item name="itemIconTint">@color/bottom_nav_color</item>
        <item name="itemTextColor">@color/bottom_nav_color</item>
        <item name="elevation">8dp</item>
    </style>

    <!-- Tab Style -->
    <style name="Tab.LaundryMerchant" parent="Widget.Material3.TabLayout">
        <item name="tabIndicatorColor">@color/colorPrimary</item>
        <item name="tabSelectedTextColor">@color/colorPrimary</item>
        <item name="tabTextColor">@color/gray_600</item>
        <item name="tabIndicatorHeight">2dp</item>
    </style>

    <!-- Chip Style -->
    <style name="Chip.LaundryMerchant" parent="Widget.Material3.Chip.Choice">
        <item name="chipBackgroundColor">@color/chip_background_color</item>
        <item name="android:textColor">@color/chip_text_color</item>
        <item name="chipStrokeColor">@color/colorPrimary</item>
        <item name="chipStrokeWidth">1dp</item>
    </style>

    <!-- FAB Style -->
    <style name="FAB.LaundryMerchant" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="backgroundTint">@color/colorPrimary</item>
        <item name="tint">@android:color/white</item>
        <item name="elevation">6dp</item>
    </style>

    <!-- Dialog Style -->
    <style name="Dialog.LaundryMerchant" parent="ThemeOverlay.Material3.Dialog">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:windowBackground">@drawable/dialog_background</item>
    </style>

    <!-- Progress Bar Style -->
    <style name="ProgressBar.LaundryMerchant" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressTint">@color/colorPrimary</item>
        <item name="android:progressBackgroundTint">@color/gray_200</item>
    </style>

    <!-- Dialog Styles -->
    <style name="Dialog.FullScreen" parent="android:Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
    </style>

</resources>
