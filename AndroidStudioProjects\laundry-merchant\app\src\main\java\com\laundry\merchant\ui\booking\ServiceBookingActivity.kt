package com.laundry.merchant.ui.booking

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.chip.Chip
import com.laundry.merchant.R
import com.laundry.merchant.data.model.ServiceType
import com.laundry.merchant.data.model.UrgentPriority
import com.laundry.merchant.data.model.UrgentReason
import com.laundry.merchant.databinding.ActivityServiceBookingBinding
import com.laundry.merchant.ui.booking.adapter.MerchantAvailabilityAdapter
import com.laundry.merchant.ui.booking.adapter.ServiceSlotAdapter
import com.laundry.merchant.utils.formatDate
import com.laundry.merchant.utils.formatTime
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date

@AndroidEntryPoint
class ServiceBookingActivity : AppCompatActivity() {

    private lateinit var binding: ActivityServiceBookingBinding
    private val viewModel: ServiceBookingViewModel by viewModels()
    
    private lateinit var slotAdapter: ServiceSlotAdapter
    private lateinit var merchantAdapter: MerchantAvailabilityAdapter
    
    private var selectedDate: Date = Date()
    private var selectedServiceType: ServiceType = ServiceType.HOME_SERVICE
    private var isUrgentService = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityServiceBookingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerViews()
        setupServiceTypeChips()
        observeViewModel()
        
        // 加载初始数据
        viewModel.loadAvailableSlots(selectedServiceType, selectedDate, isUrgentService)
        viewModel.loadMerchantAvailability()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "服务预约"

        // 设置日期选择
        binding.textViewSelectedDate.text = selectedDate.formatDate()
        binding.layoutDateSelector.setOnClickListener {
            showDatePicker()
        }

        // 设置紧急服务开关
        binding.switchUrgentService.setOnCheckedChangeListener { _, isChecked ->
            isUrgentService = isChecked
            binding.layoutUrgentOptions.visibility = if (isChecked) View.VISIBLE else View.GONE
            viewModel.loadAvailableSlots(selectedServiceType, selectedDate, isUrgentService)
        }

        // 设置预约按钮
        binding.buttonConfirmBooking.setOnClickListener {
            confirmBooking()
        }

        // 设置紧急预约按钮
        binding.buttonUrgentBooking.setOnClickListener {
            createUrgentBooking()
        }
    }

    private fun setupRecyclerViews() {
        // 时段选择
        slotAdapter = ServiceSlotAdapter { slot ->
            viewModel.selectTimeSlot(slot)
        }
        binding.recyclerViewTimeSlots.apply {
            layoutManager = GridLayoutManager(this@ServiceBookingActivity, 3)
            adapter = slotAdapter
        }

        // 商家状态
        merchantAdapter = MerchantAvailabilityAdapter { merchant ->
            viewModel.selectMerchant(merchant)
        }
        binding.recyclerViewMerchants.apply {
            layoutManager = LinearLayoutManager(this@ServiceBookingActivity)
            adapter = merchantAdapter
        }
    }

    private fun setupServiceTypeChips() {
        val serviceTypes = listOf(
            ServiceType.HOME_SERVICE to "上门服务",
            ServiceType.STORE_SERVICE to "到店服务",
            ServiceType.URGENT_SERVICE to "紧急服务"
        )

        serviceTypes.forEach { (type, name) ->
            val chip = Chip(this).apply {
                text = name
                isCheckable = true
                isChecked = type == selectedServiceType
                setOnCheckedChangeListener { _, isChecked ->
                    if (isChecked) {
                        selectedServiceType = type
                        // 取消其他chip的选中状态
                        binding.chipGroupServiceTypes.children.forEach { view ->
                            if (view is Chip && view != this) {
                                view.isChecked = false
                            }
                        }
                        viewModel.loadAvailableSlots(selectedServiceType, selectedDate, isUrgentService)
                    }
                }
            }
            binding.chipGroupServiceTypes.addView(chip)
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: ServiceBookingUiState) {
        // 更新加载状态
        binding.progressBar.visibility = if (state.isLoading) View.VISIBLE else View.GONE

        // 更新时段列表
        slotAdapter.updateData(state.availableSlots)
        binding.textViewSlotsCount.text = "可选时段 (${state.availableSlots.size})"

        // 更新商家状态
        merchantAdapter.updateData(state.merchantAvailability)

        // 更新选中状态
        binding.textViewSelectedSlot.text = state.selectedSlot?.let {
            "${it.startTime} - ${it.endTime}"
        } ?: "请选择时段"

        binding.textViewSelectedMerchant.text = state.selectedMerchant?.merchantName ?: "系统自动分配"

        // 更新价格信息
        state.selectedSlot?.let { slot ->
            val price = if (isUrgentService) slot.urgentPrice ?: slot.price else slot.price
            binding.textViewPrice.text = "¥${String.format("%.2f", price)}"
            
            if (isUrgentService && slot.urgentPrice != null) {
                binding.textViewUrgentFee.visibility = View.VISIBLE
                binding.textViewUrgentFee.text = "紧急服务费: ¥${String.format("%.2f", slot.urgentPrice!! - slot.price)}"
            } else {
                binding.textViewUrgentFee.visibility = View.GONE
            }
        }

        // 更新按钮状态
        binding.buttonConfirmBooking.isEnabled = state.selectedSlot != null && !state.isLoading

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: ServiceBookingEvent) {
        when (event) {
            is ServiceBookingEvent.ShowError -> {
                showError(event.message)
            }
            is ServiceBookingEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is ServiceBookingEvent.BookingCreated -> {
                showToast("预约成功！")
                // 可以跳转到预约详情页面
                finish()
            }
            is ServiceBookingEvent.UrgentBookingCreated -> {
                showToast("紧急预约已提交，请等待确认")
                finish()
            }
        }
    }

    private fun showDatePicker() {
        val calendar = Calendar.getInstance()
        calendar.time = selectedDate

        val datePickerDialog = DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                val newCalendar = Calendar.getInstance()
                newCalendar.set(year, month, dayOfMonth)
                selectedDate = newCalendar.time
                binding.textViewSelectedDate.text = selectedDate.formatDate()
                viewModel.loadAvailableSlots(selectedServiceType, selectedDate, isUrgentService)
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        )

        // 设置最小日期为今天
        datePickerDialog.datePicker.minDate = System.currentTimeMillis()
        // 设置最大日期为30天后
        datePickerDialog.datePicker.maxDate = System.currentTimeMillis() + 30L * 24 * 60 * 60 * 1000
        
        datePickerDialog.show()
    }

    private fun confirmBooking() {
        val customerName = binding.editTextCustomerName.text.toString().trim()
        val customerPhone = binding.editTextCustomerPhone.text.toString().trim()
        val customerAddress = binding.editTextCustomerAddress.text.toString().trim()
        val notes = binding.editTextNotes.text.toString().trim()

        if (customerName.isEmpty()) {
            binding.editTextCustomerName.error = "请输入客户姓名"
            return
        }

        if (customerPhone.isEmpty()) {
            binding.editTextCustomerPhone.error = "请输入联系电话"
            return
        }

        if (customerAddress.isEmpty()) {
            binding.editTextCustomerAddress.error = "请输入服务地址"
            return
        }

        viewModel.createBooking(
            customerName = customerName,
            customerPhone = customerPhone,
            customerAddress = customerAddress,
            notes = notes
        )
    }

    private fun createUrgentBooking() {
        val customerAddress = binding.editTextUrgentAddress.text.toString().trim()
        val urgentDescription = binding.editTextUrgentDescription.text.toString().trim()

        if (customerAddress.isEmpty()) {
            binding.editTextUrgentAddress.error = "请输入服务地址"
            return
        }

        if (urgentDescription.isEmpty()) {
            binding.editTextUrgentDescription.error = "请描述紧急情况"
            return
        }

        // 获取选中的紧急原因
        val urgentReason = when (binding.radioGroupUrgentReason.checkedRadioButtonId) {
            R.id.radioEmergency -> UrgentReason.EMERGENCY
            R.id.radioSpecialEvent -> UrgentReason.SPECIAL_EVENT
            R.id.radioLastMinute -> UrgentReason.LAST_MINUTE
            R.id.radioVipCustomer -> UrgentReason.VIP_CUSTOMER
            else -> UrgentReason.EMERGENCY
        }

        viewModel.createUrgentBooking(
            customerLocation = customerAddress,
            urgentReason = urgentReason,
            description = urgentDescription,
            priority = UrgentPriority.HIGH
        )
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
