package com.laundry.merchant.ui.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.core.content.ContextCompat
import com.laundry.merchant.R
import com.laundry.merchant.databinding.DialogPrivacyAgreementBinding

class PrivacyAgreementDialog(
    context: Context,
    private val onAgree: () -> Unit,
    private val onDisagree: () -> Unit
) : Dialog(context, R.style.Dialog_FullScreen) {

    private lateinit var binding: DialogPrivacyAgreementBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogPrivacyAgreementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupClickableText()
    }

    private fun setupViews() {
        // 设置不可取消
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        // 设置按钮点击事件
        binding.buttonAgree.setOnClickListener {
            onAgree()
            dismiss()
        }

        binding.buttonDisagree.setOnClickListener {
            onDisagree()
            dismiss()
        }

        binding.buttonClose.setOnClickListener {
            onDisagree()
            dismiss()
        }
    }

    private fun setupClickableText() {
        val fullText = "为了更好地保护您的权益，请仔细阅读并同意《用户协议》和《隐私政策》。\n\n" +
                "我们承诺严格保护您的个人信息安全，仅在必要时收集和使用您的信息，" +
                "并采取行业标准的安全措施保护您的数据。\n\n" +
                "点击"同意"即表示您已阅读并同意上述协议。"

        val spannableString = SpannableString(fullText)

        // 设置《用户协议》点击事件
        val userAgreementStart = fullText.indexOf("《用户协议》")
        val userAgreementEnd = userAgreementStart + "《用户协议》".length
        if (userAgreementStart != -1) {
            spannableString.setSpan(
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        showUserAgreement()
                    }
                },
                userAgreementStart,
                userAgreementEnd,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannableString.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(context, R.color.colorPrimary)),
                userAgreementStart,
                userAgreementEnd,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        // 设置《隐私政策》点击事件
        val privacyPolicyStart = fullText.indexOf("《隐私政策》")
        val privacyPolicyEnd = privacyPolicyStart + "《隐私政策》".length
        if (privacyPolicyStart != -1) {
            spannableString.setSpan(
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        showPrivacyPolicy()
                    }
                },
                privacyPolicyStart,
                privacyPolicyEnd,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannableString.setSpan(
                ForegroundColorSpan(ContextCompat.getColor(context, R.color.colorPrimary)),
                privacyPolicyStart,
                privacyPolicyEnd,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        binding.textViewContent.text = spannableString
        binding.textViewContent.movementMethod = LinkMovementMethod.getInstance()
    }

    private fun showUserAgreement() {
        // 显示用户协议详情
        val dialog = AgreementDetailDialog(
            context,
            "用户协议",
            getUserAgreementContent()
        )
        dialog.show()
    }

    private fun showPrivacyPolicy() {
        // 显示隐私政策详情
        val dialog = AgreementDetailDialog(
            context,
            "隐私政策",
            getPrivacyPolicyContent()
        )
        dialog.show()
    }

    private fun getUserAgreementContent(): String {
        return """
            用户协议
            
            1. 服务条款
            欢迎使用洗护商家服务平台。本协议是您与本平台之间关于使用本服务的法律协议。
            
            2. 账户注册
            您需要注册账户才能使用本服务。注册时请提供真实、准确的信息。
            
            3. 服务内容
            本平台为商家提供洗护服务管理工具，包括订单管理、财务管理等功能。
            
            4. 用户义务
            您承诺遵守相关法律法规，不得利用本服务从事违法活动。
            
            5. 知识产权
            本平台的所有内容均受知识产权法保护。
            
            6. 免责声明
            在法律允许的范围内，本平台不承担因使用本服务而产生的任何损失。
            
            7. 协议修改
            本平台有权随时修改本协议，修改后的协议将在平台上公布。
            
            8. 争议解决
            因本协议产生的争议，双方应友好协商解决。
            
            本协议自您点击同意之日起生效。
        """.trimIndent()
    }

    private fun getPrivacyPolicyContent(): String {
        return """
            隐私政策
            
            1. 信息收集
            我们可能收集以下信息：
            - 账户信息（手机号、姓名等）
            - 设备信息（设备型号、操作系统等）
            - 使用信息（操作记录、偏好设置等）
            
            2. 信息使用
            我们使用收集的信息用于：
            - 提供和改进服务
            - 处理交易和订单
            - 发送重要通知
            - 防范安全风险
            
            3. 信息共享
            除以下情况外，我们不会与第三方共享您的个人信息：
            - 获得您的明确同意
            - 法律法规要求
            - 保护用户或公众安全
            
            4. 信息安全
            我们采取以下措施保护您的信息：
            - 数据加密传输和存储
            - 访问权限控制
            - 定期安全审计
            - 员工保密培训
            
            5. 您的权利
            您有权：
            - 查看和更新个人信息
            - 删除账户和数据
            - 撤回授权同意
            - 投诉和举报
            
            6. Cookie使用
            我们使用Cookie改善用户体验，您可以在浏览器中管理Cookie设置。
            
            7. 未成年人保护
            我们不会故意收集未成年人的个人信息。
            
            8. 政策更新
            我们可能会更新本隐私政策，更新后会及时通知您。
            
            如有疑问，请联系我们的客服。
        """.trimIndent()
    }
}
