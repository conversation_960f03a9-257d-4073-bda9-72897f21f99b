package com.laundry.merchant.ui.auth

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.laundry.merchant.databinding.ActivityForgotPasswordBinding
import com.laundry.merchant.utils.hideKeyboard
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ForgotPasswordActivity : AppCompatActivity() {

    private lateinit var binding: ActivityForgotPasswordBinding
    private val viewModel: AuthViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityForgotPasswordBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        observeViewModel()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "忘记密码"

        // 设置输入监听
        binding.editTextPhone.addTextChangedListener(createTextWatcher())
        binding.editTextVerificationCode.addTextChangedListener(createTextWatcher())
        binding.editTextNewPassword.addTextChangedListener(createTextWatcher())

        // 设置点击监听
        binding.buttonSendCode.setOnClickListener {
            sendVerificationCode()
        }

        binding.buttonResetPassword.setOnClickListener {
            resetPassword()
        }

        // 设置根布局点击隐藏键盘
        binding.root.setOnClickListener {
            hideKeyboard()
        }
    }

    private fun createTextWatcher(): TextWatcher {
        return object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                updateButtonStates()
                clearErrors()
            }
        }
    }

    private fun updateButtonStates() {
        val phone = binding.editTextPhone.text.toString().trim()
        val verificationCode = binding.editTextVerificationCode.text.toString().trim()
        val newPassword = binding.editTextNewPassword.text.toString().trim()

        binding.buttonSendCode.isEnabled = isValidPhone(phone)
        binding.buttonResetPassword.isEnabled = phone.isNotEmpty() && 
                verificationCode.isNotEmpty() && 
                newPassword.isNotEmpty() &&
                isValidPhone(phone)
    }

    private fun isValidPhone(phone: String): Boolean {
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }

    private fun clearErrors() {
        binding.textInputLayoutPhone.error = null
        binding.textInputLayoutVerificationCode.error = null
        binding.textInputLayoutNewPassword.error = null
    }

    private fun sendVerificationCode() {
        val phone = binding.editTextPhone.text.toString().trim()
        
        if (!isValidPhone(phone)) {
            binding.textInputLayoutPhone.error = "请输入正确的手机号"
            return
        }

        hideKeyboard()
        viewModel.sendVerificationCode(phone)
    }

    private fun resetPassword() {
        val phone = binding.editTextPhone.text.toString().trim()
        val verificationCode = binding.editTextVerificationCode.text.toString().trim()
        val newPassword = binding.editTextNewPassword.text.toString().trim()

        hideKeyboard()
        viewModel.resetPassword(phone, verificationCode, newPassword)
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: AuthUiState) {
        // 更新加载状态
        binding.progressBar.visibility = if (state.isLoading) View.VISIBLE else View.GONE
        binding.buttonResetPassword.isEnabled = !state.isLoading

        // 更新发送验证码按钮状态
        if (state.isSendingCode) {
            binding.buttonSendCode.text = "发送中..."
            binding.buttonSendCode.isEnabled = false
        } else if (state.countdown > 0) {
            binding.buttonSendCode.text = "${state.countdown}s后重发"
            binding.buttonSendCode.isEnabled = false
        } else {
            binding.buttonSendCode.text = "发送验证码"
            updateButtonStates()
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: AuthEvent) {
        when (event) {
            is AuthEvent.ResetPasswordSuccess -> {
                showToast("密码重置成功")
                finish()
            }
            is AuthEvent.ResetPasswordError -> {
                showError(event.message)
            }
            is AuthEvent.VerificationCodeSent -> {
                showToast("验证码已发送")
            }
            is AuthEvent.SendCodeError -> {
                showError(event.message)
            }
            is AuthEvent.ValidationError -> {
                showValidationError(event.field, event.message)
            }
            else -> {}
        }
    }

    private fun showError(message: String) {
        showToast(message)
    }

    private fun showValidationError(field: String, message: String) {
        when (field) {
            "phone" -> binding.textInputLayoutPhone.error = message
            "verificationCode" -> binding.textInputLayoutVerificationCode.error = message
            "newPassword" -> binding.textInputLayoutNewPassword.error = message
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
