package com.laundry.merchant.di

import com.laundry.merchant.data.repository.AuthRepository
import com.laundry.merchant.data.repository.AuthRepositoryImpl
import com.laundry.merchant.data.repository.BookingRepository
import com.laundry.merchant.data.repository.BookingRepositoryImpl
import com.laundry.merchant.data.repository.CartRepository
import com.laundry.merchant.data.repository.CartRepositoryImpl
import com.laundry.merchant.data.repository.FavoriteRepository
import com.laundry.merchant.data.repository.FavoriteRepositoryImpl
import com.laundry.merchant.data.repository.FinanceRepository
import com.laundry.merchant.data.repository.FinanceRepositoryImpl
import com.laundry.merchant.data.repository.MembershipRepository
import com.laundry.merchant.data.repository.MembershipRepositoryImpl
import com.laundry.merchant.data.repository.MerchantRepository
import com.laundry.merchant.data.repository.MerchantRepositoryImpl
import com.laundry.merchant.data.repository.NotificationRepository
import com.laundry.merchant.data.repository.NotificationRepositoryImpl
import com.laundry.merchant.data.repository.OrderRepository
import com.laundry.merchant.data.repository.OrderRepositoryImpl
import com.laundry.merchant.data.repository.ProductRepository
import com.laundry.merchant.data.repository.ProductRepositoryImpl
import com.laundry.merchant.data.repository.PromotionRepository
import com.laundry.merchant.data.repository.PromotionRepositoryImpl
import com.laundry.merchant.data.repository.RankingRepository
import com.laundry.merchant.data.repository.RankingRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindAuthRepository(
        authRepositoryImpl: AuthRepositoryImpl
    ): AuthRepository

    @Binds
    @Singleton
    abstract fun bindMerchantRepository(
        merchantRepositoryImpl: MerchantRepositoryImpl
    ): MerchantRepository

    @Binds
    @Singleton
    abstract fun bindOrderRepository(
        orderRepositoryImpl: OrderRepositoryImpl
    ): OrderRepository

    @Binds
    @Singleton
    abstract fun bindFinanceRepository(
        financeRepositoryImpl: FinanceRepositoryImpl
    ): FinanceRepository

    @Binds
    @Singleton
    abstract fun bindPromotionRepository(
        promotionRepositoryImpl: PromotionRepositoryImpl
    ): PromotionRepository

    @Binds
    @Singleton
    abstract fun bindRankingRepository(
        rankingRepositoryImpl: RankingRepositoryImpl
    ): RankingRepository

    @Binds
    @Singleton
    abstract fun bindNotificationRepository(
        notificationRepositoryImpl: NotificationRepositoryImpl
    ): NotificationRepository

    @Binds
    @Singleton
    abstract fun bindFavoriteRepository(
        favoriteRepositoryImpl: FavoriteRepositoryImpl
    ): FavoriteRepository

    @Binds
    @Singleton
    abstract fun bindProductRepository(
        productRepositoryImpl: ProductRepositoryImpl
    ): ProductRepository

    @Binds
    @Singleton
    abstract fun bindBookingRepository(
        bookingRepositoryImpl: BookingRepositoryImpl
    ): BookingRepository

    @Binds
    @Singleton
    abstract fun bindCartRepository(
        cartRepositoryImpl: CartRepositoryImpl
    ): CartRepository

    @Binds
    @Singleton
    abstract fun bindMembershipRepository(
        membershipRepositoryImpl: MembershipRepositoryImpl
    ): MembershipRepository
}
