<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.LaundryUser" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/green_500</item>
        <item name="colorPrimaryVariant">@color/green_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/blue_500</item>
        <item name="colorSecondaryVariant">@color/blue_700</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/green_500</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar color. -->
        <item name="android:navigationBarColor">@color/white</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Background colors. -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Text colors. -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
        
        <!-- Window attributes. -->
        <item name="android:windowBackground">@color/background_light</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowActivityTransitions">true</item>
        
        <!-- Action bar. -->
        <item name="actionBarTheme">@style/ThemeOverlay.LaundryUser.ActionBar</item>
        <item name="actionBarStyle">@style/Widget.LaundryUser.ActionBar</item>
        
        <!-- Toolbar. -->
        <item name="toolbarStyle">@style/Widget.LaundryUser.Toolbar</item>
        
        <!-- Bottom navigation. -->
        <item name="bottomNavigationStyle">@style/Widget.LaundryUser.BottomNavigation</item>
        
        <!-- Button styles. -->
        <item name="materialButtonStyle">@style/Widget.LaundryUser.Button</item>
        <item name="borderlessButtonStyle">@style/Widget.LaundryUser.Button.Borderless</item>
        
        <!-- Text input styles. -->
        <item name="textInputStyle">@style/Widget.LaundryUser.TextInputLayout</item>
        
        <!-- Card style. -->
        <item name="materialCardViewStyle">@style/Widget.LaundryUser.CardView</item>
        
        <!-- FAB style. -->
        <item name="floatingActionButtonStyle">@style/Widget.LaundryUser.FloatingActionButton</item>
    </style>

    <!-- No Action Bar Theme -->
    <style name="Theme.LaundryUser.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Splash Theme -->
    <style name="Theme.LaundryUser.Splash" parent="Theme.LaundryUser.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
    </style>

    <!-- Fullscreen Theme -->
    <style name="Theme.LaundryUser.Fullscreen" parent="Theme.LaundryUser.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!-- Transparent Theme -->
    <style name="Theme.LaundryUser.Transparent" parent="Theme.LaundryUser.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <!-- Action Bar Theme -->
    <style name="ThemeOverlay.LaundryUser.ActionBar" parent="ThemeOverlay.Material3.ActionBar">
        <item name="colorPrimary">@color/green_500</item>
        <item name="colorOnPrimary">@color/white</item>
    </style>

    <!-- Widget Styles -->
    <style name="Widget.LaundryUser.ActionBar" parent="Widget.Material3.ActionBar.Solid">
        <item name="background">@color/green_500</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
    </style>

    <style name="Widget.LaundryUser.Toolbar" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/green_500</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
        <item name="android:theme">@style/ThemeOverlay.LaundryUser.Toolbar</item>
    </style>

    <style name="ThemeOverlay.LaundryUser.Toolbar" parent="ThemeOverlay.Material3.Toolbar.Surface">
        <item name="colorOnSurface">@color/white</item>
        <item name="actionMenuTextColor">@color/white</item>
    </style>

    <style name="Widget.LaundryUser.BottomNavigation" parent="Widget.Material3.BottomNavigationView">
        <item name="android:background">@color/white</item>
        <item name="itemIconTint">@color/bottom_nav_color</item>
        <item name="itemTextColor">@color/bottom_nav_color</item>
        <item name="elevation">8dp</item>
    </style>

    <style name="Widget.LaundryUser.Button" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/green_500</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>

    <style name="Widget.LaundryUser.Button.Borderless" parent="Widget.Material3.Button.TextButton">
        <item name="android:textColor">@color/green_500</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>

    <style name="Widget.LaundryUser.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/green_500</item>
        <item name="strokeColor">@color/green_500</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>

    <style name="Widget.LaundryUser.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/green_500</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
    </style>

    <style name="Widget.LaundryUser.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/white</item>
    </style>

    <style name="Widget.LaundryUser.FloatingActionButton" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="backgroundTint">@color/green_500</item>
        <item name="tint">@color/white</item>
        <item name="elevation">6dp</item>
    </style>

    <!-- Text Styles -->
    <style name="TextAppearance.LaundryUser.Headline1" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textSize">32sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="TextAppearance.LaundryUser.Headline2" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
    </style>

    <style name="TextAppearance.LaundryUser.Headline3" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>

    <style name="TextAppearance.LaundryUser.Body1" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>

    <style name="TextAppearance.LaundryUser.Body2" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>

    <style name="TextAppearance.LaundryUser.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_hint</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>

    <!-- Dialog Styles -->
    <style name="Theme.LaundryUser.Dialog" parent="ThemeOverlay.Material3.Dialog">
        <item name="colorPrimary">@color/green_500</item>
        <item name="colorSecondary">@color/blue_500</item>
        <item name="android:windowBackground">@drawable/dialog_background</item>
    </style>

    <!-- Bottom Sheet Styles -->
    <style name="Theme.LaundryUser.BottomSheet" parent="ThemeOverlay.Material3.BottomSheetDialog">
        <item name="colorPrimary">@color/green_500</item>
        <item name="colorSecondary">@color/blue_500</item>
        <item name="android:windowBackground">@drawable/bottom_sheet_background</item>
    </style>

    <!-- Chip Styles -->
    <style name="Widget.LaundryUser.Chip" parent="Widget.Material3.Chip.Filter">
        <item name="chipBackgroundColor">@color/chip_background_color</item>
        <item name="android:textColor">@color/chip_text_color</item>
        <item name="chipStrokeColor">@color/green_500</item>
        <item name="chipStrokeWidth">1dp</item>
    </style>

    <!-- Tab Layout Styles -->
    <style name="Widget.LaundryUser.TabLayout" parent="Widget.Material3.TabLayout">
        <item name="tabIndicatorColor">@color/green_500</item>
        <item name="tabSelectedTextColor">@color/green_500</item>
        <item name="tabTextColor">@color/text_secondary</item>
        <item name="android:background">@color/white</item>
    </style>

    <!-- Progress Bar Styles -->
    <style name="Widget.LaundryUser.ProgressBar" parent="Widget.Material3.CircularProgressIndicator">
        <item name="indicatorColor">@color/green_500</item>
        <item name="trackColor">@color/gray_200</item>
    </style>

    <!-- Switch Styles -->
    <style name="Widget.LaundryUser.Switch" parent="Widget.Material3.CompoundButton.Switch">
        <item name="thumbTint">@color/switch_thumb_color</item>
        <item name="trackTint">@color/switch_track_color</item>
    </style>

    <!-- Slider Styles -->
    <style name="Widget.LaundryUser.Slider" parent="Widget.Material3.Slider">
        <item name="activeTrackColor">@color/green_500</item>
        <item name="inactiveTrackColor">@color/gray_300</item>
        <item name="thumbColor">@color/green_500</item>
    </style>
</resources>
