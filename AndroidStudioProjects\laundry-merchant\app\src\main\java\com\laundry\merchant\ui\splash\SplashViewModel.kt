package com.laundry.merchant.ui.splash

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SplashViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _navigationEvent = MutableSharedFlow<SplashNavigationEvent>()
    val navigationEvent: SharedFlow<SplashNavigationEvent> = _navigationEvent.asSharedFlow()

    fun checkLoginStatus() {
        viewModelScope.launch {
            try {
                val isLoggedIn = authRepository.isLoggedIn().first()
                
                if (isLoggedIn) {
                    _navigationEvent.emit(SplashNavigationEvent.NavigateToMain)
                } else {
                    _navigationEvent.emit(SplashNavigationEvent.NavigateToLogin)
                }
            } catch (e: Exception) {
                // 如果检查登录状态失败，默认跳转到登录页面
                _navigationEvent.emit(SplashNavigationEvent.NavigateToLogin)
            }
        }
    }
}

sealed class SplashNavigationEvent {
    object NavigateToMain : SplashNavigationEvent()
    object NavigateToLogin : SplashNavigationEvent()
}
