package com.laundry.merchant.ui.statistics

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class StatisticsViewModel : ViewModel() {

    private val _statistics = MutableLiveData<Statistics>()
    val statistics: LiveData<Statistics> = _statistics

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    init {
        loadStatistics()
    }

    private fun loadStatistics() {
        _isLoading.value = true
        
        // Mock data - in real app, this would come from repository/API
        val mockStats = Statistics(
            todayOrders = 8,
            todayRevenue = 240.0,
            monthOrders = 156,
            monthRevenue = 4680.0,
            totalOrders = 1250,
            totalRevenue = 37500.0,
            averageRating = 4.6f,
            completionRate = 98.5f
        )
        
        _statistics.value = mockStats
        _isLoading.value = false
    }
}

data class Statistics(
    val todayOrders: Int,
    val todayRevenue: Double,
    val monthOrders: Int,
    val monthRevenue: Double,
    val totalOrders: Int,
    val totalRevenue: Double,
    val averageRating: Float,
    val completionRate: Float
)
