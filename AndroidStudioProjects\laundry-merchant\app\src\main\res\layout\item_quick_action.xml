<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="100dp"
    android:layout_height="100dp"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="8dp">

        <ImageView
            android:id="@+id/imageViewIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_add_order"
            android:tint="@color/colorPrimary" />

        <TextView
            android:id="@+id/textViewTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="新建订单"
            android:textColor="@color/gray_800"
            android:textSize="12sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/textViewDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="手动创建"
            android:textColor="@color/gray_500"
            android:textSize="10sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
