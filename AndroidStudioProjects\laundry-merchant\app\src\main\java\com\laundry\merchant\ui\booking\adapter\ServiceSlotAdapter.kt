package com.laundry.merchant.ui.booking.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.data.model.ServiceSlot
import com.laundry.merchant.databinding.ItemServiceSlotBinding
import com.laundry.merchant.utils.formatCurrency

class ServiceSlotAdapter(
    private val onSlotSelected: (ServiceSlot) -> Unit
) : ListAdapter<ServiceSlot, ServiceSlotAdapter.ViewHolder>(ServiceSlotDiffCallback()) {

    private var selectedSlotId: String? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemServiceSlotBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<ServiceSlot>) {
        submitList(newData)
    }

    fun setSelectedSlot(slotId: String?) {
        selectedSlotId = slotId
        notifyDataSetChanged()
    }

    inner class ViewHolder(private val binding: ItemServiceSlotBinding) : 
        RecyclerView.ViewHolder(binding.root) {

        fun bind(slot: ServiceSlot) {
            // 设置时间
            binding.textViewTime.text = "${slot.startTime} - ${slot.endTime}"
            
            // 设置价格
            binding.textViewPrice.text = slot.price.formatCurrency()
            
            // 设置紧急价格（如果有）
            if (slot.urgentPrice != null && slot.urgentPrice != slot.price) {
                binding.textViewUrgentPrice.visibility = android.view.View.VISIBLE
                binding.textViewUrgentPrice.text = "紧急: ${slot.urgentPrice.formatCurrency()}"
            } else {
                binding.textViewUrgentPrice.visibility = android.view.View.GONE
            }
            
            // 设置可用状态
            val context = binding.root.context
            when {
                !slot.isAvailable -> {
                    binding.textViewStatus.text = "不可用"
                    binding.textViewStatus.setTextColor(
                        ContextCompat.getColor(context, R.color.red_500)
                    )
                    binding.root.isEnabled = false
                    binding.root.alpha = 0.5f
                }
                slot.isFullyBooked() -> {
                    binding.textViewStatus.text = "已满"
                    binding.textViewStatus.setTextColor(
                        ContextCompat.getColor(context, R.color.orange_500)
                    )
                    binding.root.isEnabled = false
                    binding.root.alpha = 0.5f
                }
                slot.getAvailableSlots() <= 2 -> {
                    binding.textViewStatus.text = "仅剩${slot.getAvailableSlots()}个"
                    binding.textViewStatus.setTextColor(
                        ContextCompat.getColor(context, R.color.orange_500)
                    )
                    binding.root.isEnabled = true
                    binding.root.alpha = 1.0f
                }
                else -> {
                    binding.textViewStatus.text = "可预约"
                    binding.textViewStatus.setTextColor(
                        ContextCompat.getColor(context, R.color.green_500)
                    )
                    binding.root.isEnabled = true
                    binding.root.alpha = 1.0f
                }
            }
            
            // 设置选中状态
            val isSelected = slot.id == selectedSlotId
            if (isSelected) {
                binding.root.setBackgroundResource(R.drawable.slot_selected_background)
                binding.textViewTime.setTextColor(
                    ContextCompat.getColor(context, R.color.colorPrimary)
                )
            } else {
                binding.root.setBackgroundResource(R.drawable.slot_normal_background)
                binding.textViewTime.setTextColor(
                    ContextCompat.getColor(context, R.color.gray_800)
                )
            }
            
            // 设置点击事件
            binding.root.setOnClickListener {
                if (slot.isAvailable && !slot.isFullyBooked()) {
                    selectedSlotId = slot.id
                    onSlotSelected(slot)
                    notifyDataSetChanged()
                }
            }
        }
    }

    private class ServiceSlotDiffCallback : DiffUtil.ItemCallback<ServiceSlot>() {
        override fun areItemsTheSame(oldItem: ServiceSlot, newItem: ServiceSlot): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: ServiceSlot, newItem: ServiceSlot): Boolean {
            return oldItem == newItem
        }
    }
}
