<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 工具栏 -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- 统计信息栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:elevation="2dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <TextView
            android:id="@+id/textViewUnreadCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="未读 0 条"
            android:textColor="@color/gray_600"
            android:textSize="14sp" />

        <Button
            android:id="@+id/buttonMarkAllRead"
            style="@style/Button.LaundryMerchant.Borderless"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_marginEnd="8dp"
            android:enabled="false"
            android:text="全部已读"
            android:textSize="12sp" />

        <Button
            android:id="@+id/buttonClearAll"
            style="@style/Button.LaundryMerchant.Borderless"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:enabled="false"
            android:text="清空"
            android:textColor="@color/red_500"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- 分类标签 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        app:tabIndicatorColor="@color/colorPrimary"
        app:tabSelectedTextColor="@color/colorPrimary"
        app:tabTextColor="@color/gray_600" />

    <!-- 通知列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewNotifications"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 空状态视图 -->
    <LinearLayout
        android:id="@+id/emptyView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginBottom="16dp"
            android:alpha="0.3"
            android:src="@drawable/ic_notifications"
            app:tint="@color/gray_400" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="暂无通知"
            android:textColor="@color/gray_500"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="通知消息会在这里显示"
            android:textColor="@color/gray_400"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>
