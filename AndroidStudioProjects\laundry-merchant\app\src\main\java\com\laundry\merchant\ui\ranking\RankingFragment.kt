package com.laundry.merchant.ui.ranking

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.laundry.merchant.databinding.FragmentRankingBinding
import com.laundry.merchant.ui.ranking.adapter.RankingAdapter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class RankingFragment : Fragment() {

    private var _binding: FragmentRankingBinding? = null
    private val binding get() = _binding!!

    private val viewModel: RankingViewModel by viewModels()
    
    private lateinit var rankingAdapter: RankingAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRankingBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupViews()
        setupRecyclerView()
        setupTabs()
        observeViewModel()
        
        // 加载数据
        viewModel.loadRankingData()
    }

    private fun setupViews() {
        // 设置刷新监听
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshData()
        }
        
        // 设置时间筛选
        binding.chipToday.setOnClickListener {
            viewModel.filterByPeriod("today")
        }
        
        binding.chipWeek.setOnClickListener {
            viewModel.filterByPeriod("week")
        }
        
        binding.chipMonth.setOnClickListener {
            viewModel.filterByPeriod("month")
        }
        
        // 设置地区筛选
        binding.buttonAreaFilter.setOnClickListener {
            showAreaFilterDialog()
        }
    }

    private fun setupRecyclerView() {
        rankingAdapter = RankingAdapter { merchant ->
            navigateToMerchantDetail(merchant.id)
        }
        
        binding.recyclerViewRanking.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = rankingAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("综合排名"))
            addTab(newTab().setText("订单量"))
            addTab(newTab().setText("营业额"))
            addTab(newTab().setText("好评率"))
            addTab(newTab().setText("响应速度"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    val rankingType = when (tab?.position) {
                        0 -> "comprehensive"
                        1 -> "orders"
                        2 -> "revenue"
                        3 -> "rating"
                        4 -> "response_time"
                        else -> "comprehensive"
                    }
                    viewModel.filterByType(rankingType)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
        
        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: RankingUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading
        
        // 更新我的排名信息
        state.myRanking?.let { ranking ->
            binding.textMyRank.text = "第${ranking.rank}名"
            binding.textMyScore.text = "${String.format("%.1f", ranking.score)}分"
            binding.textMyOrderCount.text = "${ranking.orderCount}单"
            binding.textMyRevenue.text = "¥${String.format("%.0f", ranking.revenue)}"
            binding.textMyRating.text = "${String.format("%.1f", ranking.rating)}分"
            
            // 设置排名变化
            when {
                ranking.rankChange > 0 -> {
                    binding.textRankChange.text = "↑${ranking.rankChange}"
                    binding.textRankChange.setTextColor(
                        androidx.core.content.ContextCompat.getColor(requireContext(), com.laundry.merchant.R.color.green_500)
                    )
                }
                ranking.rankChange < 0 -> {
                    binding.textRankChange.text = "↓${Math.abs(ranking.rankChange)}"
                    binding.textRankChange.setTextColor(
                        androidx.core.content.ContextCompat.getColor(requireContext(), com.laundry.merchant.R.color.red_500)
                    )
                }
                else -> {
                    binding.textRankChange.text = "-"
                    binding.textRankChange.setTextColor(
                        androidx.core.content.ContextCompat.getColor(requireContext(), com.laundry.merchant.R.color.gray_500)
                    )
                }
            }
        }
        
        // 更新排行榜列表
        rankingAdapter.updateData(state.rankings)
        
        // 更新筛选状态
        binding.chipToday.isChecked = state.currentPeriod == "today"
        binding.chipWeek.isChecked = state.currentPeriod == "week"
        binding.chipMonth.isChecked = state.currentPeriod == "month"
        
        // 更新地区筛选显示
        binding.textAreaFilter.text = state.currentArea ?: "全部地区"
        
        // 更新空状态
        binding.emptyView.visibility = if (state.rankings.isEmpty() && !state.isLoading) {
            View.VISIBLE
        } else {
            View.GONE
        }
        
        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: RankingEvent) {
        when (event) {
            is RankingEvent.ShowError -> {
                showError(event.message)
            }
            is RankingEvent.ShowTip -> {
                showTip(event.message)
            }
            is RankingEvent.NavigateToImprovement -> {
                navigateToImprovementSuggestions()
            }
        }
    }

    private fun showAreaFilterDialog() {
        // TODO: 显示地区筛选对话框
    }

    private fun navigateToMerchantDetail(merchantId: String) {
        // TODO: 导航到商家详情页面
    }

    private fun navigateToImprovementSuggestions() {
        // TODO: 导航到改进建议页面
    }

    private fun showError(message: String) {
        // TODO: 显示错误信息
    }

    private fun showTip(message: String) {
        // TODO: 显示提示信息
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
