{"version": "0.2.0", "configurations": [{"name": "Launch Android App", "type": "android", "request": "launch", "appSrcRoot": "${workspaceRoot}/app/src/main", "apkFile": "${workspaceRoot}/app/build/outputs/apk/debug/app-debug.apk", "adbPath": "${env:ANDROID_HOME}/platform-tools/adb", "device": "", "preLaunchTask": "android: build debug"}, {"name": "Attach to Android Process", "type": "android", "request": "attach", "appSrcRoot": "${workspaceRoot}/app/src/main", "adbPath": "${env:ANDROID_HOME}/platform-tools/adb", "processId": "${command:PickAndroidProcess}"}, {"name": "Launch on Emulator", "type": "android", "request": "launch", "appSrcRoot": "${workspaceRoot}/app/src/main", "apkFile": "${workspaceRoot}/app/build/outputs/apk/debug/app-debug.apk", "adbPath": "${env:ANDROID_HOME}/platform-tools/adb", "device": "emulator", "preLaunchTask": "android: build debug"}, {"name": "Launch on Physical Device", "type": "android", "request": "launch", "appSrcRoot": "${workspaceRoot}/app/src/main", "apkFile": "${workspaceRoot}/app/build/outputs/apk/debug/app-debug.apk", "adbPath": "${env:ANDROID_HOME}/platform-tools/adb", "device": "device", "preLaunchTask": "android: build debug"}]}