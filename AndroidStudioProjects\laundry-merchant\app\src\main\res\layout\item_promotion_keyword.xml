<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 关键词头部 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewKeyword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="洗衣服务"
                    android:textColor="@color/gray_800"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textViewStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="投放中"
                        android:textColor="@color/green_500"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textViewQuality"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="质量分: 8"
                        android:textColor="@color/green_500"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/textViewPosition"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="排名: 2.3"
                        android:textColor="@color/gray_500"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

            <Switch
                android:id="@+id/switchStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- 出价和数据 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewBid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="出价: ¥1.50"
                    android:textColor="@color/blue_500"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textViewClicks"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="点击: 35"
                        android:textColor="@color/gray_600"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/textViewImpressions"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="展现: 420"
                        android:textColor="@color/gray_600"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/textViewCost"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="消费: ¥52.50"
                        android:textColor="@color/orange_500"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

            <!-- 出价调整按钮 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/buttonDecreaseBid"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginEnd="4dp"
                    android:background="@drawable/button_outline_background"
                    android:text="-"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <Button
                    android:id="@+id/buttonIncreaseBid"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="@drawable/button_outline_background"
                    android:text="+"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
