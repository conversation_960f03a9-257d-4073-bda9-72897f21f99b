package com.laundry.user.network

import com.laundry.user.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface ApiService {
    
    // ==================== 用户认证 ====================
    
    @POST(ApiConfig.Endpoints.LOGIN)
    suspend fun login(
        @Body request: LoginRequest
    ): Response<ApiResponse<LoginResponse>>
    
    @POST(ApiConfig.Endpoints.REGISTER)
    suspend fun register(
        @Body request: RegisterRequest
    ): Response<ApiResponse<RegisterResponse>>
    
    @POST(ApiConfig.Endpoints.REFRESH_TOKEN)
    suspend fun refreshToken(
        @Body request: RefreshTokenRequest
    ): Response<ApiResponse<TokenResponse>>
    
    @POST(ApiConfig.Endpoints.LOGOUT)
    suspend fun logout(): Response<ApiResponse<Unit>>
    
    // ==================== 用户信息 ====================
    
    @GET(ApiConfig.Endpoints.USER_PROFILE)
    suspend fun getUserProfile(): Response<ApiResponse<UserProfile>>
    
    @PUT(ApiConfig.Endpoints.UPDATE_PROFILE)
    suspend fun updateProfile(
        @Body request: UpdateProfileRequest
    ): Response<ApiResponse<UserProfile>>
    
    @GET(ApiConfig.Endpoints.USER_ADDRESSES)
    suspend fun getUserAddresses(): Response<ApiResponse<List<UserAddress>>>
    
    @POST(ApiConfig.Endpoints.USER_ADDRESSES)
    suspend fun addAddress(
        @Body request: AddAddressRequest
    ): Response<ApiResponse<UserAddress>>
    
    // ==================== 服务相关 ====================
    
    @GET(ApiConfig.Endpoints.SERVICES)
    suspend fun getServices(
        @Query(ApiConfig.Params.PAGE) page: Int = 0,
        @Query(ApiConfig.Params.SIZE) size: Int = 20,
        @Query(ApiConfig.Params.CATEGORY) category: String? = null,
        @Query(ApiConfig.Params.LATITUDE) lat: Double? = null,
        @Query(ApiConfig.Params.LONGITUDE) lng: Double? = null,
        @Query(ApiConfig.Params.RADIUS) radius: Int? = null,
        @Query(ApiConfig.Params.MIN_PRICE) minPrice: Double? = null,
        @Query(ApiConfig.Params.MAX_PRICE) maxPrice: Double? = null,
        @Query(ApiConfig.Params.RATING) rating: Double? = null,
        @Query(ApiConfig.Params.PROMOTED) promoted: Boolean? = null,
        @Query(ApiConfig.Params.SORT) sort: String = "relevance"
    ): Response<ApiResponse<PageResponse<Service>>>
    
    @GET(ApiConfig.Endpoints.SERVICE_SEARCH)
    suspend fun searchServices(
        @Query(ApiConfig.Params.KEYWORD) keyword: String,
        @Query(ApiConfig.Params.PAGE) page: Int = 0,
        @Query(ApiConfig.Params.SIZE) size: Int = 20,
        @Query(ApiConfig.Params.LATITUDE) lat: Double? = null,
        @Query(ApiConfig.Params.LONGITUDE) lng: Double? = null,
        @Query(ApiConfig.Params.SORT) sort: String = "relevance"
    ): Response<ApiResponse<PageResponse<Service>>>
    
    @GET(ApiConfig.Endpoints.SERVICE_DETAILS)
    suspend fun getServiceDetails(
        @Path("id") serviceId: String
    ): Response<ApiResponse<ServiceDetail>>
    
    @GET(ApiConfig.Endpoints.SERVICE_CATEGORIES)
    suspend fun getServiceCategories(): Response<ApiResponse<List<ServiceCategory>>>
    
    // ==================== 订单相关 ====================
    
    @GET(ApiConfig.Endpoints.ORDERS)
    suspend fun getOrders(
        @Query(ApiConfig.Params.PAGE) page: Int = 0,
        @Query(ApiConfig.Params.SIZE) size: Int = 20,
        @Query("status") status: String? = null
    ): Response<ApiResponse<PageResponse<Order>>>
    
    @POST(ApiConfig.Endpoints.CREATE_ORDER)
    suspend fun createOrder(
        @Body request: CreateOrderRequest
    ): Response<ApiResponse<Order>>
    
    @GET(ApiConfig.Endpoints.ORDER_DETAILS)
    suspend fun getOrderDetails(
        @Path("id") orderId: String
    ): Response<ApiResponse<OrderDetail>>
    
    @PUT(ApiConfig.Endpoints.ORDER_STATUS)
    suspend fun updateOrderStatus(
        @Path("id") orderId: String,
        @Body request: UpdateOrderStatusRequest
    ): Response<ApiResponse<Order>>
    
    @POST(ApiConfig.Endpoints.CANCEL_ORDER)
    suspend fun cancelOrder(
        @Path("id") orderId: String,
        @Body request: CancelOrderRequest
    ): Response<ApiResponse<Order>>
    
    // ==================== 支付相关 ====================
    
    @POST(ApiConfig.Endpoints.CREATE_PAYMENT)
    suspend fun createPayment(
        @Body request: CreatePaymentRequest
    ): Response<ApiResponse<PaymentResponse>>
    
    @GET(ApiConfig.Endpoints.PAYMENT_STATUS)
    suspend fun getPaymentStatus(
        @Path("id") paymentId: String
    ): Response<ApiResponse<PaymentStatus>>
    
    // ==================== 评价相关 ====================
    
    @GET(ApiConfig.Endpoints.REVIEWS)
    suspend fun getReviews(
        @Query("service_id") serviceId: String? = null,
        @Query("merchant_id") merchantId: String? = null,
        @Query(ApiConfig.Params.PAGE) page: Int = 0,
        @Query(ApiConfig.Params.SIZE) size: Int = 20
    ): Response<ApiResponse<PageResponse<Review>>>
    
    @POST(ApiConfig.Endpoints.CREATE_REVIEW)
    suspend fun createReview(
        @Body request: CreateReviewRequest
    ): Response<ApiResponse<Review>>
    
    @Multipart
    @POST(ApiConfig.Endpoints.REVIEW_IMAGES)
    suspend fun uploadReviewImages(
        @Part("review_id") reviewId: String,
        @Part images: List<okhttp3.MultipartBody.Part>
    ): Response<ApiResponse<List<String>>>
    
    // ==================== 收藏相关 ====================
    
    @GET(ApiConfig.Endpoints.FAVORITES)
    suspend fun getFavorites(
        @Query(ApiConfig.Params.PAGE) page: Int = 0,
        @Query(ApiConfig.Params.SIZE) size: Int = 20
    ): Response<ApiResponse<PageResponse<Favorite>>>
    
    @POST(ApiConfig.Endpoints.ADD_FAVORITE)
    suspend fun addFavorite(
        @Body request: AddFavoriteRequest
    ): Response<ApiResponse<Favorite>>
    
    @DELETE(ApiConfig.Endpoints.REMOVE_FAVORITE)
    suspend fun removeFavorite(
        @Path("id") favoriteId: String
    ): Response<ApiResponse<Unit>>
    
    // ==================== 积分和优惠券 ====================
    
    @GET(ApiConfig.Endpoints.POINTS)
    suspend fun getPointsHistory(
        @Query(ApiConfig.Params.PAGE) page: Int = 0,
        @Query(ApiConfig.Params.SIZE) size: Int = 20
    ): Response<ApiResponse<PageResponse<PointsRecord>>>
    
    @GET(ApiConfig.Endpoints.COUPONS)
    suspend fun getCoupons(
        @Query("status") status: String? = null
    ): Response<ApiResponse<List<Coupon>>>
    
    @POST(ApiConfig.Endpoints.USE_COUPON)
    suspend fun useCoupon(
        @Path("id") couponId: String,
        @Body request: UseCouponRequest
    ): Response<ApiResponse<CouponUsage>>
    
    // ==================== 消息和通知 ====================
    
    @GET(ApiConfig.Endpoints.MESSAGES)
    suspend fun getMessages(
        @Query("conversation_id") conversationId: String? = null,
        @Query(ApiConfig.Params.PAGE) page: Int = 0,
        @Query(ApiConfig.Params.SIZE) size: Int = 50
    ): Response<ApiResponse<PageResponse<Message>>>
    
    @POST(ApiConfig.Endpoints.MESSAGES)
    suspend fun sendMessage(
        @Body request: SendMessageRequest
    ): Response<ApiResponse<Message>>
    
    @GET(ApiConfig.Endpoints.NOTIFICATIONS)
    suspend fun getNotifications(
        @Query(ApiConfig.Params.PAGE) page: Int = 0,
        @Query(ApiConfig.Params.SIZE) size: Int = 20
    ): Response<ApiResponse<PageResponse<Notification>>>
    
    @PUT(ApiConfig.Endpoints.MARK_READ)
    suspend fun markNotificationAsRead(
        @Path("id") notificationId: String
    ): Response<ApiResponse<Unit>>
    
    // ==================== 文件上传 ====================
    
    @Multipart
    @POST(ApiConfig.Endpoints.UPLOAD_IMAGE)
    suspend fun uploadImage(
        @Part image: okhttp3.MultipartBody.Part,
        @Part("type") type: String
    ): Response<ApiResponse<UploadResponse>>
    
    @Multipart
    @POST(ApiConfig.Endpoints.UPLOAD_FILE)
    suspend fun uploadFile(
        @Part file: okhttp3.MultipartBody.Part,
        @Part("type") type: String
    ): Response<ApiResponse<UploadResponse>>
}

// 请求数据类
data class LoginRequest(
    val phone: String,
    val password: String,
    val deviceId: String,
    val deviceType: String = "android"
)

data class RegisterRequest(
    val phone: String,
    val password: String,
    val verificationCode: String,
    val nickname: String,
    val deviceId: String,
    val deviceType: String = "android"
)

data class RefreshTokenRequest(
    val refreshToken: String
)

data class UpdateProfileRequest(
    val nickname: String?,
    val avatar: String?,
    val gender: String?,
    val birthday: String?
)

data class AddAddressRequest(
    val name: String,
    val phone: String,
    val province: String,
    val city: String,
    val district: String,
    val address: String,
    val latitude: Double,
    val longitude: Double,
    val isDefault: Boolean = false
)

data class CreateOrderRequest(
    val serviceId: String,
    val merchantId: String,
    val addressId: String,
    val quantity: Int,
    val note: String?,
    val couponId: String?,
    val appointmentTime: String?
)

data class UpdateOrderStatusRequest(
    val status: String,
    val note: String?
)

data class CancelOrderRequest(
    val reason: String
)

data class CreatePaymentRequest(
    val orderId: String,
    val paymentMethod: String, // alipay, wechat, balance
    val amount: Double,
    val returnUrl: String?
)

data class CreateReviewRequest(
    val orderId: String,
    val serviceId: String,
    val merchantId: String,
    val rating: Int,
    val content: String,
    val tags: List<String>?
)

data class AddFavoriteRequest(
    val type: String, // service, merchant
    val targetId: String
)

data class UseCouponRequest(
    val orderId: String
)

data class SendMessageRequest(
    val conversationId: String,
    val content: String,
    val type: String = "text", // text, image, file
    val extra: Map<String, Any>?
)
