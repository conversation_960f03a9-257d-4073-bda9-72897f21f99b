package com.laundry.merchant.ui.ranking

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.repository.RankingRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RankingViewModel @Inject constructor(
    private val rankingRepository: RankingRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(RankingUiState())
    val uiState: StateFlow<RankingUiState> = _uiState.asStateFlow()

    private val _events = MutableSharedFlow<RankingEvent>()
    val events: SharedFlow<RankingEvent> = _events.asSharedFlow()

    private var allRankings: List<MerchantRanking> = emptyList()
    private var currentType: String = "comprehensive"
    private var currentPeriod: String = "month"
    private var currentArea: String? = null

    fun loadRankingData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val myRanking = rankingRepository.getMyRanking(currentType, currentPeriod, currentArea)
                allRankings = rankingRepository.getRankings(currentType, currentPeriod, currentArea)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    myRanking = myRanking,
                    rankings = allRankings,
                    currentType = currentType,
                    currentPeriod = currentPeriod,
                    currentArea = currentArea
                )
                
                // 检查排名变化并给出提示
                checkRankingChanges(myRanking)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载排行榜数据失败: ${e.message}"
                )
                _events.emit(RankingEvent.ShowError("加载排行榜数据失败"))
            }
        }
    }

    fun refreshData() {
        loadRankingData()
    }

    fun filterByType(type: String) {
        currentType = type
        loadRankingData()
    }

    fun filterByPeriod(period: String) {
        currentPeriod = period
        loadRankingData()
    }

    fun filterByArea(area: String?) {
        currentArea = area
        loadRankingData()
    }

    private fun checkRankingChanges(myRanking: MerchantRanking) {
        when {
            myRanking.rankChange > 0 -> {
                _events.emit(RankingEvent.ShowTip("恭喜！您的排名上升了${myRanking.rankChange}位"))
            }
            myRanking.rankChange < 0 -> {
                _events.emit(RankingEvent.ShowTip("您的排名下降了${Math.abs(myRanking.rankChange)}位，建议查看改进建议"))
            }
            myRanking.rank <= 3 -> {
                _events.emit(RankingEvent.ShowTip("恭喜进入前三名！继续保持优秀表现"))
            }
            myRanking.rank <= 10 -> {
                _events.emit(RankingEvent.ShowTip("您已进入前十名，再接再厉冲击前三！"))
            }
        }
    }
}

// UI状态数据类
data class RankingUiState(
    val isLoading: Boolean = false,
    val myRanking: MerchantRanking? = null,
    val rankings: List<MerchantRanking> = emptyList(),
    val currentType: String = "comprehensive",
    val currentPeriod: String = "month",
    val currentArea: String? = null,
    val error: String? = null
)

// 事件数据类
sealed class RankingEvent {
    data class ShowError(val message: String) : RankingEvent()
    data class ShowTip(val message: String) : RankingEvent()
    object NavigateToImprovement : RankingEvent()
}

// 商家排名数据类
data class MerchantRanking(
    val id: String,
    val name: String,
    val avatar: String?,
    val rank: Int,
    val rankChange: Int, // 正数表示上升，负数表示下降
    val score: Double,
    val orderCount: Int,
    val revenue: Double,
    val rating: Double,
    val responseTime: Int, // 平均响应时间（分钟）
    val completionRate: Double,
    val area: String,
    val isMyself: Boolean = false
)
