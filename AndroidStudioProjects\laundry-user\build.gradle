// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = "1.9.10"
    ext.hilt_version = "2.48"
    ext.compose_version = "1.5.4"
    ext.compose_compiler_version = "1.5.4"
    
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    
    dependencies {
        classpath "com.android.tools.build:gradle:8.1.2"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
        classpath "com.google.gms:google-services:4.4.0"
        classpath "com.google.firebase:firebase-crashlytics-gradle:2.9.9"
        
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    // SDK versions
    compileSdkVersion = 34
    targetSdkVersion = 34
    minSdkVersion = 24
    
    // App versions
    versionCode = 1
    versionName = "1.0.0"
    
    // Dependency versions
    appCompatVersion = "1.6.1"
    coreKtxVersion = "1.12.0"
    lifecycleVersion = "2.7.0"
    activityComposeVersion = "1.8.1"
    navigationVersion = "2.7.5"
    roomVersion = "2.6.0"
    retrofitVersion = "2.9.0"
    okhttpVersion = "4.12.0"
    gsonVersion = "2.10.1"
    glideVersion = "4.16.0"
    workManagerVersion = "2.8.1"
    timberVersion = "5.0.1"
    materialVersion = "1.10.0"
    constraintLayoutVersion = "2.1.4"
    recyclerViewVersion = "1.3.2"
    swipeRefreshLayoutVersion = "1.1.0"
    viewPager2Version = "1.0.0"
    fragmentVersion = "1.6.2"
    
    // Google Play Services
    playServicesLocationVersion = "21.0.1"
    playServicesMapsVersion = "18.2.0"
    
    // Firebase
    firebaseBomVersion = "32.6.0"
    
    // Testing
    junitVersion = "4.13.2"
    androidxJunitVersion = "1.1.5"
    espressoVersion = "3.5.1"
    mockitoVersion = "5.7.0"
    
    // Payment SDKs
    alipayVersion = "15.8.11"
    wechatPayVersion = "6.8.0"
}
