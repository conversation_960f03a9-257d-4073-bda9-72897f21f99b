package com.laundry.merchant.payment

data class PaymentRequest(
    val orderId: String,
    val amount: Double,
    val currency: String = "CNY",
    val subject: String,
    val description: String? = null,
    val userId: String,
    val notifyUrl: String? = null,
    val returnUrl: String? = null,
    val extraParams: Map<String, String> = emptyMap()
)

sealed class PaymentResult {
    data class Success(
        val orderId: String,
        val method: PaymentMethod,
        val transactionId: String,
        val amount: Double,
        val timestamp: Long
    ) : PaymentResult()
    
    data class Failed(
        val orderId: String,
        val method: PaymentMethod,
        val errorCode: String,
        val errorMessage: String
    ) : PaymentResult()
    
    data class Cancelled(
        val orderId: String,
        val method: PaymentMethod
    ) : PaymentResult()
    
    data class Pending(
        val orderId: String,
        val method: PaymentMethod
    ) : PaymentResult()
}

enum class PaymentMethod {
    ALIPAY,         // 支付宝
    WECHAT_PAY,     // 微信支付
    BANK_CARD,      // 银行卡
    WALLET,         // 余额
    POINTS,         // 积分
    CASH,           // 现金
    CREDIT_CARD,    // 信用卡
    APPLE_PAY,      // Apple Pay
    GOOGLE_PAY      // Google Pay
}

enum class PaymentStatus {
    PENDING,        // 待支付
    SUCCESS,        // 支付成功
    FAILED,         // 支付失败
    CANCELLED,      // 已取消
    REFUNDED,       // 已退款
    PARTIAL_REFUND, // 部分退款
    UNKNOWN         // 未知状态
}

data class RefundRequest(
    val transactionId: String,
    val orderId: String,
    val amount: Double,
    val reason: String,
    val originalMethod: PaymentMethod,
    val userId: String,
    val notifyUrl: String? = null
)

sealed class RefundResult {
    data class Success(
        val transactionId: String,
        val refundId: String,
        val amount: Double,
        val timestamp: Long
    ) : RefundResult()
    
    data class Failed(
        val transactionId: String,
        val errorCode: String,
        val errorMessage: String
    ) : RefundResult()
    
    data class Pending(
        val transactionId: String,
        val refundId: String
    ) : RefundResult()
}

data class PaymentMethodInfo(
    val method: PaymentMethod,
    val name: String,
    val icon: String,
    val isEnabled: Boolean = true,
    val fee: Double = 0.0,
    val feeType: FeeType = FeeType.NONE,
    val minAmount: Double = 0.0,
    val maxAmount: Double = Double.MAX_VALUE,
    val description: String? = null,
    val supportRefund: Boolean = true,
    val processingTime: String? = null
)

enum class FeeType {
    NONE,           // 无手续费
    FIXED,          // 固定手续费
    PERCENTAGE,     // 百分比手续费
    TIERED          // 阶梯手续费
}

data class PaymentTransaction(
    val id: String,
    val orderId: String,
    val userId: String,
    val method: PaymentMethod,
    val amount: Double,
    val currency: String,
    val status: PaymentStatus,
    val transactionId: String? = null,
    val thirdPartyTransactionId: String? = null,
    val createdAt: Long,
    val completedAt: Long? = null,
    val failureReason: String? = null,
    val refunds: List<RefundTransaction> = emptyList()
)

data class RefundTransaction(
    val id: String,
    val paymentTransactionId: String,
    val amount: Double,
    val reason: String,
    val status: RefundStatus,
    val refundId: String? = null,
    val thirdPartyRefundId: String? = null,
    val createdAt: Long,
    val completedAt: Long? = null,
    val failureReason: String? = null
)

enum class RefundStatus {
    PENDING,        // 退款中
    SUCCESS,        // 退款成功
    FAILED,         // 退款失败
    CANCELLED       // 退款取消
}

data class WalletInfo(
    val userId: String,
    val balance: Double,
    val frozenAmount: Double = 0.0,
    val availableAmount: Double = balance - frozenAmount,
    val currency: String = "CNY",
    val lastUpdated: Long
)

data class WalletTransaction(
    val id: String,
    val userId: String,
    val type: WalletTransactionType,
    val amount: Double,
    val balanceBefore: Double,
    val balanceAfter: Double,
    val description: String,
    val relatedOrderId: String? = null,
    val relatedTransactionId: String? = null,
    val createdAt: Long
)

enum class WalletTransactionType {
    RECHARGE,       // 充值
    PAYMENT,        // 支付
    REFUND,         // 退款
    WITHDRAW,       // 提现
    TRANSFER_IN,    // 转入
    TRANSFER_OUT,   // 转出
    REWARD,         // 奖励
    PENALTY,        // 扣款
    FREEZE,         // 冻结
    UNFREEZE        // 解冻
}

data class PaymentConfig(
    val alipayConfig: AlipayConfig? = null,
    val wechatConfig: WechatConfig? = null,
    val bankCardConfig: BankCardConfig? = null,
    val walletConfig: WalletConfig? = null
)

data class AlipayConfig(
    val appId: String,
    val privateKey: String,
    val publicKey: String,
    val signType: String = "RSA2",
    val charset: String = "UTF-8",
    val gatewayUrl: String = "https://openapi.alipay.com/gateway.do"
)

data class WechatConfig(
    val appId: String,
    val mchId: String,
    val apiKey: String,
    val certPath: String? = null,
    val notifyUrl: String
)

data class BankCardConfig(
    val merchantId: String,
    val secretKey: String,
    val gatewayUrl: String,
    val supportedBanks: List<String> = emptyList()
)

data class WalletConfig(
    val minRechargeAmount: Double = 1.0,
    val maxRechargeAmount: Double = 10000.0,
    val minWithdrawAmount: Double = 10.0,
    val maxWithdrawAmount: Double = 5000.0,
    val withdrawFeeRate: Double = 0.001, // 0.1%
    val withdrawFeeMin: Double = 1.0,
    val withdrawFeeMax: Double = 25.0
)
