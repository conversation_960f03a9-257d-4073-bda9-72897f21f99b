<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 搜索栏 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <androidx.appcompat.widget.SearchView
            android:id="@+id/searchView"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@android:color/transparent"
            app:iconifiedByDefault="false"
            app:queryHint="搜索订单号、客户姓名或电话" />

    </androidx.cardview.widget.CardView>

    <!-- 统计信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/textTotalCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="共0单"
            android:textColor="@color/gray_600"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textPendingCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="待处理0单"
            android:textColor="@color/orange_500"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- 状态标签 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        app:tabIndicatorColor="@color/colorPrimary"
        app:tabSelectedTextColor="@color/colorPrimary"
        app:tabTextColor="@color/gray_600" />

    <!-- 订单列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewOrders"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp" />

            <!-- 空状态 -->
            <LinearLayout
                android:id="@+id/emptyView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_empty_orders"
                    android:tint="@color/gray_400" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="暂无订单"
                    android:textColor="@color/gray_500"
                    android:textSize="16sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="订单会在这里显示"
                    android:textColor="@color/gray_400"
                    android:textSize="14sp" />

            </LinearLayout>

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>
