package com.laundry.user.ui.search

import android.os.Bundle
import android.widget.*
import androidx.appcompat.app.AppCompatActivity

class SearchActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val keyword = intent.getStringExtra("keyword") ?: ""
        
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(20, 20, 20, 20)
        }
        
        val titleText = TextView(this).apply {
            text = "搜索结果: $keyword"
            textSize = 20f
            setPadding(0, 0, 0, 20)
        }
        
        val contentText = TextView(this).apply {
            text = "搜索功能开发中..."
            textSize = 16f
        }
        
        layout.addView(titleText)
        layout.addView(contentText)
        
        setContentView(layout)
    }
}
