package com.laundry.merchant.websocket

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import okhttp3.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WebSocketManager @Inject constructor() {
    
    private val TAG = "WebSocketManager"
    private val gson = Gson()
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    private var webSocket: WebSocket? = null
    private var isConnected = false
    private var reconnectAttempts = 0
    private val maxReconnectAttempts = 5
    private val reconnectDelay = 5000L // 5秒
    
    private val _connectionState = MutableSharedFlow<ConnectionState>()
    val connectionState: SharedFlow<ConnectionState> = _connectionState.asSharedFlow()
    
    private val _messages = MutableSharedFlow<WebSocketMessage>()
    val messages: SharedFlow<WebSocketMessage> = _messages.asSharedFlow()
    
    private val client = OkHttpClient.Builder()
        .pingInterval(30, TimeUnit.SECONDS)
        .retryOnConnectionFailure(true)
        .build()

    fun connect(url: String, token: String) {
        if (isConnected) {
            Log.d(TAG, "WebSocket already connected")
            return
        }

        val request = Request.Builder()
            .url(url)
            .addHeader("Authorization", "Bearer $token")
            .build()

        webSocket = client.newWebSocket(request, createWebSocketListener())
        
        scope.launch {
            _connectionState.emit(ConnectionState.CONNECTING)
        }
    }

    fun disconnect() {
        webSocket?.close(1000, "Client disconnect")
        webSocket = null
        isConnected = false
        reconnectAttempts = 0
        
        scope.launch {
            _connectionState.emit(ConnectionState.DISCONNECTED)
        }
    }

    fun sendMessage(message: WebSocketMessage) {
        if (!isConnected) {
            Log.w(TAG, "WebSocket not connected, cannot send message")
            return
        }

        try {
            val json = gson.toJson(message)
            webSocket?.send(json)
            Log.d(TAG, "Message sent: $json")
        } catch (e: Exception) {
            Log.e(TAG, "Error sending message", e)
        }
    }

    private fun createWebSocketListener(): WebSocketListener {
        return object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket connected")
                isConnected = true
                reconnectAttempts = 0
                
                scope.launch {
                    _connectionState.emit(ConnectionState.CONNECTED)
                }
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "Message received: $text")
                
                try {
                    val message = gson.fromJson(text, WebSocketMessage::class.java)
                    scope.launch {
                        _messages.emit(message)
                    }
                } catch (e: JsonSyntaxException) {
                    Log.e(TAG, "Error parsing message: $text", e)
                }
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket closing: $code $reason")
                isConnected = false
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket closed: $code $reason")
                isConnected = false
                
                scope.launch {
                    _connectionState.emit(ConnectionState.DISCONNECTED)
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket error", t)
                isConnected = false
                
                scope.launch {
                    _connectionState.emit(ConnectionState.ERROR(t.message ?: "Unknown error"))
                    
                    // 尝试重连
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++
                        Log.d(TAG, "Attempting reconnect $reconnectAttempts/$maxReconnectAttempts")
                        
                        kotlinx.coroutines.delay(reconnectDelay)
                        // 这里需要重新获取URL和token进行重连
                        // reconnect()
                    }
                }
            }
        }
    }
}

sealed class ConnectionState {
    object CONNECTING : ConnectionState()
    object CONNECTED : ConnectionState()
    object DISCONNECTED : ConnectionState()
    data class ERROR(val message: String) : ConnectionState()
}

data class WebSocketMessage(
    val type: MessageType,
    val data: Any,
    val timestamp: Long = System.currentTimeMillis(),
    val messageId: String = java.util.UUID.randomUUID().toString()
)

enum class MessageType {
    // 订单相关
    ORDER_STATUS_UPDATE,
    ORDER_CREATED,
    ORDER_CANCELLED,
    ORDER_COMPLETED,
    
    // 预约相关
    BOOKING_STATUS_UPDATE,
    BOOKING_REMINDER,
    BOOKING_CANCELLED,
    
    // 商家状态
    MERCHANT_STATUS_UPDATE,
    MERCHANT_LOCATION_UPDATE,
    
    // 配送相关
    DELIVERY_STATUS_UPDATE,
    DELIVERY_LOCATION_UPDATE,
    DELIVERY_ARRIVED,
    
    // 系统通知
    SYSTEM_NOTIFICATION,
    PROMOTION_UPDATE,
    
    // 聊天消息
    CHAT_MESSAGE,
    TYPING_INDICATOR,
    
    // 心跳
    PING,
    PONG,
    
    // 用户状态
    USER_ONLINE,
    USER_OFFLINE
}

// 具体的消息数据类
data class OrderStatusUpdateData(
    val orderId: String,
    val oldStatus: String,
    val newStatus: String,
    val message: String? = null,
    val estimatedTime: String? = null
)

data class BookingStatusUpdateData(
    val bookingId: String,
    val oldStatus: String,
    val newStatus: String,
    val merchantName: String? = null,
    val estimatedArrival: String? = null
)

data class MerchantStatusUpdateData(
    val merchantId: String,
    val status: String,
    val currentLoad: Int,
    val estimatedWaitTime: Int,
    val location: LocationData? = null
)

data class DeliveryStatusUpdateData(
    val orderId: String,
    val deliveryId: String,
    val status: String,
    val location: LocationData? = null,
    val estimatedArrival: String? = null,
    val driverName: String? = null,
    val driverPhone: String? = null
)

data class LocationData(
    val latitude: Double,
    val longitude: Double,
    val address: String? = null,
    val accuracy: Float? = null
)

data class SystemNotificationData(
    val title: String,
    val message: String,
    val type: String,
    val actionUrl: String? = null,
    val imageUrl: String? = null
)

data class ChatMessageData(
    val chatId: String,
    val senderId: String,
    val senderName: String,
    val message: String,
    val messageType: String = "text", // text, image, file
    val attachmentUrl: String? = null
)
