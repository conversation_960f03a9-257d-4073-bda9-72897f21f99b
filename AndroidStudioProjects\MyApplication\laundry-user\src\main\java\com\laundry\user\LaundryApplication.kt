package com.laundry.user

import android.app.Application

class LaundryApplication : Application() {

    companion object {
        lateinit var instance: LaundryApplication
            private set
    }

    override fun onCreate() {
        super.onCreate()
        instance = this

        // 简化的初始化
        initializeCore()
    }

    private fun initializeCore() {
        // 设置全局异常处理器
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            // 记录崩溃信息
            exception.printStackTrace()

            // 重启应用
            restartApplication()
        }
    }

    private fun restartApplication() {
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP)
        intent?.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        android.os.Process.killProcess(android.os.Process.myPid())
    }
}
