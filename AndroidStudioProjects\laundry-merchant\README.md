# 洗护商家 Android App

一款专为洗护服务商家设计的移动端管理应用，提供订单管理、财务管理、投流管理、排行榜等核心功能。

## 功能特性

### 🏪 商家中心
- 实时业务数据展示（今日/月度订单、收入、评分）
- 投流状态监控
- 快捷操作菜单
- 最近订单概览

### 📋 订单管理
- 订单列表展示和状态筛选
- 订单搜索功能（订单号、客户信息）
- 订单状态流转管理
- 客户联系功能
- 紧急订单标识

### 💰 财务管理
- 账户余额管理（营业收入、推广余额）
- 收支统计分析
- 交易记录查询和筛选
- 充值/提现功能
- 余额不足提醒

### 📈 投流管理
- 投流状态控制
- 推广计划管理
- 关键词管理和出价调整
- 投流数据统计（点击率、转化率等）
- 预算管理

### 🏆 排行榜
- 多维度排名展示
- 时间和地区筛选
- 我的排名信息和变化趋势
- 排名提升建议

## 技术架构

### 开发语言
- **Kotlin** - 100% Kotlin 开发

### 架构模式
- **MVVM** - Model-View-ViewModel 架构
- **Repository Pattern** - 数据仓库模式
- **Clean Architecture** - 清洁架构原则

### 核心技术栈
- **UI Framework**: Android Jetpack (Fragment, ViewModel, LiveData, Navigation)
- **依赖注入**: Hilt
- **网络请求**: Retrofit + OkHttp
- **数据存储**: Room + DataStore
- **异步处理**: Kotlin Coroutines + Flow
- **图片加载**: Glide
- **JSON解析**: Gson

### 第三方库
- **Material Design Components** - UI组件
- **SwipeRefreshLayout** - 下拉刷新
- **ZXing** - 二维码扫描
- **MPAndroidChart** - 图表展示
- **Firebase** - 推送通知
- **WorkManager** - 后台任务

## 项目结构

```
app/
├── src/main/java/com/laundry/merchant/
│   ├── data/                    # 数据层
│   │   ├── local/              # 本地数据存储
│   │   ├── model/              # 数据模型
│   │   └── repository/         # 数据仓库
│   ├── di/                     # 依赖注入模块
│   ├── network/                # 网络层
│   ├── service/                # 后台服务
│   ├── ui/                     # UI层
│   │   ├── main/               # 主界面
│   │   ├── dashboard/          # 商家中心
│   │   ├── order/              # 订单管理
│   │   ├── finance/            # 财务管理
│   │   ├── promotion/          # 投流管理
│   │   └── ranking/            # 排行榜
│   ├── utils/                  # 工具类
│   └── worker/                 # 后台任务
└── src/main/res/               # 资源文件
    ├── layout/                 # 布局文件
    ├── drawable/               # 图标资源
    ├── values/                 # 值资源
    └── menu/                   # 菜单资源
```

## 开发环境

### 系统要求
- **Android Studio**: Arctic Fox 或更高版本
- **Kotlin**: 1.9.20
- **Gradle**: 8.2.0
- **Min SDK**: 24 (Android 7.0)
- **Target SDK**: 34 (Android 14)
- **Compile SDK**: 34

### 构建配置
```gradle
android {
    compileSdk 34
    
    defaultConfig {
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"
    }
    
    buildFeatures {
        viewBinding true
        dataBinding true
    }
}
```

## 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/laundry-merchant-android.git
cd laundry-merchant-android
```

### 2. 配置环境
- 确保安装了 Android Studio Arctic Fox 或更高版本
- 配置 Android SDK 和相关工具

### 3. 构建项目
```bash
./gradlew build
```

### 4. 运行应用
- 连接 Android 设备或启动模拟器
- 在 Android Studio 中点击 Run 按钮

## API 配置

在 `NetworkModule.kt` 中配置 API 基础地址：

```kotlin
private const val BASE_URL = "https://api.laundry-merchant.com/v1/"
```

## 权限说明

应用需要以下权限：
- **网络访问** - 数据同步和API调用
- **电话** - 联系客户功能
- **位置** - 获取商家位置信息
- **相机** - 扫码功能
- **存储** - 图片和文件保存
- **通知** - 接收重要消息提醒

## 版本历史

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现核心功能模块
- 支持订单管理、财务管理、投流管理、排行榜

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: [Your Name]
- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-repo/laundry-merchant-android

## 致谢

感谢所有为这个项目做出贡献的开发者和设计师。
