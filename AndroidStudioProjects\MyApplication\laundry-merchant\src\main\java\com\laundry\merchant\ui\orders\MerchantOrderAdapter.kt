package com.laundry.merchant.ui.orders

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.databinding.ItemMerchantOrderBinding
import com.laundry.merchant.model.Order
import com.laundry.merchant.model.OrderStatus
import java.text.SimpleDateFormat
import java.util.Locale

class MerchantOrderAdapter(
    private val onAcceptOrder: (Order) -> Unit,
    private val onUpdateStatus: (Order, OrderStatus) -> Unit,
    private val onOrderClick: (Order) -> Unit
) : ListAdapter<Order, MerchantOrderAdapter.OrderViewHolder>(OrderDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderViewHolder {
        val binding = ItemMerchantOrderBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OrderViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OrderViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class OrderViewHolder(
        private val binding: ItemMerchantOrderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        private val dateFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())

        fun bind(order: Order) {
            binding.apply {
                textOrderId.text = "订单号: ${order.id}"
                textCustomerName.text = "客户: ${order.userName}"
                textCustomerPhone.text = order.userPhone
                textOrderServices.text = order.services.joinToString(", ") { it.name }
                textOrderAmount.text = "¥${order.totalAmount}"
                textOrderTime.text = dateFormat.format(order.createdAt)
                textOrderStatus.text = getStatusText(order.status)
                textPickupAddress.text = "${order.pickupAddress.address} ${order.pickupAddress.detailAddress}"
                
                if (order.notes.isNotEmpty()) {
                    textOrderNotes.text = "备注: ${order.notes}"
                    textOrderNotes.visibility = View.VISIBLE
                } else {
                    textOrderNotes.visibility = View.GONE
                }
                
                // Set status color
                val statusColor = when (order.status) {
                    OrderStatus.PENDING -> R.color.status_pending
                    OrderStatus.ACCEPTED -> R.color.status_accepted
                    OrderStatus.PICKED_UP -> R.color.status_picked_up
                    OrderStatus.IN_PROGRESS -> R.color.status_in_progress
                    OrderStatus.READY -> R.color.status_ready
                    OrderStatus.DELIVERING -> R.color.status_delivering
                    OrderStatus.COMPLETED -> R.color.status_completed
                    OrderStatus.CANCELLED -> R.color.status_cancelled
                }
                
                textOrderStatus.setTextColor(
                    ContextCompat.getColor(root.context, statusColor)
                )
                
                // Setup action buttons based on order status
                setupActionButtons(order)
                
                root.setOnClickListener {
                    onOrderClick(order)
                }
            }
        }

        private fun setupActionButtons(order: Order) {
            binding.apply {
                when (order.status) {
                    OrderStatus.PENDING -> {
                        buttonAccept.visibility = View.VISIBLE
                        buttonReject.visibility = View.VISIBLE
                        buttonUpdateStatus.visibility = View.GONE
                        
                        buttonAccept.setOnClickListener {
                            onAcceptOrder(order)
                        }
                        
                        buttonReject.setOnClickListener {
                            onUpdateStatus(order, OrderStatus.CANCELLED)
                        }
                    }
                    OrderStatus.ACCEPTED -> {
                        buttonAccept.visibility = View.GONE
                        buttonReject.visibility = View.GONE
                        buttonUpdateStatus.visibility = View.VISIBLE
                        buttonUpdateStatus.text = "标记已取件"
                        
                        buttonUpdateStatus.setOnClickListener {
                            onUpdateStatus(order, OrderStatus.PICKED_UP)
                        }
                    }
                    OrderStatus.PICKED_UP -> {
                        buttonAccept.visibility = View.GONE
                        buttonReject.visibility = View.GONE
                        buttonUpdateStatus.visibility = View.VISIBLE
                        buttonUpdateStatus.text = "开始处理"
                        
                        buttonUpdateStatus.setOnClickListener {
                            onUpdateStatus(order, OrderStatus.IN_PROGRESS)
                        }
                    }
                    OrderStatus.IN_PROGRESS -> {
                        buttonAccept.visibility = View.GONE
                        buttonReject.visibility = View.GONE
                        buttonUpdateStatus.visibility = View.VISIBLE
                        buttonUpdateStatus.text = "标记完成"
                        
                        buttonUpdateStatus.setOnClickListener {
                            onUpdateStatus(order, OrderStatus.READY)
                        }
                    }
                    OrderStatus.READY -> {
                        buttonAccept.visibility = View.GONE
                        buttonReject.visibility = View.GONE
                        buttonUpdateStatus.visibility = View.VISIBLE
                        buttonUpdateStatus.text = "开始配送"
                        
                        buttonUpdateStatus.setOnClickListener {
                            onUpdateStatus(order, OrderStatus.DELIVERING)
                        }
                    }
                    OrderStatus.DELIVERING -> {
                        buttonAccept.visibility = View.GONE
                        buttonReject.visibility = View.GONE
                        buttonUpdateStatus.visibility = View.VISIBLE
                        buttonUpdateStatus.text = "确认送达"
                        
                        buttonUpdateStatus.setOnClickListener {
                            onUpdateStatus(order, OrderStatus.COMPLETED)
                        }
                    }
                    else -> {
                        buttonAccept.visibility = View.GONE
                        buttonReject.visibility = View.GONE
                        buttonUpdateStatus.visibility = View.GONE
                    }
                }
            }
        }

        private fun getStatusText(status: OrderStatus): String {
            return when (status) {
                OrderStatus.PENDING -> "待接单"
                OrderStatus.ACCEPTED -> "已接单"
                OrderStatus.PICKED_UP -> "已取件"
                OrderStatus.IN_PROGRESS -> "处理中"
                OrderStatus.READY -> "已完成"
                OrderStatus.DELIVERING -> "配送中"
                OrderStatus.COMPLETED -> "已送达"
                OrderStatus.CANCELLED -> "已取消"
            }
        }
    }

    private class OrderDiffCallback : DiffUtil.ItemCallback<Order>() {
        override fun areItemsTheSame(oldItem: Order, newItem: Order): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Order, newItem: Order): Boolean {
            return oldItem == newItem
        }
    }
}
