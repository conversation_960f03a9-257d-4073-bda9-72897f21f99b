<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 紧急标识 -->
            <View
                android:id="@+id/viewUrgentIndicator"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="8dp"
                android:background="@color/red_500"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textViewOrderId"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="ORD001"
                        android:textColor="@color/gray_800"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textViewTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="10:30"
                        android:textColor="@color/gray_500"
                        android:textSize="12sp" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textViewCustomerName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="张三"
                    android:textColor="@color/gray_600"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/textViewServiceName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="普通洗衣"
                    android:textColor="@color/gray_500"
                    android:textSize="12sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥25.00"
                    android:textColor="@color/green_500"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="待取件"
                    android:textColor="@color/orange_500"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
