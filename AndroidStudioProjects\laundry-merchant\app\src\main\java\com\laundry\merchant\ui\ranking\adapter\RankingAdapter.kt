package com.laundry.merchant.ui.ranking.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.ui.ranking.MerchantRanking
import com.laundry.merchant.utils.ImageUtils

class RankingAdapter(
    private val onMerchantClick: (MerchantRanking) -> Unit
) : ListAdapter<MerchantRanking, RankingAdapter.ViewHolder>(RankingDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_ranking, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<MerchantRanking>) {
        submitList(newData)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val rankTextView: TextView = itemView.findViewById(R.id.textViewRank)
        private val rankBadgeImageView: ImageView = itemView.findViewById(R.id.imageViewRankBadge)
        private val avatarImageView: ImageView = itemView.findViewById(R.id.imageViewAvatar)
        private val nameTextView: TextView = itemView.findViewById(R.id.textViewName)
        private val areaTextView: TextView = itemView.findViewById(R.id.textViewArea)
        private val scoreTextView: TextView = itemView.findViewById(R.id.textViewScore)
        private val orderCountTextView: TextView = itemView.findViewById(R.id.textViewOrderCount)
        private val revenueTextView: TextView = itemView.findViewById(R.id.textViewRevenue)
        private val ratingTextView: TextView = itemView.findViewById(R.id.textViewRating)
        private val responseTimeTextView: TextView = itemView.findViewById(R.id.textViewResponseTime)
        private val rankChangeTextView: TextView = itemView.findViewById(R.id.textViewRankChange)
        private val myselfIndicator: View = itemView.findViewById(R.id.viewMyselfIndicator)

        fun bind(ranking: MerchantRanking) {
            nameTextView.text = ranking.name
            areaTextView.text = ranking.area
            scoreTextView.text = "${String.format("%.1f", ranking.score)}分"
            orderCountTextView.text = "${ranking.orderCount}单"
            revenueTextView.text = "¥${String.format("%.0f", ranking.revenue)}"
            ratingTextView.text = "${String.format("%.1f", ranking.rating)}分"
            responseTimeTextView.text = "${ranking.responseTime}分钟"
            
            // 设置排名显示
            when (ranking.rank) {
                1 -> {
                    rankTextView.visibility = View.GONE
                    rankBadgeImageView.visibility = View.VISIBLE
                    rankBadgeImageView.setImageResource(R.drawable.ic_rank_1)
                }
                2 -> {
                    rankTextView.visibility = View.GONE
                    rankBadgeImageView.visibility = View.VISIBLE
                    rankBadgeImageView.setImageResource(R.drawable.ic_rank_2)
                }
                3 -> {
                    rankTextView.visibility = View.GONE
                    rankBadgeImageView.visibility = View.VISIBLE
                    rankBadgeImageView.setImageResource(R.drawable.ic_rank_3)
                }
                else -> {
                    rankTextView.visibility = View.VISIBLE
                    rankBadgeImageView.visibility = View.GONE
                    rankTextView.text = ranking.rank.toString()
                }
            }
            
            // 设置头像
            ImageUtils.loadAvatar(
                avatarImageView,
                ranking.avatar,
                R.drawable.ic_merchant_placeholder
            )
            
            // 设置排名变化
            when {
                ranking.rankChange > 0 -> {
                    rankChangeTextView.text = "↑${ranking.rankChange}"
                    rankChangeTextView.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.green_500)
                    )
                    rankChangeTextView.visibility = View.VISIBLE
                }
                ranking.rankChange < 0 -> {
                    rankChangeTextView.text = "↓${Math.abs(ranking.rankChange)}"
                    rankChangeTextView.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.red_500)
                    )
                    rankChangeTextView.visibility = View.VISIBLE
                }
                else -> {
                    rankChangeTextView.visibility = View.GONE
                }
            }
            
            // 设置自己的标识
            myselfIndicator.visibility = if (ranking.isMyself) View.VISIBLE else View.GONE
            
            // 设置背景色（自己的排名高亮显示）
            if (ranking.isMyself) {
                itemView.setBackgroundColor(
                    ContextCompat.getColor(itemView.context, R.color.blue_50)
                )
            } else {
                itemView.setBackgroundColor(
                    ContextCompat.getColor(itemView.context, android.R.color.transparent)
                )
            }
            
            itemView.setOnClickListener {
                onMerchantClick(ranking)
            }
        }
    }

    private class RankingDiffCallback : DiffUtil.ItemCallback<MerchantRanking>() {
        override fun areItemsTheSame(oldItem: MerchantRanking, newItem: MerchantRanking): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: MerchantRanking, newItem: MerchantRanking): Boolean {
            return oldItem == newItem
        }
    }
}
