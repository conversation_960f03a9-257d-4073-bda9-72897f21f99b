package com.laundry.merchant.ui.membership

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.laundry.merchant.R
import com.laundry.merchant.data.model.MembershipLevel
import com.laundry.merchant.databinding.ActivityMembershipBinding
import com.laundry.merchant.ui.membership.adapter.MembershipBenefitAdapter
import com.laundry.merchant.ui.membership.adapter.MembershipRewardAdapter
import com.laundry.merchant.ui.membership.adapter.PointsHistoryAdapter
import com.laundry.merchant.utils.formatCurrency
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MembershipActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMembershipBinding
    private val viewModel: MembershipViewModel by viewModels()
    
    private lateinit var benefitAdapter: MembershipBenefitAdapter
    private lateinit var rewardAdapter: MembershipRewardAdapter
    private lateinit var pointsHistoryAdapter: PointsHistoryAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMembershipBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerViews()
        observeViewModel()
        
        // 加载会员信息
        viewModel.loadMembershipInfo()
        viewModel.loadPointsHistory()
        viewModel.loadAvailableRewards()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "会员中心"

        // 设置签到按钮
        binding.buttonCheckIn.setOnClickListener {
            viewModel.checkIn()
        }

        // 设置升级按钮
        binding.buttonUpgrade.setOnClickListener {
            viewModel.upgradeMembership()
        }

        // 设置会员卡点击
        binding.layoutMembershipCard.setOnClickListener {
            showMembershipCard()
        }

        // 设置积分历史点击
        binding.layoutPointsHistory.setOnClickListener {
            val intent = Intent(this, PointsHistoryActivity::class.java)
            startActivity(intent)
        }

        // 设置会员活动点击
        binding.layoutMembershipActivities.setOnClickListener {
            val intent = Intent(this, MembershipActivitiesActivity::class.java)
            startActivity(intent)
        }
    }

    private fun setupRecyclerViews() {
        // 会员权益
        benefitAdapter = MembershipBenefitAdapter()
        binding.recyclerViewBenefits.apply {
            layoutManager = GridLayoutManager(this@MembershipActivity, 2)
            adapter = benefitAdapter
        }

        // 积分奖励
        rewardAdapter = MembershipRewardAdapter { reward ->
            viewModel.redeemReward(reward.id)
        }
        binding.recyclerViewRewards.apply {
            layoutManager = LinearLayoutManager(this@MembershipActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = rewardAdapter
        }

        // 积分历史（最近几条）
        pointsHistoryAdapter = PointsHistoryAdapter()
        binding.recyclerViewRecentPoints.apply {
            layoutManager = LinearLayoutManager(this@MembershipActivity)
            adapter = pointsHistoryAdapter
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: MembershipUiState) {
        // 更新加载状态
        binding.progressBar.visibility = if (state.isLoading) View.VISIBLE else View.GONE

        state.membershipInfo?.let { info ->
            // 更新会员等级信息
            binding.textViewMemberLevel.text = info.level.levelName
            binding.textViewMemberLevel.setTextColor(
                android.graphics.Color.parseColor(info.level.color)
            )
            
            // 设置等级图标
            binding.imageViewLevelIcon.setImageResource(
                when (info.level) {
                    MembershipLevel.BRONZE -> R.drawable.ic_bronze
                    MembershipLevel.SILVER -> R.drawable.ic_silver
                    MembershipLevel.GOLD -> R.drawable.ic_gold
                    MembershipLevel.PLATINUM -> R.drawable.ic_platinum
                    MembershipLevel.DIAMOND -> R.drawable.ic_diamond
                }
            )

            // 更新积分信息
            binding.textViewCurrentPoints.text = "${info.currentPoints}"
            binding.textViewTotalSpent.text = info.totalSpent.formatCurrency()
            binding.textViewTotalOrders.text = "${info.totalOrders}单"

            // 更新升级进度
            if (info.level.getNextLevel() != null) {
                binding.layoutUpgradeProgress.visibility = View.VISIBLE
                binding.buttonUpgrade.visibility = View.VISIBLE
                
                val nextLevel = info.level.getNextLevel()!!
                binding.textViewNextLevel.text = "升级到${nextLevel.levelName}"
                binding.textViewPointsToNext.text = "还需${info.getPointsToNextLevel()}积分"
                binding.textViewSpentToNext.text = "还需消费${info.getSpentToNextLevel().formatCurrency()}"
                
                binding.progressBarUpgrade.progress = (info.progressToNext * 100).toInt()
                
                binding.buttonUpgrade.isEnabled = info.canUpgrade()
                binding.buttonUpgrade.text = if (info.canUpgrade()) "立即升级" else "继续努力"
            } else {
                binding.layoutUpgradeProgress.visibility = View.GONE
                binding.buttonUpgrade.visibility = View.GONE
            }

            // 更新会员权益
            benefitAdapter.updateData(info.benefits)

            // 更新会员卡信息
            binding.textViewCardNumber.text = "卡号: ${info.membershipCard.cardNumber}"
        }

        // 更新积分奖励
        rewardAdapter.updateData(state.availableRewards)

        // 更新积分历史（最近5条）
        pointsHistoryAdapter.updateData(state.pointsHistory.take(5))

        // 更新签到状态
        binding.buttonCheckIn.isEnabled = !state.hasCheckedInToday && !state.isLoading
        binding.buttonCheckIn.text = if (state.hasCheckedInToday) "今日已签到" else "每日签到"

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: MembershipEvent) {
        when (event) {
            is MembershipEvent.ShowError -> {
                showError(event.message)
            }
            is MembershipEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is MembershipEvent.CheckInSuccess -> {
                showToast("签到成功！获得${event.points}积分")
            }
            is MembershipEvent.UpgradeSuccess -> {
                showToast("恭喜升级到${event.newLevel.levelName}！")
            }
            is MembershipEvent.RewardRedeemed -> {
                showToast("兑换成功！")
            }
        }
    }

    private fun showMembershipCard() {
        val membershipInfo = viewModel.uiState.value.membershipInfo
        if (membershipInfo != null) {
            val intent = Intent(this, MembershipCardActivity::class.java)
            intent.putExtra("card_number", membershipInfo.membershipCard.cardNumber)
            intent.putExtra("qr_code", membershipInfo.membershipCard.qrCode)
            intent.putExtra("level", membershipInfo.level.name)
            startActivity(intent)
        }
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
