<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗护帮 - 功能演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            transition: opacity 0.3s;
        }
        
        .nav-links a:hover {
            opacity: 0.8;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 100px 20px 20px;
        }
        
        .section {
            margin-bottom: 60px;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 2rem;
            margin-bottom: 30px;
            text-align: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 30px;
        }
        
        .demo-phone {
            width: 280px;
            height: 560px;
            background: #000;
            border-radius: 30px;
            padding: 20px;
            margin: 0 auto;
            position: relative;
        }
        
        .demo-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            position: relative;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            text-align: center;
        }
        
        .demo-content {
            padding: 20px;
            height: calc(100% - 60px);
            overflow-y: auto;
        }
        
        .search-bar {
            background: #f0f0f0;
            border-radius: 25px;
            padding: 12px 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .search-result {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .promoted-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
        }
        
        .ranking-badge {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.8rem;
            margin-right: 10px;
        }
        
        .merchant-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .merchant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .merchant-name {
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .merchant-ranking {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        .promotion-controls {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .control-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .control-input {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px 10px;
            width: 80px;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .admin-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .admin-header {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .admin-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .metric-item {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .metric-value {
            font-weight: bold;
            font-size: 1.2rem;
            color: #667eea;
        }
        
        .metric-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">🧺 洗护帮</div>
            <div class="nav-links">
                <a href="#user-features">用户端</a>
                <a href="#merchant-features">商家端</a>
                <a href="#admin-features">管理端</a>
                <a href="#promotion-system">投流系统</a>
            </div>
        </div>
    </nav>
    
    <div class="container">
        <!-- 用户端功能 -->
        <section id="user-features" class="section">
            <h2 class="section-title">📱 用户端功能演示</h2>
            
            <div class="demo-container">
                <div>
                    <h3>智能搜索排序</h3>
                    <p>集成投流竞价的智能搜索系统，为用户提供最相关的服务推荐。</p>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon">🔍</div>
                            <div class="feature-title">智能搜索</div>
                            <div class="feature-description">多因子排序算法，结合相关性、质量分、投流权重和用户偏好</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">🎯</div>
                            <div class="feature-title">个性化推荐</div>
                            <div class="feature-description">基于用户历史行为、地理位置和时间因素的个性化推荐</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">💰</div>
                            <div class="feature-title">投流展示</div>
                            <div class="feature-description">推广服务优先展示，带有明确的推广标识，保证公平竞争</div>
                        </div>
                    </div>
                </div>
                
                <div class="demo-phone">
                    <div class="demo-screen">
                        <div class="demo-header">
                            <h4>洗护帮</h4>
                        </div>
                        <div class="demo-content">
                            <div class="search-bar">
                                <span>🔍</span>
                                <span>衣物洗护</span>
                            </div>
                            
                            <div class="search-result">
                                <div class="promoted-badge">推广</div>
                                <div style="display: flex; align-items: center;">
                                    <span class="ranking-badge">1</span>
                                    <div>
                                        <div style="font-weight: bold;">优质洗衣店</div>
                                        <div style="font-size: 0.8rem; color: #666;">⭐ 4.9 · 距离500m · ¥15起</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="search-result">
                                <div class="promoted-badge">推广</div>
                                <div style="display: flex; align-items: center;">
                                    <span class="ranking-badge">2</span>
                                    <div>
                                        <div style="font-weight: bold;">专业干洗店</div>
                                        <div style="font-size: 0.8rem; color: #666;">⭐ 4.8 · 距离800m · ¥20起</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="search-result">
                                <div style="display: flex; align-items: center;">
                                    <span class="ranking-badge">3</span>
                                    <div>
                                        <div style="font-weight: bold;">快速洗护</div>
                                        <div style="font-size: 0.8rem; color: #666;">⭐ 4.7 · 距离1.2km · ¥12起</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 商家端功能 -->
        <section id="merchant-features" class="section">
            <h2 class="section-title">🏪 商家端投流系统</h2>
            
            <div class="demo-container">
                <div>
                    <h3>投流竞价管理</h3>
                    <p>完整的投流竞价系统，帮助商家提升排名，获得更多曝光和订单。</p>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon">💳</div>
                            <div class="feature-title">充值支付</div>
                            <div class="feature-description">支持支付宝、微信支付、银行卡等多种支付方式</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">🎯</div>
                            <div class="feature-title">关键词竞价</div>
                            <div class="feature-description">精准关键词竞价，智能出价策略，实时调整</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">📊</div>
                            <div class="feature-title">效果分析</div>
                            <div class="feature-description">详细的投流数据分析，ROI监控，转化跟踪</div>
                        </div>
                    </div>
                </div>
                
                <div class="demo-phone">
                    <div class="demo-screen">
                        <div class="demo-header">
                            <h4>商家管理中心</h4>
                        </div>
                        <div class="demo-content">
                            <div class="merchant-card">
                                <div class="merchant-header">
                                    <div class="merchant-name">我的洗衣店</div>
                                    <div class="merchant-ranking">排名 #3</div>
                                </div>
                                
                                <div class="stats-row">
                                    <div class="stat-item">
                                        <div class="stat-value">¥1,280</div>
                                        <div class="stat-label">账户余额</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value">156</div>
                                        <div class="stat-label">今日点击</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value">3.2</div>
                                        <div class="stat-label">ROI</div>
                                    </div>
                                </div>
                                
                                <div class="promotion-controls">
                                    <div class="control-row">
                                        <span>关键词出价:</span>
                                        <input type="text" class="control-input" value="¥2.50">
                                    </div>
                                    <div class="control-row">
                                        <span>日预算:</span>
                                        <input type="text" class="control-input" value="¥200">
                                    </div>
                                    <button class="btn">更新设置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 管理端功能 -->
        <section id="admin-features" class="section">
            <h2 class="section-title">⚙️ 管理端控制系统</h2>
            
            <div class="demo-container">
                <div>
                    <h3>投流系统管理</h3>
                    <p>全面的投流系统管理和控制，包括算法配置、违规监控和财务管理。</p>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon">🎛️</div>
                            <div class="feature-title">算法配置</div>
                            <div class="feature-description">排名算法权重配置，投流规则设置，实时调整</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">🔍</div>
                            <div class="feature-title">违规监控</div>
                            <div class="feature-description">自动检测恶意竞价、虚假点击等违规行为</div>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">💰</div>
                            <div class="feature-title">财务管理</div>
                            <div class="feature-description">平台收入统计，分成结算，财务报表分析</div>
                        </div>
                    </div>
                </div>
                
                <div class="admin-panel">
                    <div class="admin-header">
                        <h4>平台管理中心</h4>
                    </div>
                    
                    <div class="admin-section">
                        <div class="admin-title">📈 投流概览</div>
                        <div class="metric-grid">
                            <div class="metric-item">
                                <div class="metric-value">¥125,680</div>
                                <div class="metric-label">总投入</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">¥25,136</div>
                                <div class="metric-label">平台收入</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">1,245</div>
                                <div class="metric-label">活跃商家</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">¥1.85</div>
                                <div class="metric-label">平均CPC</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-section">
                        <div class="admin-title">⚙️ 算法配置</div>
                        <div style="margin-bottom: 10px;">
                            <span>投流权重: </span>
                            <input type="range" min="0" max="100" value="30" style="width: 100px;">
                            <span> 30%</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <span>质量权重: </span>
                            <input type="range" min="0" max="100" value="40" style="width: 100px;">
                            <span> 40%</span>
                        </div>
                        <div>
                            <span>相关性权重: </span>
                            <input type="range" min="0" max="100" value="30" style="width: 100px;">
                            <span> 30%</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 投流系统架构 -->
        <section id="promotion-system" class="section">
            <h2 class="section-title">🚀 投流系统架构</h2>
            
            <div style="text-align: center; margin-bottom: 30px;">
                <div style="background: #f8f9fa; border-radius: 15px; padding: 30px; font-family: monospace;">
                    <div style="margin-bottom: 20px; font-size: 1.2rem; font-weight: bold;">投流系统架构图</div>
                    <div style="border: 2px solid #667eea; border-radius: 10px; padding: 20px; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-around; margin-bottom: 15px;">
                            <div style="background: #4CAF50; color: white; padding: 10px; border-radius: 5px;">商家端投流</div>
                            <div style="background: #2196F3; color: white; padding: 10px; border-radius: 5px;">用户端搜索</div>
                            <div style="background: #FF9800; color: white; padding: 10px; border-radius: 5px;">管理端控制</div>
                            <div style="background: #9C27B0; color: white; padding: 10px; border-radius: 5px;">数据分析平台</div>
                        </div>
                    </div>
                    <div style="border: 2px solid #764ba2; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">投流服务层 (竞价引擎)</div>
                    </div>
                    <div style="border: 2px solid #667eea; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-around;">
                            <span>竞价算法</span>
                            <span>排名算法</span>
                            <span>推荐算法</span>
                            <span>反作弊算法</span>
                            <span>风控算法</span>
                        </div>
                    </div>
                    <div style="border: 2px solid #764ba2; border-radius: 10px; padding: 15px;">
                        <div style="display: flex; justify-content: space-around;">
                            <span>MySQL</span>
                            <span>Redis</span>
                            <span>ElasticSearch</span>
                            <span>ClickHouse</span>
                            <span>Kafka</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">实时竞价</div>
                    <div class="feature-description">毫秒级响应，高并发处理，实时排名更新</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <div class="feature-title">智能算法</div>
                    <div class="feature-description">机器学习驱动的排名和推荐算法</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">大数据处理</div>
                    <div class="feature-description">实时计算+离线分析+预测建模</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">微服务架构</div>
                    <div class="feature-description">服务拆分+独立部署+弹性扩展</div>
                </div>
            </div>
        </section>
    </div>
    
    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 动态更新数据
        setInterval(() => {
            // 更新点击数
            const clickElement = document.querySelector('.stat-value');
            if (clickElement && clickElement.textContent === '156') {
                const newValue = 156 + Math.floor(Math.random() * 5);
                clickElement.textContent = newValue.toString();
            }
            
            // 更新排名
            const rankingElements = document.querySelectorAll('.merchant-ranking');
            rankingElements.forEach(el => {
                if (Math.random() < 0.1) { // 10% 概率更新
                    const currentRank = parseInt(el.textContent.match(/\d+/)[0]);
                    const change = Math.random() < 0.5 ? -1 : 1;
                    const newRank = Math.max(1, Math.min(10, currentRank + change));
                    el.textContent = `排名 #${newRank}`;
                }
            });
        }, 3000);
    </script>
</body>
</html>
