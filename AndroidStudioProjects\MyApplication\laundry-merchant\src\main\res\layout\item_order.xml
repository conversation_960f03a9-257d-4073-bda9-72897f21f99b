<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/text_order_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="订单号: ORD001"
                android:textColor="@color/primary_text"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/text_order_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="处理中"
                android:textColor="@color/status_in_progress"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/text_order_services"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="普通洗衣"
            android:textColor="@color/secondary_text"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/text_order_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="12-06 14:30"
                android:textColor="@color/secondary_text"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/text_order_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¥15"
                android:textColor="@color/price_color"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
