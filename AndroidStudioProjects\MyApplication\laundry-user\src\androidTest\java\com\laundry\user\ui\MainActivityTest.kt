package com.laundry.user.ui

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.contrib.RecyclerViewActions
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import androidx.recyclerview.widget.RecyclerView
import com.laundry.user.MainActivity
import com.laundry.user.R
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityTest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)
    
    @Test
    fun testBottomNavigationWorks() {
        // 测试底部导航栏功能
        
        // 点击订单标签
        onView(withId(R.id.navigation_orders))
            .perform(click())
        
        // 验证订单页面显示
        onView(withId(R.id.text_orders_title))
            .check(matches(isDisplayed()))
        
        // 点击个人资料标签
        onView(withId(R.id.navigation_profile))
            .perform(click())
        
        // 验证个人资料页面显示
        onView(withId(R.id.text_user_name))
            .check(matches(isDisplayed()))
        
        // 返回首页
        onView(withId(R.id.navigation_home))
            .perform(click())
        
        // 验证首页显示
        onView(withId(R.id.text_welcome))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testServiceListDisplayed() {
        // 测试服务列表显示
        
        // 等待服务列表加载
        Thread.sleep(2000)
        
        // 验证RecyclerView显示
        onView(withId(R.id.recycler_view_services))
            .check(matches(isDisplayed()))
        
        // 验证至少有一个服务项
        onView(withId(R.id.recycler_view_services))
            .check(matches(hasMinimumChildCount(1)))
    }
    
    @Test
    fun testServiceItemClick() {
        // 测试服务项点击
        
        // 等待服务列表加载
        Thread.sleep(2000)
        
        // 点击第一个服务项
        onView(withId(R.id.recycler_view_services))
            .perform(
                RecyclerViewActions.actionOnItemAtPosition<RecyclerView.ViewHolder>(
                    0,
                    click()
                )
            )
        
        // 这里可以验证点击后的行为，比如跳转到详情页面
        // 由于我们还没有实现详情页面，这里暂时省略
    }
    
    @Test
    fun testOrdersPageEmpty() {
        // 测试订单页面空状态
        
        // 切换到订单页面
        onView(withId(R.id.navigation_orders))
            .perform(click())
        
        // 如果没有订单，应该显示空状态文本
        // 这个测试依赖于当前用户没有订单的状态
        onView(withId(R.id.text_empty_orders))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testProfileMenuItems() {
        // 测试个人资料页面菜单项
        
        // 切换到个人资料页面
        onView(withId(R.id.navigation_profile))
            .perform(click())
        
        // 验证各个菜单项显示
        onView(withText("地址管理"))
            .check(matches(isDisplayed()))
        
        onView(withText("订单历史"))
            .check(matches(isDisplayed()))
        
        onView(withText("设置"))
            .check(matches(isDisplayed()))
        
        onView(withText("帮助与支持"))
            .check(matches(isDisplayed()))
        
        onView(withText("关于"))
            .check(matches(isDisplayed()))
        
        // 测试点击地址管理
        onView(withText("地址管理"))
            .perform(click())
        
        // 这里可以验证点击后的行为
    }
}
