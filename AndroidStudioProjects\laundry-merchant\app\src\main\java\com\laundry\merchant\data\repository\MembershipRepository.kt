package com.laundry.merchant.data.repository

import com.laundry.merchant.data.model.*
import com.laundry.merchant.network.ApiService
import com.laundry.merchant.network.NetworkResult
import kotlinx.coroutines.delay
import java.util.Calendar
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

interface MembershipRepository {
    suspend fun getMembershipInfo(userId: String): NetworkResult<MembershipInfo>
    suspend fun getPointsHistory(userId: String): NetworkResult<List<PointsTransaction>>
    suspend fun redeemReward(userId: String, rewardId: String): NetworkResult<Boolean>
    suspend fun getAvailableRewards(userId: String): NetworkResult<List<MembershipReward>>
    suspend fun getMembershipActivities(): NetworkResult<List<MembershipActivity>>
    suspend fun participateInActivity(userId: String, activityId: String): NetworkResult<Boolean>
    suspend fun checkIn(userId: String): NetworkResult<PointsTransaction>
    suspend fun upgradeMembership(userId: String): NetworkResult<MembershipInfo>
    suspend fun getMembershipStats(): NetworkResult<MembershipStats>
    suspend fun earnPoints(userId: String, points: Int, type: PointsTransactionType, description: String): NetworkResult<PointsTransaction>
}

@Singleton
class MembershipRepositoryImpl @Inject constructor(
    private val apiService: ApiService
) : MembershipRepository {

    override suspend fun getMembershipInfo(userId: String): NetworkResult<MembershipInfo> {
        return try {
            delay(500)
            val membershipInfo = generateMockMembershipInfo(userId)
            NetworkResult.Success(membershipInfo)
        } catch (e: Exception) {
            NetworkResult.Error("获取会员信息失败: ${e.message}")
        }
    }

    override suspend fun getPointsHistory(userId: String): NetworkResult<List<PointsTransaction>> {
        return try {
            delay(400)
            val history = generateMockPointsHistory(userId)
            NetworkResult.Success(history)
        } catch (e: Exception) {
            NetworkResult.Error("获取积分历史失败: ${e.message}")
        }
    }

    override suspend fun redeemReward(userId: String, rewardId: String): NetworkResult<Boolean> {
        return try {
            delay(600)
            // 模拟兑换奖励
            val reward = getMockRewards().find { it.id == rewardId }
            if (reward != null) {
                // 检查积分是否足够
                val membershipInfo = generateMockMembershipInfo(userId)
                if (membershipInfo.currentPoints >= reward.pointsCost) {
                    NetworkResult.Success(true)
                } else {
                    NetworkResult.Error("积分不足")
                }
            } else {
                NetworkResult.Error("奖励不存在")
            }
        } catch (e: Exception) {
            NetworkResult.Error("兑换失败: ${e.message}")
        }
    }

    override suspend fun getAvailableRewards(userId: String): NetworkResult<List<MembershipReward>> {
        return try {
            delay(400)
            val membershipInfo = generateMockMembershipInfo(userId)
            val rewards = getMockRewards().filter { reward ->
                reward.isAvailable && 
                (reward.requiredLevel == null || 
                 reward.requiredLevel.ordinal <= membershipInfo.level.ordinal)
            }
            NetworkResult.Success(rewards)
        } catch (e: Exception) {
            NetworkResult.Error("获取可兑换奖励失败: ${e.message}")
        }
    }

    override suspend fun getMembershipActivities(): NetworkResult<List<MembershipActivity>> {
        return try {
            delay(400)
            val activities = getMockActivities()
            NetworkResult.Success(activities)
        } catch (e: Exception) {
            NetworkResult.Error("获取会员活动失败: ${e.message}")
        }
    }

    override suspend fun participateInActivity(userId: String, activityId: String): NetworkResult<Boolean> {
        return try {
            delay(500)
            // 模拟参与活动
            NetworkResult.Success(true)
        } catch (e: Exception) {
            NetworkResult.Error("参与活动失败: ${e.message}")
        }
    }

    override suspend fun checkIn(userId: String): NetworkResult<PointsTransaction> {
        return try {
            delay(300)
            // 模拟签到获得积分
            val points = (10..50).random() // 随机获得10-50积分
            val transaction = PointsTransaction(
                id = "checkin_${System.currentTimeMillis()}",
                userId = userId,
                type = PointsTransactionType.EARNED_CHECKIN,
                points = points,
                description = "每日签到奖励",
                createdAt = Date(),
                expiryDate = Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000) // 1年后过期
            )
            NetworkResult.Success(transaction)
        } catch (e: Exception) {
            NetworkResult.Error("签到失败: ${e.message}")
        }
    }

    override suspend fun upgradeMembership(userId: String): NetworkResult<MembershipInfo> {
        return try {
            delay(600)
            val currentInfo = generateMockMembershipInfo(userId)
            if (currentInfo.canUpgrade()) {
                val nextLevel = currentInfo.level.getNextLevel()
                if (nextLevel != null) {
                    val upgradedInfo = currentInfo.copy(
                        level = nextLevel,
                        lastUpgradeDate = Date()
                    )
                    NetworkResult.Success(upgradedInfo)
                } else {
                    NetworkResult.Error("已是最高等级")
                }
            } else {
                NetworkResult.Error("升级条件不满足")
            }
        } catch (e: Exception) {
            NetworkResult.Error("升级失败: ${e.message}")
        }
    }

    override suspend fun getMembershipStats(): NetworkResult<MembershipStats> {
        return try {
            delay(500)
            val stats = generateMockMembershipStats()
            NetworkResult.Success(stats)
        } catch (e: Exception) {
            NetworkResult.Error("获取会员统计失败: ${e.message}")
        }
    }

    override suspend fun earnPoints(
        userId: String,
        points: Int,
        type: PointsTransactionType,
        description: String
    ): NetworkResult<PointsTransaction> {
        return try {
            delay(300)
            val transaction = PointsTransaction(
                id = "earn_${System.currentTimeMillis()}",
                userId = userId,
                type = type,
                points = points,
                description = description,
                createdAt = Date(),
                expiryDate = Date(System.currentTimeMillis() + 365L * 24 * 60 * 60 * 1000)
            )
            NetworkResult.Success(transaction)
        } catch (e: Exception) {
            NetworkResult.Error("积分获取失败: ${e.message}")
        }
    }

    // 模拟数据生成方法
    private fun generateMockMembershipInfo(userId: String): MembershipInfo {
        val currentPoints = 3500
        val totalSpent = 1200.0
        val totalOrders = 25
        val level = MembershipLevel.SILVER
        val nextLevel = level.getNextLevel() ?: level
        
        return MembershipInfo(
            userId = userId,
            level = level,
            currentPoints = currentPoints,
            totalSpent = totalSpent,
            totalOrders = totalOrders,
            joinDate = Date(System.currentTimeMillis() - 180L * 24 * 60 * 60 * 1000), // 180天前
            lastUpgradeDate = Date(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000), // 30天前
            nextLevelPoints = nextLevel.pointsRequired,
            nextLevelSpent = nextLevel.spentRequired,
            progressToNext = if (nextLevel != level) {
                val pointsProgress = currentPoints.toFloat() / nextLevel.pointsRequired
                val spentProgress = totalSpent.toFloat() / nextLevel.spentRequired
                minOf(pointsProgress, spentProgress)
            } else 1.0f,
            benefits = generateMembershipBenefits(level),
            availableRewards = getMockRewards().take(3),
            membershipCard = MembershipCard(
                cardNumber = "LC${userId.takeLast(8)}",
                qrCode = "QR_$userId",
                barcode = "BAR_$userId",
                issueDate = Date(System.currentTimeMillis() - 180L * 24 * 60 * 60 * 1000),
                validUntil = null,
                cardDesign = level.name.lowercase()
            )
        )
    }

    private fun generateMembershipBenefits(level: MembershipLevel): List<MembershipBenefit> {
        val benefits = mutableListOf<MembershipBenefit>()
        
        // 基础权益
        benefits.add(
            MembershipBenefit(
                id = "discount_${level.name}",
                name = "会员折扣",
                description = "享受${(level.discountRate * 100).toInt()}%折扣",
                type = BenefitType.DISCOUNT,
                value = "${(level.discountRate * 100).toInt()}%",
                icon = "ic_discount"
            )
        )
        
        benefits.add(
            MembershipBenefit(
                id = "points_${level.name}",
                name = "积分倍数",
                description = "消费积分${level.pointsMultiplier}倍",
                type = BenefitType.POINTS_MULTIPLIER,
                value = "${level.pointsMultiplier}x",
                icon = "ic_points"
            )
        )
        
        // 高级权益
        if (level.ordinal >= MembershipLevel.GOLD.ordinal) {
            benefits.add(
                MembershipBenefit(
                    id = "priority_service",
                    name = "优先服务",
                    description = "享受优先服务权",
                    type = BenefitType.PRIORITY_SERVICE,
                    value = "优先",
                    icon = "ic_priority"
                )
            )
        }
        
        if (level.ordinal >= MembershipLevel.PLATINUM.ordinal) {
            benefits.add(
                MembershipBenefit(
                    id = "free_shipping",
                    name = "免配送费",
                    description = "全年免配送费",
                    type = BenefitType.FREE_SHIPPING,
                    value = "免费",
                    icon = "ic_shipping"
                )
            )
        }
        
        return benefits
    }

    private fun generateMockPointsHistory(userId: String): List<PointsTransaction> {
        val history = mutableListOf<PointsTransaction>()
        val calendar = Calendar.getInstance()
        
        // 生成最近30天的积分记录
        for (i in 0..29) {
            calendar.add(Calendar.DAY_OF_MONTH, -1)
            val date = calendar.time
            
            // 随机生成一些交易记录
            if ((0..2).random() == 0) { // 33%概率有记录
                val types = listOf(
                    PointsTransactionType.EARNED_ORDER,
                    PointsTransactionType.EARNED_CHECKIN,
                    PointsTransactionType.EARNED_REVIEW
                )
                val type = types.random()
                val points = when (type) {
                    PointsTransactionType.EARNED_ORDER -> (50..200).random()
                    PointsTransactionType.EARNED_CHECKIN -> (10..50).random()
                    PointsTransactionType.EARNED_REVIEW -> (20..100).random()
                    else -> 0
                }
                
                history.add(
                    PointsTransaction(
                        id = "trans_${date.time}",
                        userId = userId,
                        type = type,
                        points = points,
                        description = when (type) {
                            PointsTransactionType.EARNED_ORDER -> "订单消费获得"
                            PointsTransactionType.EARNED_CHECKIN -> "每日签到奖励"
                            PointsTransactionType.EARNED_REVIEW -> "评价订单获得"
                            else -> "其他"
                        },
                        createdAt = date,
                        expiryDate = Date(date.time + 365L * 24 * 60 * 60 * 1000)
                    )
                )
            }
        }
        
        return history.sortedByDescending { it.createdAt }
    }

    private fun getMockRewards(): List<MembershipReward> {
        return listOf(
            MembershipReward(
                id = "reward_1",
                name = "20元代金券",
                description = "满100可用",
                pointsCost = 2000,
                type = RewardType.CASH_VOUCHER,
                value = 20.0,
                imageUrl = "reward_voucher_20",
                requiredLevel = MembershipLevel.BRONZE
            ),
            MembershipReward(
                id = "reward_2",
                name = "免费洗衣服务",
                description = "价值50元的免费洗衣服务",
                pointsCost = 5000,
                type = RewardType.FREE_SERVICE,
                value = 50.0,
                imageUrl = "reward_free_service",
                requiredLevel = MembershipLevel.SILVER
            ),
            MembershipReward(
                id = "reward_3",
                name = "精美洗衣袋",
                description = "高品质洗衣袋一套",
                pointsCost = 3000,
                type = RewardType.PHYSICAL_GIFT,
                value = 0.0,
                imageUrl = "reward_laundry_bag",
                stock = 50,
                requiredLevel = MembershipLevel.BRONZE
            ),
            MembershipReward(
                id = "reward_4",
                name = "VIP升级服务",
                description = "升级为VIP服务体验",
                pointsCost = 8000,
                type = RewardType.UPGRADE_SERVICE,
                value = 0.0,
                imageUrl = "reward_vip_upgrade",
                requiredLevel = MembershipLevel.GOLD
            )
        )
    }

    private fun getMockActivities(): List<MembershipActivity> {
        val now = Date()
        val futureDate = Date(now.time + 30L * 24 * 60 * 60 * 1000)
        
        return listOf(
            MembershipActivity(
                id = "activity_1",
                title = "积分双倍周",
                description = "本周内所有消费积分双倍",
                type = ActivityType.POINTS_DOUBLE,
                startDate = now,
                endDate = Date(now.time + 7L * 24 * 60 * 60 * 1000),
                rewards = listOf(
                    ActivityReward("双倍积分", RewardType.POINTS_BONUS, "2x积分")
                ),
                participants = 1250,
                imageUrl = "activity_double_points"
            ),
            MembershipActivity(
                id = "activity_2",
                title = "黄金会员专享特卖",
                description = "黄金及以上会员专享特价商品",
                type = ActivityType.EXCLUSIVE_SALE,
                requiredLevel = MembershipLevel.GOLD,
                startDate = now,
                endDate = futureDate,
                rewards = listOf(
                    ActivityReward("专享折扣", RewardType.COUPON, "额外8折")
                ),
                participants = 320,
                imageUrl = "activity_exclusive_sale"
            ),
            MembershipActivity(
                id = "activity_3",
                title = "幸运大转盘",
                description = "每日一次免费抽奖机会",
                type = ActivityType.LUCKY_DRAW,
                startDate = now,
                endDate = futureDate,
                rewards = listOf(
                    ActivityReward("代金券", RewardType.CASH_VOUCHER, "5-100元", 0.3f),
                    ActivityReward("积分奖励", RewardType.POINTS_BONUS, "100-1000积分", 0.5f),
                    ActivityReward("实物奖品", RewardType.PHYSICAL_GIFT, "精美礼品", 0.1f),
                    ActivityReward("谢谢参与", RewardType.POINTS_BONUS, "10积分", 0.1f)
                ),
                participants = 5680,
                maxParticipants = 10000,
                imageUrl = "activity_lucky_draw"
            )
        )
    }

    private fun generateMockMembershipStats(): MembershipStats {
        return MembershipStats(
            totalMembers = 15680,
            levelDistribution = mapOf(
                MembershipLevel.BRONZE to 8500,
                MembershipLevel.SILVER to 4200,
                MembershipLevel.GOLD to 2100,
                MembershipLevel.PLATINUM to 750,
                MembershipLevel.DIAMOND to 130
            ),
            averageSpent = 850.0,
            averagePoints = 2100,
            monthlyNewMembers = 320,
            monthlyUpgrades = 85,
            topSpenders = listOf(
                MemberSpendingSummary("user_1", "王**", MembershipLevel.DIAMOND, 15680.0, 156, 100.5),
                MemberSpendingSummary("user_2", "李**", MembershipLevel.PLATINUM, 12450.0, 124, 100.4),
                MemberSpendingSummary("user_3", "张**", MembershipLevel.PLATINUM, 11200.0, 98, 114.3)
            )
        )
    }
}
