package com.laundry.admin.ui.system

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.tabs.TabLayout
import com.laundry.admin.R
import com.laundry.admin.databinding.ActivitySystemConfigBinding
import com.laundry.admin.ui.system.adapter.SystemConfigAdapter
import com.laundry.admin.ui.system.adapter.OperationLogAdapter
import com.laundry.admin.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class SystemConfigActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySystemConfigBinding
    private val viewModel: SystemConfigViewModel by viewModels()
    
    private lateinit var configAdapter: SystemConfigAdapter
    private lateinit var logAdapter: OperationLogAdapter
    private var currentTab = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySystemConfigBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerViews()
        setupTabs()
        observeViewModel()
        
        // 处理从告警跳转的情况
        val alertId = intent.getStringExtra("alert_id")
        if (alertId != null) {
            viewModel.handleSystemAlert(alertId)
        } else {
            viewModel.loadSystemConfigs()
        }
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "系统配置"

        // 设置刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            refreshData()
        }

        // 设置快捷操作按钮
        binding.buttonSystemHealth.setOnClickListener {
            showSystemHealthDialog()
        }

        binding.buttonBackupRestore.setOnClickListener {
            showBackupRestoreDialog()
        }

        binding.buttonMaintenanceMode.setOnClickListener {
            showMaintenanceModeDialog()
        }

        binding.buttonClearCache.setOnClickListener {
            showClearCacheDialog()
        }
    }

    private fun setupRecyclerViews() {
        // 系统配置列表
        configAdapter = SystemConfigAdapter(
            onConfigEdit = { config ->
                showConfigEditDialog(config)
            },
            onConfigToggle = { config, enabled ->
                viewModel.toggleConfig(config.id, enabled)
            }
        )
        
        binding.recyclerViewConfigs.apply {
            layoutManager = LinearLayoutManager(this@SystemConfigActivity)
            adapter = configAdapter
        }

        // 操作日志列表
        logAdapter = OperationLogAdapter(
            onLogClick = { log ->
                showLogDetailDialog(log)
            }
        )
        
        binding.recyclerViewLogs.apply {
            layoutManager = LinearLayoutManager(this@SystemConfigActivity)
            adapter = logAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("系统配置"))
            addTab(newTab().setText("操作日志"))
            addTab(newTab().setText("系统监控"))
            addTab(newTab().setText("版本管理"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    currentTab = tab?.position ?: 0
                    updateTabContent(currentTab)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun updateTabContent(tabIndex: Int) {
        // 隐藏所有内容
        binding.layoutConfigs.visibility = View.GONE
        binding.layoutLogs.visibility = View.GONE
        binding.layoutMonitoring.visibility = View.GONE
        binding.layoutVersions.visibility = View.GONE

        when (tabIndex) {
            0 -> {
                binding.layoutConfigs.visibility = View.VISIBLE
                viewModel.loadSystemConfigs()
            }
            1 -> {
                binding.layoutLogs.visibility = View.VISIBLE
                viewModel.loadOperationLogs()
            }
            2 -> {
                binding.layoutMonitoring.visibility = View.VISIBLE
                viewModel.loadSystemMonitoring()
            }
            3 -> {
                binding.layoutVersions.visibility = View.VISIBLE
                viewModel.loadVersionManagement()
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_system_config, menu)
        
        // 设置搜索
        val searchItem = menu?.findItem(R.id.action_search)
        val searchView = searchItem?.actionView as? SearchView
        searchView?.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { 
                    when (currentTab) {
                        0 -> viewModel.searchConfigs(it)
                        1 -> viewModel.searchLogs(it)
                    }
                }
                return true
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                if (newText.isNullOrEmpty()) {
                    refreshData()
                }
                return true
            }
        })
        
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_add_config -> {
                showAddConfigDialog()
                true
            }
            R.id.action_export_logs -> {
                showExportLogsDialog()
                true
            }
            R.id.action_system_info -> {
                showSystemInfoDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: SystemConfigUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新系统配置
        configAdapter.updateData(state.systemConfigs)

        // 更新操作日志
        logAdapter.updateData(state.operationLogs)

        // 更新系统状态
        state.systemStatus?.let { status ->
            updateSystemStatus(status)
        }

        // 更新系统监控数据
        state.monitoringData?.let { monitoring ->
            updateMonitoringData(monitoring)
        }

        // 更新版本信息
        state.versionInfo?.let { version ->
            updateVersionInfo(version)
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun updateSystemStatus(status: com.laundry.admin.data.model.SystemStatus) {
        // 更新系统健康状态
        val healthColor = when (status.healthStatus) {
            "healthy" -> R.color.green_500
            "warning" -> R.color.orange_500
            "critical" -> R.color.red_500
            else -> R.color.gray_500
        }
        
        binding.viewSystemHealthIndicator.setBackgroundResource(healthColor)
        binding.textViewSystemHealth.text = when (status.healthStatus) {
            "healthy" -> "系统正常"
            "warning" -> "系统警告"
            "critical" -> "系统异常"
            else -> "状态未知"
        }

        // 更新服务器指标
        binding.textViewServerLoad.text = "${status.serverLoad}%"
        binding.progressBarServerLoad.progress = status.serverLoad
        
        binding.textViewMemoryUsage.text = "${status.memoryUsage}%"
        binding.progressBarMemoryUsage.progress = status.memoryUsage
        
        binding.textViewDiskUsage.text = "${status.diskUsage}%"
        binding.progressBarDiskUsage.progress = status.diskUsage
        
        binding.textViewActiveConnections.text = "${status.activeConnections}"
        binding.textViewResponseTime.text = "${String.format("%.2f", status.responseTime)}ms"
    }

    private fun updateMonitoringData(monitoring: SystemMonitoringData) {
        // 更新API监控
        binding.textViewApiCalls.text = "${monitoring.totalApiCalls}"
        binding.textViewApiErrors.text = "${monitoring.apiErrors}"
        binding.textViewApiSuccessRate.text = "${String.format("%.2f", monitoring.apiSuccessRate)}%"
        
        // 更新数据库监控
        binding.textViewDbConnections.text = "${monitoring.dbConnections}"
        binding.textViewDbQueries.text = "${monitoring.dbQueries}"
        binding.textViewDbResponseTime.text = "${String.format("%.2f", monitoring.dbResponseTime)}ms"
        
        // 更新缓存监控
        binding.textViewCacheHitRate.text = "${String.format("%.2f", monitoring.cacheHitRate)}%"
        binding.textViewCacheSize.text = "${monitoring.cacheSize}MB"
        
        // 更新错误监控
        binding.textViewErrorRate.text = "${String.format("%.2f", monitoring.errorRate)}%"
        binding.textViewCriticalErrors.text = "${monitoring.criticalErrors}"
    }

    private fun updateVersionInfo(version: VersionInfo) {
        binding.textViewCurrentVersion.text = version.currentVersion
        binding.textViewLatestVersion.text = version.latestVersion
        binding.textViewUpdateAvailable.text = if (version.updateAvailable) "有更新" else "已是最新"
        binding.textViewLastUpdate.text = version.lastUpdateTime
        
        binding.buttonCheckUpdate.isEnabled = !version.isChecking
        binding.buttonApplyUpdate.isEnabled = version.updateAvailable && !version.isUpdating
    }

    private fun handleEvent(event: SystemConfigEvent) {
        when (event) {
            is SystemConfigEvent.ShowError -> {
                showError(event.message)
            }
            is SystemConfigEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is SystemConfigEvent.ConfigUpdated -> {
                showToast("配置已更新")
                refreshData()
            }
            is SystemConfigEvent.SystemRestarted -> {
                showToast("系统已重启")
            }
            is SystemConfigEvent.BackupCompleted -> {
                showToast("备份已完成")
            }
            is SystemConfigEvent.RestoreCompleted -> {
                showToast("恢复已完成")
            }
        }
    }

    private fun showConfigEditDialog(config: com.laundry.admin.data.model.SystemConfig) {
        val dialog = ConfigEditDialog.newInstance(config) { updatedConfig ->
            viewModel.updateConfig(updatedConfig)
        }
        dialog.show(supportFragmentManager, "ConfigEditDialog")
    }

    private fun showAddConfigDialog() {
        val dialog = AddConfigDialog.newInstance { newConfig ->
            viewModel.addConfig(newConfig)
        }
        dialog.show(supportFragmentManager, "AddConfigDialog")
    }

    private fun showLogDetailDialog(log: com.laundry.admin.data.model.OperationLog) {
        val dialog = LogDetailDialog.newInstance(log)
        dialog.show(supportFragmentManager, "LogDetailDialog")
    }

    private fun showSystemHealthDialog() {
        val dialog = SystemHealthDialog.newInstance()
        dialog.show(supportFragmentManager, "SystemHealthDialog")
    }

    private fun showBackupRestoreDialog() {
        val operations = arrayOf("创建备份", "恢复备份", "查看备份历史", "自动备份设置")
        
        MaterialAlertDialogBuilder(this)
            .setTitle("备份与恢复")
            .setItems(operations) { _, which ->
                when (which) {
                    0 -> showCreateBackupDialog()
                    1 -> showRestoreBackupDialog()
                    2 -> showBackupHistoryDialog()
                    3 -> showAutoBackupSettingsDialog()
                }
            }
            .show()
    }

    private fun showMaintenanceModeDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("维护模式")
            .setMessage("启用维护模式将暂停所有用户服务，确定要继续吗？")
            .setPositiveButton("启用") { _, _ ->
                showMaintenanceReasonDialog()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showMaintenanceReasonDialog() {
        val dialog = MaintenanceReasonDialog.newInstance { reason, duration ->
            viewModel.enableMaintenanceMode(reason, duration)
        }
        dialog.show(supportFragmentManager, "MaintenanceReasonDialog")
    }

    private fun showClearCacheDialog() {
        val cacheTypes = arrayOf("应用缓存", "数据库缓存", "Redis缓存", "CDN缓存", "全部缓存")
        val selectedTypes = mutableListOf<String>()
        
        MaterialAlertDialogBuilder(this)
            .setTitle("清理缓存")
            .setMultiChoiceItems(cacheTypes, null) { _, which, isChecked ->
                if (isChecked) {
                    selectedTypes.add(cacheTypes[which])
                } else {
                    selectedTypes.remove(cacheTypes[which])
                }
            }
            .setPositiveButton("清理") { _, _ ->
                if (selectedTypes.isNotEmpty()) {
                    viewModel.clearCache(selectedTypes)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showCreateBackupDialog() {
        val dialog = CreateBackupDialog.newInstance { backupType, description ->
            viewModel.createBackup(backupType, description)
        }
        dialog.show(supportFragmentManager, "CreateBackupDialog")
    }

    private fun showRestoreBackupDialog() {
        val dialog = RestoreBackupDialog.newInstance { backupId ->
            viewModel.restoreBackup(backupId)
        }
        dialog.show(supportFragmentManager, "RestoreBackupDialog")
    }

    private fun showBackupHistoryDialog() {
        val dialog = BackupHistoryDialog.newInstance()
        dialog.show(supportFragmentManager, "BackupHistoryDialog")
    }

    private fun showAutoBackupSettingsDialog() {
        val dialog = AutoBackupSettingsDialog.newInstance()
        dialog.show(supportFragmentManager, "AutoBackupSettingsDialog")
    }

    private fun showExportLogsDialog() {
        val exportTypes = arrayOf("今日日志", "本周日志", "本月日志", "自定义时间", "错误日志")
        
        MaterialAlertDialogBuilder(this)
            .setTitle("导出日志")
            .setItems(exportTypes) { _, which ->
                when (which) {
                    0 -> viewModel.exportTodayLogs()
                    1 -> viewModel.exportWeekLogs()
                    2 -> viewModel.exportMonthLogs()
                    3 -> showCustomLogExportDialog()
                    4 -> viewModel.exportErrorLogs()
                }
            }
            .show()
    }

    private fun showCustomLogExportDialog() {
        val dialog = CustomLogExportDialog.newInstance { startDate, endDate, logLevel ->
            viewModel.exportCustomLogs(startDate, endDate, logLevel)
        }
        dialog.show(supportFragmentManager, "CustomLogExportDialog")
    }

    private fun showSystemInfoDialog() {
        val dialog = SystemInfoDialog.newInstance()
        dialog.show(supportFragmentManager, "SystemInfoDialog")
    }

    private fun refreshData() {
        when (currentTab) {
            0 -> viewModel.loadSystemConfigs()
            1 -> viewModel.loadOperationLogs()
            2 -> viewModel.loadSystemMonitoring()
            3 -> viewModel.loadVersionManagement()
        }
    }

    private fun showError(message: String) {
        showToast(message)
    }
}
