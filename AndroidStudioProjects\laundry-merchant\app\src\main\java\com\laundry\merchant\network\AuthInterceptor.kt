package com.laundry.merchant.network

import android.content.Context
import androidx.datastore.preferences.core.stringPreferencesKey
import com.laundry.merchant.data.local.PreferencesManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthInterceptor @Inject constructor() : Interceptor {

    companion object {
        private const val HEADER_AUTHORIZATION = "Authorization"
        private const val BEARER_PREFIX = "Bearer "
    }

    @Inject
    lateinit var preferencesManager: PreferencesManager

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // 获取访问令牌
        val accessToken = runBlocking {
            try {
                preferencesManager.getAccessToken().first()
            } catch (e: Exception) {
                null
            }
        }

        // 如果没有token或者是登录接口，直接发送原始请求
        if (accessToken.isNullOrBlank() || originalRequest.url.encodedPath.contains("/auth/login")) {
            return chain.proceed(originalRequest)
        }

        // 添加Authorization头
        val authenticatedRequest = originalRequest.newBuilder()
            .header(HEADER_AUTHORIZATION, "$BEARER_PREFIX$accessToken")
            .build()

        val response = chain.proceed(authenticatedRequest)

        // 如果返回401未授权，可能需要刷新token
        if (response.code == 401) {
            response.close()
            
            // 尝试刷新token
            val newToken = refreshToken()
            
            if (newToken != null) {
                // 使用新token重新发送请求
                val newRequest = originalRequest.newBuilder()
                    .header(HEADER_AUTHORIZATION, "$BEARER_PREFIX$newToken")
                    .build()
                return chain.proceed(newRequest)
            } else {
                // 刷新失败，清除本地token并跳转到登录页
                runBlocking {
                    preferencesManager.clearTokens()
                }
                // TODO: 发送广播或事件通知需要重新登录
            }
        }

        return response
    }

    private fun refreshToken(): String? {
        return try {
            val refreshToken = runBlocking {
                preferencesManager.getRefreshToken().first()
            }
            
            if (refreshToken.isNullOrBlank()) {
                return null
            }

            // TODO: 调用刷新token的API
            // 这里需要创建一个新的Retrofit实例来避免循环依赖
            // val newTokenResponse = refreshTokenApi.refreshToken(RefreshTokenRequest(refreshToken))
            // if (newTokenResponse.isSuccessful) {
            //     val newAccessToken = newTokenResponse.body()?.data?.accessToken
            //     runBlocking {
            //         preferencesManager.saveAccessToken(newAccessToken)
            //     }
            //     return newAccessToken
            // }
            
            null
        } catch (e: Exception) {
            null
        }
    }
}
