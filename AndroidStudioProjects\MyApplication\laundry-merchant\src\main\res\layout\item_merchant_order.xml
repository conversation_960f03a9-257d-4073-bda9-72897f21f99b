<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Order Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/text_order_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="订单号: ORD001"
                android:textColor="@color/primary_text"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/text_order_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="待接单"
                android:textColor="@color/status_pending"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Customer Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/text_customer_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="客户: 张三"
                android:textColor="@color/primary_text"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/text_customer_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="13800138000"
                android:textColor="@color/secondary_text"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Services -->
        <TextView
            android:id="@+id/text_order_services"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="普通洗衣"
            android:textColor="@color/secondary_text"
            android:textSize="14sp" />

        <!-- Address -->
        <TextView
            android:id="@+id/text_pickup_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="取件地址: 北京市朝阳区某某小区"
            android:textColor="@color/secondary_text"
            android:textSize="12sp" />

        <!-- Notes -->
        <TextView
            android:id="@+id/text_order_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="备注: 请小心处理"
            android:textColor="@color/secondary_text"
            android:textSize="12sp"
            android:visibility="gone" />

        <!-- Time and Amount -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/text_order_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="12-06 14:30"
                android:textColor="@color/secondary_text"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/text_order_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¥15"
                android:textColor="@color/price_color"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/button_accept"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_primary"
                android:text="@string/accept"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/button_reject"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_outline"
                android:text="@string/reject"
                android:textColor="@color/error_color"
                android:textSize="12sp"
                android:visibility="gone" />

            <Button
                android:id="@+id/button_update_status"
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:background="@drawable/button_primary"
                android:text="更新状态"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
