package com.laundry.merchant.model

import java.util.Date

data class Order(
    val id: String,
    val userId: String,
    val userName: String,
    val userPhone: String,
    val merchantId: String,
    val services: List<LaundryService>,
    val totalAmount: Double,
    val status: OrderStatus,
    val pickupAddress: Address,
    val deliveryAddress: Address,
    val pickupTime: Date?,
    val deliveryTime: Date?,
    val createdAt: Date,
    val notes: String = "",
    val estimatedCompletionTime: Date? = null
)

enum class OrderStatus {
    PENDING,        // 待接单
    ACCEPTED,       // 已接单
    PICKED_UP,      // 已取件
    IN_PROGRESS,    // 处理中
    READY,          // 已完成
    DELIVERING,     // 配送中
    COMPLETED,      // 已完成
    CANCELLED       // 已取消
}

data class LaundryService(
    val id: Int,
    val name: String,
    val description: String,
    val price: Double,
    val imageUrl: String,
    val category: String,
    val estimatedTime: String = "1-2天",
    val isAvailable: Boolean = true
)

data class Address(
    val id: String = "",
    val name: String,
    val phone: String,
    val address: String,
    val detailAddress: String,
    val isDefault: Boolean = false
)
