package com.laundry.merchant.network

import com.laundry.merchant.data.model.MerchantProfile
import com.laundry.merchant.data.model.MerchantStatistics
import com.laundry.merchant.ui.finance.AccountInfo
import com.laundry.merchant.ui.finance.FinanceStatistics
import com.laundry.merchant.ui.finance.Transaction
import com.laundry.merchant.ui.main.PromotionStatus
import com.laundry.merchant.ui.order.Order
import com.laundry.merchant.ui.promotion.PromotionCampaign
import com.laundry.merchant.ui.promotion.PromotionKeyword
import com.laundry.merchant.ui.promotion.PromotionStatistics
import com.laundry.merchant.ui.ranking.MerchantRanking
import retrofit2.Response
import retrofit2.http.*

interface ApiService {
    
    // 商家相关接口
    @GET("merchant/profile")
    suspend fun getMerchantProfile(): Response<ApiResponse<MerchantProfile>>
    
    @PUT("merchant/profile")
    suspend fun updateMerchantProfile(@Body profile: MerchantProfile): Response<ApiResponse<MerchantProfile>>
    
    @GET("merchant/statistics")
    suspend fun getMerchantStatistics(): Response<ApiResponse<MerchantStatistics>>
    
    // 订单相关接口
    @GET("orders")
    suspend fun getOrders(
        @Query("status") status: String? = null,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<List<Order>>>
    
    @GET("orders/{orderId}")
    suspend fun getOrderById(@Path("orderId") orderId: String): Response<ApiResponse<Order>>
    
    @POST("orders/{orderId}/accept")
    suspend fun acceptOrder(@Path("orderId") orderId: String): Response<ApiResponse<Unit>>
    
    @PUT("orders/{orderId}/status")
    suspend fun updateOrderStatus(
        @Path("orderId") orderId: String,
        @Body request: UpdateStatusRequest
    ): Response<ApiResponse<Unit>>
    
    @POST("orders/{orderId}/cancel")
    suspend fun cancelOrder(
        @Path("orderId") orderId: String,
        @Body request: CancelOrderRequest
    ): Response<ApiResponse<Unit>>
    
    // 财务相关接口
    @GET("finance/account")
    suspend fun getAccountInfo(): Response<ApiResponse<AccountInfo>>
    
    @GET("finance/statistics")
    suspend fun getFinanceStatistics(): Response<ApiResponse<FinanceStatistics>>
    
    @GET("finance/transactions")
    suspend fun getTransactions(
        @Query("type") type: String? = null,
        @Query("category") category: String? = null,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<List<Transaction>>>
    
    @POST("finance/recharge")
    suspend fun recharge(@Body request: RechargeRequest): Response<ApiResponse<Unit>>
    
    @POST("finance/withdraw")
    suspend fun withdraw(@Body request: WithdrawRequest): Response<ApiResponse<Unit>>
    
    // 投流相关接口
    @GET("promotion/status")
    suspend fun getPromotionStatus(): Response<ApiResponse<PromotionStatus>>
    
    @GET("promotion/statistics")
    suspend fun getPromotionStatistics(): Response<ApiResponse<PromotionStatistics>>
    
    @GET("promotion/campaigns")
    suspend fun getPromotionCampaigns(): Response<ApiResponse<List<PromotionCampaign>>>
    
    @GET("promotion/keywords")
    suspend fun getPromotionKeywords(): Response<ApiResponse<List<PromotionKeyword>>>
    
    @POST("promotion/start")
    suspend fun startPromotion(): Response<ApiResponse<Unit>>
    
    @POST("promotion/pause")
    suspend fun pausePromotion(): Response<ApiResponse<Unit>>
    
    @PUT("promotion/budget")
    suspend fun updateDailyBudget(@Body request: UpdateBudgetRequest): Response<ApiResponse<Unit>>
    
    @PUT("promotion/campaigns/{campaignId}/status")
    suspend fun toggleCampaignStatus(@Path("campaignId") campaignId: String): Response<ApiResponse<Unit>>
    
    @PUT("promotion/keywords/{keywordId}/status")
    suspend fun toggleKeywordStatus(@Path("keywordId") keywordId: String): Response<ApiResponse<Unit>>
    
    @PUT("promotion/keywords/{keywordId}/bid")
    suspend fun updateKeywordBid(
        @Path("keywordId") keywordId: String,
        @Body request: UpdateBidRequest
    ): Response<ApiResponse<Unit>>
    
    // 排行榜相关接口
    @GET("ranking/my")
    suspend fun getMyRanking(
        @Query("type") type: String,
        @Query("period") period: String,
        @Query("area") area: String? = null
    ): Response<ApiResponse<MerchantRanking>>
    
    @GET("ranking/list")
    suspend fun getRankings(
        @Query("type") type: String,
        @Query("period") period: String,
        @Query("area") area: String? = null,
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 50
    ): Response<ApiResponse<List<MerchantRanking>>>
    
    // 认证相关接口
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<ApiResponse<LoginResponse>>
    
    @POST("auth/logout")
    suspend fun logout(): Response<ApiResponse<Unit>>
    
    @POST("auth/refresh")
    suspend fun refreshToken(@Body request: RefreshTokenRequest): Response<ApiResponse<LoginResponse>>
    
    // 通知相关接口
    @GET("notifications")
    suspend fun getNotifications(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20
    ): Response<ApiResponse<List<Notification>>>
    
    @GET("notifications/unread-count")
    suspend fun getUnreadNotificationCount(): Response<ApiResponse<Int>>
    
    @PUT("notifications/{notificationId}/read")
    suspend fun markNotificationAsRead(@Path("notificationId") notificationId: String): Response<ApiResponse<Unit>>
    
    @POST("notifications/fcm-token")
    suspend fun updateFcmToken(@Body request: UpdateFcmTokenRequest): Response<ApiResponse<Unit>>
}

// 请求数据类
data class UpdateStatusRequest(val status: String)
data class CancelOrderRequest(val reason: String)
data class RechargeRequest(val amount: Double, val paymentMethod: String)
data class WithdrawRequest(val amount: Double, val bankAccount: String)
data class UpdateBudgetRequest(val dailyBudget: Double)
data class UpdateBidRequest(val bid: Double)
data class LoginRequest(val username: String, val password: String)
data class RefreshTokenRequest(val refreshToken: String)
data class UpdateFcmTokenRequest(val token: String)

// 响应数据类
data class LoginResponse(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Long,
    val merchantProfile: MerchantProfile
)

data class Notification(
    val id: String,
    val title: String,
    val content: String,
    val type: String,
    val isRead: Boolean,
    val createdAt: String
)
