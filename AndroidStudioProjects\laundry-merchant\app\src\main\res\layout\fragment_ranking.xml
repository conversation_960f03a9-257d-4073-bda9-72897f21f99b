<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- 我的排名卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_background"
            android:orientation="vertical"
            android:padding="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="我的排名"
                        android:textColor="@android:color/white"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/textMyRank"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="第8名"
                            android:textColor="@android:color/white"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/textRankChange"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:text="↑2"
                            android:textColor="@color/green_300"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="综合评分"
                        android:textColor="@android:color/white"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/textMyScore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="87.5分"
                        android:textColor="@android:color/white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <!-- 详细数据 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textMyOrderCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="156单"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="订单量"
                        android:textColor="@android:color/white"
                        android:textSize="12sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textMyRevenue"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¥7850"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="营业额"
                        android:textColor="@android:color/white"
                        android:textSize="12sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textMyRating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.6分"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="好评率"
                        android:textColor="@android:color/white"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 筛选条件 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <!-- 时间筛选 -->
        <com.google.android.material.chip.ChipGroup
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chipToday"
                style="@style/Widget.MaterialComponents.Chip.Choice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="今日" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipWeek"
                style="@style/Widget.MaterialComponents.Chip.Choice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="本周" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipMonth"
                style="@style/Widget.MaterialComponents.Chip.Choice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="本月" />

        </com.google.android.material.chip.ChipGroup>

        <!-- 地区筛选 -->
        <Button
            android:id="@+id/buttonAreaFilter"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:drawableEnd="@drawable/ic_arrow_drop_down"
            android:text="全部地区"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/textAreaFilter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="全部地区"
            android:textColor="@color/gray_600"
            android:textSize="14sp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- 排名类型标签 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        app:tabIndicatorColor="@color/colorPrimary"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="@color/colorPrimary"
        app:tabTextColor="@color/gray_600" />

    <!-- 排行榜列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewRanking"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp" />

            <!-- 空状态 -->
            <LinearLayout
                android:id="@+id/emptyView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_trophy"
                    android:tint="@color/gray_400" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="暂无排行榜数据"
                    android:textColor="@color/gray_500"
                    android:textSize="16sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="努力经营，争取上榜！"
                    android:textColor="@color/gray_400"
                    android:textSize="14sp" />

            </LinearLayout>

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>
