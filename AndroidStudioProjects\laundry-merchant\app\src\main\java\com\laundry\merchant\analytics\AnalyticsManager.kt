package com.laundry.merchant.analytics

import android.content.Context
import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AnalyticsManager @Inject constructor(
    private val context: Context
) {
    
    private val firebaseAnalytics: FirebaseAnalytics = Firebase.analytics
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    private val _userBehaviorEvents = MutableSharedFlow<UserBehaviorEvent>()
    val userBehaviorEvents: SharedFlow<UserBehaviorEvent> = _userBehaviorEvents.asSharedFlow()
    
    private val _businessMetrics = MutableSharedFlow<BusinessMetric>()
    val businessMetrics: SharedFlow<BusinessMetric> = _businessMetrics.asSharedFlow()

    // 用户行为追踪
    fun trackUserLogin(userId: String, loginMethod: String) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.LOGIN) {
            param(FirebaseAnalytics.Param.METHOD, loginMethod)
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.Login(userId, loginMethod, System.currentTimeMillis())
            )
        }
    }

    fun trackUserRegister(userId: String, registrationMethod: String) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SIGN_UP) {
            param(FirebaseAnalytics.Param.METHOD, registrationMethod)
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.Register(userId, registrationMethod, System.currentTimeMillis())
            )
        }
    }

    fun trackProductView(userId: String, productId: String, productName: String, category: String) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.VIEW_ITEM) {
            param(FirebaseAnalytics.Param.ITEM_ID, productId)
            param(FirebaseAnalytics.Param.ITEM_NAME, productName)
            param(FirebaseAnalytics.Param.ITEM_CATEGORY, category)
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.ProductView(userId, productId, productName, category, System.currentTimeMillis())
            )
        }
    }

    fun trackAddToCart(userId: String, productId: String, productName: String, price: Double, quantity: Int) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.ADD_TO_CART) {
            param(FirebaseAnalytics.Param.ITEM_ID, productId)
            param(FirebaseAnalytics.Param.ITEM_NAME, productName)
            param(FirebaseAnalytics.Param.PRICE, price)
            param(FirebaseAnalytics.Param.QUANTITY, quantity.toLong())
            param(FirebaseAnalytics.Param.CURRENCY, "CNY")
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.AddToCart(userId, productId, productName, price, quantity, System.currentTimeMillis())
            )
        }
    }

    fun trackPurchase(userId: String, orderId: String, totalAmount: Double, paymentMethod: String, items: List<PurchaseItem>) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.PURCHASE) {
            param(FirebaseAnalytics.Param.TRANSACTION_ID, orderId)
            param(FirebaseAnalytics.Param.VALUE, totalAmount)
            param(FirebaseAnalytics.Param.CURRENCY, "CNY")
            param("payment_method", paymentMethod)
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.Purchase(userId, orderId, totalAmount, paymentMethod, items, System.currentTimeMillis())
            )
            
            _businessMetrics.emit(
                BusinessMetric.Revenue(totalAmount, "CNY", System.currentTimeMillis())
            )
        }
    }

    fun trackSearch(userId: String, searchQuery: String, resultCount: Int) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SEARCH) {
            param(FirebaseAnalytics.Param.SEARCH_TERM, searchQuery)
            param("result_count", resultCount.toLong())
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.Search(userId, searchQuery, resultCount, System.currentTimeMillis())
            )
        }
    }

    fun trackBookingCreated(userId: String, bookingId: String, serviceType: String, amount: Double) {
        firebaseAnalytics.logEvent("booking_created") {
            param("booking_id", bookingId)
            param("service_type", serviceType)
            param("amount", amount)
            param(FirebaseAnalytics.Param.CURRENCY, "CNY")
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.BookingCreated(userId, bookingId, serviceType, amount, System.currentTimeMillis())
            )
        }
    }

    fun trackPageView(userId: String, pageName: String, pageClass: String) {
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW) {
            param(FirebaseAnalytics.Param.SCREEN_NAME, pageName)
            param(FirebaseAnalytics.Param.SCREEN_CLASS, pageClass)
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.PageView(userId, pageName, pageClass, System.currentTimeMillis())
            )
        }
    }

    fun trackButtonClick(userId: String, buttonName: String, pageName: String) {
        firebaseAnalytics.logEvent("button_click") {
            param("button_name", buttonName)
            param("page_name", pageName)
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.ButtonClick(userId, buttonName, pageName, System.currentTimeMillis())
            )
        }
    }

    fun trackError(userId: String, errorType: String, errorMessage: String, pageName: String) {
        firebaseAnalytics.logEvent("app_error") {
            param("error_type", errorType)
            param("error_message", errorMessage)
            param("page_name", pageName)
        }
        
        scope.launch {
            _userBehaviorEvents.emit(
                UserBehaviorEvent.Error(userId, errorType, errorMessage, pageName, System.currentTimeMillis())
            )
        }
    }

    // 业务指标追踪
    fun trackUserRetention(userId: String, daysSinceFirstUse: Int) {
        firebaseAnalytics.logEvent("user_retention") {
            param("days_since_first_use", daysSinceFirstUse.toLong())
        }
        
        scope.launch {
            _businessMetrics.emit(
                BusinessMetric.UserRetention(userId, daysSinceFirstUse, System.currentTimeMillis())
            )
        }
    }

    fun trackSessionDuration(userId: String, durationSeconds: Long) {
        firebaseAnalytics.logEvent("session_duration") {
            param("duration_seconds", durationSeconds)
        }
        
        scope.launch {
            _businessMetrics.emit(
                BusinessMetric.SessionDuration(userId, durationSeconds, System.currentTimeMillis())
            )
        }
    }

    fun trackConversionFunnel(userId: String, funnelStep: String, funnelName: String) {
        firebaseAnalytics.logEvent("conversion_funnel") {
            param("funnel_step", funnelStep)
            param("funnel_name", funnelName)
        }
        
        scope.launch {
            _businessMetrics.emit(
                BusinessMetric.ConversionFunnel(userId, funnelStep, funnelName, System.currentTimeMillis())
            )
        }
    }

    fun trackCustomEvent(eventName: String, parameters: Map<String, Any>) {
        firebaseAnalytics.logEvent(eventName) {
            parameters.forEach { (key, value) ->
                when (value) {
                    is String -> param(key, value)
                    is Long -> param(key, value)
                    is Double -> param(key, value)
                    is Int -> param(key, value.toLong())
                    is Float -> param(key, value.toDouble())
                    is Boolean -> param(key, if (value) 1L else 0L)
                }
            }
        }
    }

    // 设置用户属性
    fun setUserProperty(propertyName: String, propertyValue: String) {
        firebaseAnalytics.setUserProperty(propertyName, propertyValue)
    }

    fun setUserId(userId: String) {
        firebaseAnalytics.setUserId(userId)
    }

    fun setUserProperties(properties: Map<String, String>) {
        properties.forEach { (key, value) ->
            firebaseAnalytics.setUserProperty(key, value)
        }
    }

    // 性能监控
    fun trackPerformance(operationName: String, durationMs: Long, success: Boolean) {
        firebaseAnalytics.logEvent("performance_metric") {
            param("operation_name", operationName)
            param("duration_ms", durationMs)
            param("success", if (success) 1L else 0L)
        }
    }

    fun trackNetworkRequest(url: String, method: String, statusCode: Int, durationMs: Long) {
        firebaseAnalytics.logEvent("network_request") {
            param("url", url)
            param("method", method)
            param("status_code", statusCode.toLong())
            param("duration_ms", durationMs)
        }
    }

    // A/B测试
    fun trackABTestExposure(testName: String, variant: String) {
        firebaseAnalytics.logEvent("ab_test_exposure") {
            param("test_name", testName)
            param("variant", variant)
        }
    }

    fun trackABTestConversion(testName: String, variant: String, conversionType: String) {
        firebaseAnalytics.logEvent("ab_test_conversion") {
            param("test_name", testName)
            param("variant", variant)
            param("conversion_type", conversionType)
        }
    }
}
