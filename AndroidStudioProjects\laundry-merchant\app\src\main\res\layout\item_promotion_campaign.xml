<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 计划头部 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textViewCampaignName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="关键词推广"
                        android:textColor="@color/gray_800"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textViewCampaignType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/background_category"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="2dp"
                        android:text="关键词"
                        android:textColor="@color/blue_500"
                        android:textSize="10sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textViewStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="投放中"
                        android:textColor="@color/green_500"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textViewCreatedAt"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:text="创建: 12-01"
                        android:textColor="@color/gray_500"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

            <Switch
                android:id="@+id/switchStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- 预算信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textViewBudget"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="日预算: ¥100.00"
                android:textColor="@color/gray_600"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/textViewSpent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="今日消费: ¥78.50"
                android:textColor="@color/orange_500"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 数据统计 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/textViewClicks"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="点击: 65"
                android:textColor="@color/gray_600"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/textViewImpressions"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="展现: 850"
                android:textColor="@color/gray_600"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/textViewOrders"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="订单: 8"
                android:textColor="@color/green_500"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <Button
            android:id="@+id/buttonEdit"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/button_outline_background"
            android:text="编辑计划"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
