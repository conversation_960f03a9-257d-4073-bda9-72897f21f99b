package com.laundry.merchant.ui.promotion.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.Switch
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.ui.promotion.PromotionCampaign
import java.text.SimpleDateFormat
import java.util.Locale

class PromotionCampaignAdapter(
    private val onCampaignClick: (PromotionCampaign) -> Unit,
    private val onToggleStatus: (PromotionCampaign) -> Unit,
    private val onEditCampaign: (PromotionCampaign) -> Unit
) : ListAdapter<PromotionCampaign, PromotionCampaignAdapter.ViewHolder>(CampaignDiffCallback()) {

    private val dateFormat = SimpleDateFormat("MM-dd", Locale.getDefault())

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_promotion_campaign, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<PromotionCampaign>) {
        submitList(newData)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val nameTextView: TextView = itemView.findViewById(R.id.textViewCampaignName)
        private val typeTextView: TextView = itemView.findViewById(R.id.textViewCampaignType)
        private val statusTextView: TextView = itemView.findViewById(R.id.textViewStatus)
        private val budgetTextView: TextView = itemView.findViewById(R.id.textViewBudget)
        private val spentTextView: TextView = itemView.findViewById(R.id.textViewSpent)
        private val clicksTextView: TextView = itemView.findViewById(R.id.textViewClicks)
        private val impressionsTextView: TextView = itemView.findViewById(R.id.textViewImpressions)
        private val ordersTextView: TextView = itemView.findViewById(R.id.textViewOrders)
        private val createdAtTextView: TextView = itemView.findViewById(R.id.textViewCreatedAt)
        private val statusSwitch: Switch = itemView.findViewById(R.id.switchStatus)
        private val editButton: Button = itemView.findViewById(R.id.buttonEdit)

        fun bind(campaign: PromotionCampaign) {
            nameTextView.text = campaign.name
            budgetTextView.text = "日预算: ¥${String.format("%.2f", campaign.dailyBudget)}"
            spentTextView.text = "今日消费: ¥${String.format("%.2f", campaign.todaySpent)}"
            clicksTextView.text = "点击: ${campaign.clicks}"
            impressionsTextView.text = "展现: ${campaign.impressions}"
            ordersTextView.text = "订单: ${campaign.orders}"
            createdAtTextView.text = "创建: ${dateFormat.format(campaign.createdAt)}"
            
            // 设置推广类型
            typeTextView.text = when (campaign.type) {
                "keyword" -> "关键词"
                "shop" -> "店铺"
                "service" -> "服务"
                else -> "其他"
            }
            
            // 设置状态
            statusTextView.text = when (campaign.status) {
                "active" -> "投放中"
                "paused" -> "已暂停"
                "stopped" -> "已停止"
                else -> "异常"
            }
            
            val statusColor = when (campaign.status) {
                "active" -> R.color.green_500
                "paused" -> R.color.orange_500
                "stopped" -> R.color.red_500
                else -> R.color.gray_500
            }
            statusTextView.setTextColor(
                ContextCompat.getColor(itemView.context, statusColor)
            )
            
            // 设置开关状态
            statusSwitch.isChecked = campaign.status == "active"
            statusSwitch.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked != (campaign.status == "active")) {
                    onToggleStatus(campaign)
                }
            }
            
            // 设置按钮点击
            editButton.setOnClickListener {
                onEditCampaign(campaign)
            }
            
            itemView.setOnClickListener {
                onCampaignClick(campaign)
            }
        }
    }

    private class CampaignDiffCallback : DiffUtil.ItemCallback<PromotionCampaign>() {
        override fun areItemsTheSame(oldItem: PromotionCampaign, newItem: PromotionCampaign): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: PromotionCampaign, newItem: PromotionCampaign): Boolean {
            return oldItem == newItem
        }
    }
}
