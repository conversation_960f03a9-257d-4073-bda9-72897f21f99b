package com.laundry.merchant.ui.map

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.laundry.merchant.R
import com.laundry.merchant.databinding.ActivityMapBinding
import com.laundry.merchant.map.MapManager
import com.laundry.merchant.map.MerchantLocation
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class MapActivity : AppCompatActivity(), OnMapReadyCallback {

    private lateinit var binding: ActivityMapBinding
    private val viewModel: MapViewModel by viewModels()
    
    @Inject
    lateinit var mapManager: MapManager
    
    private lateinit var bottomSheetBehavior: BottomSheetBehavior<View>
    private var selectedMerchant: MerchantLocation? = null

    private val locationPermissionRequest = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        when {
            permissions.getOrDefault(Manifest.permission.ACCESS_FINE_LOCATION, false) -> {
                // 精确位置权限已授予
                enableLocationFeatures()
            }
            permissions.getOrDefault(Manifest.permission.ACCESS_COARSE_LOCATION, false) -> {
                // 粗略位置权限已授予
                enableLocationFeatures()
            }
            else -> {
                // 位置权限被拒绝
                showToast("需要位置权限才能显示附近商家")
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMapBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupBottomSheet()
        setupMap()
        observeViewModel()
        
        // 检查位置权限
        checkLocationPermission()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "附近商家"

        // 设置搜索按钮
        binding.buttonSearch.setOnClickListener {
            val query = binding.editTextSearch.text.toString().trim()
            if (query.isNotEmpty()) {
                viewModel.searchMerchants(query)
            }
        }

        // 设置筛选按钮
        binding.buttonFilter.setOnClickListener {
            showFilterDialog()
        }

        // 设置定位按钮
        binding.buttonMyLocation.setOnClickListener {
            viewModel.getCurrentLocation()
        }

        // 设置路线规划按钮
        binding.buttonRoute.setOnClickListener {
            selectedMerchant?.let { merchant ->
                viewModel.planRoute(merchant)
            }
        }

        // 设置预约按钮
        binding.buttonBooking.setOnClickListener {
            selectedMerchant?.let { merchant ->
                // TODO: 跳转到预约页面
                showToast("跳转到预约页面")
            }
        }
    }

    private fun setupBottomSheet() {
        bottomSheetBehavior = BottomSheetBehavior.from(binding.bottomSheet)
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_HIDDEN
        
        bottomSheetBehavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                when (newState) {
                    BottomSheetBehavior.STATE_HIDDEN -> {
                        selectedMerchant = null
                    }
                }
            }
            
            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                // 可以在这里处理滑动动画
            }
        })
    }

    private fun setupMap() {
        val mapFragment = supportFragmentManager.findFragmentById(R.id.mapFragment) as SupportMapFragment
        mapFragment.getMapAsync(this)
    }

    override fun onMapReady(googleMap: GoogleMap) {
        mapManager.initializeMap(googleMap)
        mapManager.initializeLocationClient(this)
        
        // 设置地图点击监听
        googleMap.setOnMarkerClickListener { marker ->
            val merchantId = marker.tag as? String
            if (merchantId != null) {
                viewModel.selectMerchant(merchantId)
            }
            true
        }
        
        // 加载附近商家
        viewModel.loadNearbyMerchants()
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }

        // 观察地图管理器的状态
        lifecycleScope.launch {
            mapManager.currentLocation.collect { location ->
                location?.let {
                    viewModel.updateCurrentLocation(LatLng(it.latitude, it.longitude))
                }
            }
        }
    }

    private fun updateUI(state: MapUiState) {
        // 更新加载状态
        binding.progressBar.visibility = if (state.isLoading) View.VISIBLE else View.GONE

        // 更新商家列表
        if (state.merchants.isNotEmpty()) {
            mapManager.showMerchants(state.merchants)
        }

        // 更新选中的商家信息
        state.selectedMerchant?.let { merchant ->
            showMerchantDetails(merchant)
        }

        // 更新配送路线
        state.deliveryRoute?.let { route ->
            mapManager.showDeliveryRoute(route)
        }

        // 更新服务范围
        state.serviceArea?.let { area ->
            mapManager.showServiceArea(area.center, area.radius)
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: MapEvent) {
        when (event) {
            is MapEvent.ShowError -> {
                showError(event.message)
            }
            is MapEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is MapEvent.MoveToLocation -> {
                mapManager.moveToLocation(event.location, event.zoom)
            }
            is MapEvent.NavigateToBooking -> {
                // TODO: 跳转到预约页面
                showToast("跳转到预约页面")
            }
        }
    }

    private fun showMerchantDetails(merchant: MerchantLocation) {
        selectedMerchant = merchant
        
        // 更新底部面板内容
        binding.textViewMerchantName.text = merchant.name
        binding.textViewMerchantAddress.text = merchant.address
        binding.textViewMerchantPhone.text = merchant.phone
        binding.ratingBarMerchant.rating = merchant.rating
        binding.textViewMerchantRating.text = merchant.rating.toString()
        
        // 更新状态
        val statusText = when (merchant.status) {
            com.laundry.merchant.data.model.MerchantStatus.AVAILABLE -> "空闲"
            com.laundry.merchant.data.model.MerchantStatus.BUSY -> "忙碌"
            com.laundry.merchant.data.model.MerchantStatus.OFFLINE -> "离线"
            else -> "未知"
        }
        binding.textViewMerchantStatus.text = statusText
        
        val statusColor = when (merchant.status) {
            com.laundry.merchant.data.model.MerchantStatus.AVAILABLE -> R.color.green_500
            com.laundry.merchant.data.model.MerchantStatus.BUSY -> R.color.orange_500
            com.laundry.merchant.data.model.MerchantStatus.OFFLINE -> R.color.red_500
            else -> R.color.gray_500
        }
        binding.textViewMerchantStatus.setTextColor(ContextCompat.getColor(this, statusColor))
        
        // 更新距离
        merchant.distance?.let { distance ->
            binding.textViewMerchantDistance.text = String.format("%.1fkm", distance / 1000)
        }
        
        // 更新服务类型
        binding.textViewServiceTypes.text = merchant.serviceTypes.joinToString(", ")
        
        // 显示底部面板
        bottomSheetBehavior.state = BottomSheetBehavior.STATE_COLLAPSED
    }

    private fun showFilterDialog() {
        // TODO: 显示筛选对话框
        showToast("筛选功能开发中")
    }

    private fun checkLocationPermission() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED -> {
                enableLocationFeatures()
            }
            else -> {
                locationPermissionRequest.launch(
                    arrayOf(
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.ACCESS_COARSE_LOCATION
                    )
                )
            }
        }
    }

    private fun enableLocationFeatures() {
        mapManager.startLocationUpdates()
        viewModel.enableLocationFeatures()
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    override fun onResume() {
        super.onResume()
        if (ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            mapManager.startLocationUpdates()
        }
    }

    override fun onPause() {
        super.onPause()
        mapManager.stopLocationUpdates()
    }
}
