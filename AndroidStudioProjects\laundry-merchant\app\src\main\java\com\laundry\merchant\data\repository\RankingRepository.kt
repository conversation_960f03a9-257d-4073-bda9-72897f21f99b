package com.laundry.merchant.data.repository

import com.laundry.merchant.ui.ranking.MerchantRanking
import javax.inject.Inject
import javax.inject.Singleton

interface RankingRepository {
    suspend fun getMyRanking(type: String, period: String, area: String?): MerchantRanking
    suspend fun getRankings(type: String, period: String, area: String?): List<MerchantRanking>
}

@Singleton
class RankingRepositoryImpl @Inject constructor(
    // private val apiService: RankingApiService,
    // private val localDataSource: RankingLocalDataSource
) : RankingRepository {

    override suspend fun getMyRanking(type: String, period: String, area: String?): MerchantRanking {
        // TODO: 实际实现中应该从API获取数据
        return MerchantRanking(
            id = "MERCHANT_001",
            name = "快洁洗衣店",
            avatar = null,
            rank = 8,
            rankChange = 2, // 上升2位
            score = 87.5,
            orderCount = 156,
            revenue = 7850.0,
            rating = 4.6,
            responseTime = 15,
            completionRate = 0.98,
            area = "朝阳区",
            isMyself = true
        )
    }

    override suspend fun getRankings(type: String, period: String, area: String?): List<MerchantRanking> {
        // TODO: 实际实现中应该从API获取数据
        return listOf(
            MerchantRanking(
                id = "MERCHANT_TOP1",
                name = "金牌洗衣",
                avatar = null,
                rank = 1,
                rankChange = 0,
                score = 95.8,
                orderCount = 320,
                revenue = 18500.0,
                rating = 4.9,
                responseTime = 8,
                completionRate = 0.99,
                area = "朝阳区"
            ),
            MerchantRanking(
                id = "MERCHANT_TOP2",
                name = "优质干洗店",
                avatar = null,
                rank = 2,
                rankChange = 1,
                score = 93.2,
                orderCount = 285,
                revenue = 16200.0,
                rating = 4.8,
                responseTime = 10,
                completionRate = 0.98,
                area = "朝阳区"
            ),
            MerchantRanking(
                id = "MERCHANT_TOP3",
                name = "便民洗衣",
                avatar = null,
                rank = 3,
                rankChange = -1,
                score = 91.5,
                orderCount = 268,
                revenue = 15800.0,
                rating = 4.7,
                responseTime = 12,
                completionRate = 0.97,
                area = "朝阳区"
            ),
            MerchantRanking(
                id = "MERCHANT_TOP4",
                name = "专业洗护",
                avatar = null,
                rank = 4,
                rankChange = 2,
                score = 90.1,
                orderCount = 245,
                revenue = 14200.0,
                rating = 4.6,
                responseTime = 13,
                completionRate = 0.96,
                area = "朝阳区"
            ),
            MerchantRanking(
                id = "MERCHANT_TOP5",
                name = "快速洗衣",
                avatar = null,
                rank = 5,
                rankChange = -1,
                score = 89.3,
                orderCount = 230,
                revenue = 13500.0,
                rating = 4.5,
                responseTime = 14,
                completionRate = 0.95,
                area = "朝阳区"
            ),
            MerchantRanking(
                id = "MERCHANT_TOP6",
                name = "精品洗护",
                avatar = null,
                rank = 6,
                rankChange = 0,
                score = 88.7,
                orderCount = 210,
                revenue = 12800.0,
                rating = 4.5,
                responseTime = 16,
                completionRate = 0.94,
                area = "朝阳区"
            ),
            MerchantRanking(
                id = "MERCHANT_TOP7",
                name = "绿色洗衣",
                avatar = null,
                rank = 7,
                rankChange = 1,
                score = 88.0,
                orderCount = 195,
                revenue = 11200.0,
                rating = 4.4,
                responseTime = 17,
                completionRate = 0.93,
                area = "朝阳区"
            ),
            MerchantRanking(
                id = "MERCHANT_001", // 我的店铺
                name = "快洁洗衣店",
                avatar = null,
                rank = 8,
                rankChange = 2,
                score = 87.5,
                orderCount = 156,
                revenue = 7850.0,
                rating = 4.6,
                responseTime = 15,
                completionRate = 0.98,
                area = "朝阳区",
                isMyself = true
            ),
            MerchantRanking(
                id = "MERCHANT_TOP9",
                name = "社区洗衣",
                avatar = null,
                rank = 9,
                rankChange = -2,
                score = 86.8,
                orderCount = 180,
                revenue = 9800.0,
                rating = 4.3,
                responseTime = 18,
                completionRate = 0.92,
                area = "朝阳区"
            ),
            MerchantRanking(
                id = "MERCHANT_TOP10",
                name = "24小时洗衣",
                avatar = null,
                rank = 10,
                rankChange = 0,
                score = 86.2,
                orderCount = 165,
                revenue = 8900.0,
                rating = 4.2,
                responseTime = 20,
                completionRate = 0.91,
                area = "朝阳区"
            )
        )
    }
}
