package com.laundry.merchant.worker

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.laundry.merchant.data.repository.MerchantRepository
import com.laundry.merchant.data.repository.OrderRepository
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@HiltWorker
class DataSyncWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val merchantRepository: MerchantRepository,
    private val orderRepository: OrderRepository
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            // 同步商家信息
            syncMerchantData()
            
            // 同步订单数据
            syncOrderData()
            
            // 同步其他必要数据
            syncOtherData()
            
            Result.success()
        } catch (e: Exception) {
            // 记录错误日志
            android.util.Log.e("DataSyncWorker", "Sync failed", e)
            
            // 如果是网络错误，可以重试
            if (runAttemptCount < 3) {
                Result.retry()
            } else {
                Result.failure()
            }
        }
    }

    private suspend fun syncMerchantData() {
        try {
            // 获取最新的商家信息
            val merchantProfile = merchantRepository.getMerchantProfile()
            // 可以在这里更新本地缓存或执行其他同步逻辑
            
        } catch (e: Exception) {
            android.util.Log.w("DataSyncWorker", "Failed to sync merchant data", e)
            throw e
        }
    }

    private suspend fun syncOrderData() {
        try {
            // 获取最新的订单信息
            val orders = orderRepository.getOrders()
            // 可以在这里更新本地缓存或执行其他同步逻辑
            
        } catch (e: Exception) {
            android.util.Log.w("DataSyncWorker", "Failed to sync order data", e)
            throw e
        }
    }

    private suspend fun syncOtherData() {
        try {
            // 同步其他数据，如财务信息、投流数据等
            // 这里可以根据需要添加更多同步逻辑
            
        } catch (e: Exception) {
            android.util.Log.w("DataSyncWorker", "Failed to sync other data", e)
            throw e
        }
    }
}
