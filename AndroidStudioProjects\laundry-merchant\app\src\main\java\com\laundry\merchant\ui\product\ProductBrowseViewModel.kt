package com.laundry.merchant.ui.product

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.model.*
import com.laundry.merchant.data.repository.ProductRepository
import com.laundry.merchant.network.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProductBrowseViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ProductBrowseUiState())
    val uiState: StateFlow<ProductBrowseUiState> = _uiState.asStateFlow()

    private val _events = MutableSharedFlow<ProductBrowseEvent>()
    val events: SharedFlow<ProductBrowseEvent> = _events.asSharedFlow()

    private var currentFilter = ProductFilter()
    private var currentCategoryId: String? = null

    fun loadCategories() {
        viewModelScope.launch {
            when (val result = productRepository.getHotCategories()) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(hotCategories = result.data)
                }
                is NetworkResult.Error -> {
                    _events.emit(ProductBrowseEvent.ShowError("加载分类失败"))
                }
                is NetworkResult.Loading -> {
                    // 处理加载状态
                }
            }
        }
    }

    fun loadProducts() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            when (val result = productRepository.getProducts(
                categoryId = currentCategoryId,
                filter = currentFilter
            )) {
                is NetworkResult.Success -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        products = result.data
                    )
                }
                is NetworkResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                    _events.emit(ProductBrowseEvent.ShowError(result.message))
                }
                is NetworkResult.Loading -> {
                    _uiState.value = _uiState.value.copy(isLoading = true)
                }
            }
        }
    }

    fun refreshData() {
        loadCategories()
        loadProducts()
    }

    fun selectCategory(categoryId: String?) {
        currentCategoryId = categoryId
        loadProducts()
    }

    fun setSortType(sortType: SortType) {
        currentFilter = currentFilter.copy(sortBy = sortType)
        _uiState.value = _uiState.value.copy(hasActiveFilter = hasActiveFilter())
        loadProducts()
    }

    fun applyFilter(filter: ProductFilter) {
        currentFilter = filter
        _uiState.value = _uiState.value.copy(hasActiveFilter = hasActiveFilter())
        loadProducts()
    }

    fun clearFilter() {
        currentFilter = ProductFilter()
        _uiState.value = _uiState.value.copy(hasActiveFilter = false)
        loadProducts()
    }

    fun addToCart(product: Product) {
        viewModelScope.launch {
            try {
                // TODO: 实现添加到购物车逻辑
                _events.emit(ProductBrowseEvent.ShowSuccess("已添加到购物车"))
            } catch (e: Exception) {
                _events.emit(ProductBrowseEvent.ShowError("添加失败"))
            }
        }
    }

    private fun hasActiveFilter(): Boolean {
        return currentFilter.categoryIds.isNotEmpty() ||
                currentFilter.priceRange != null ||
                currentFilter.serviceTypes.isNotEmpty() ||
                currentFilter.tags.isNotEmpty() ||
                currentFilter.sortBy != SortType.DEFAULT ||
                !currentFilter.onlyAvailable ||
                currentFilter.onlyInStock ||
                currentFilter.onlyDiscount
    }
}

// UI状态数据类
data class ProductBrowseUiState(
    val isLoading: Boolean = false,
    val hotCategories: List<ProductCategory> = emptyList(),
    val products: List<Product> = emptyList(),
    val hasActiveFilter: Boolean = false,
    val error: String? = null
)

// 事件数据类
sealed class ProductBrowseEvent {
    data class ShowError(val message: String) : ProductBrowseEvent()
    data class ShowSuccess(val message: String) : ProductBrowseEvent()
    data class NavigateToDetail(val productId: String) : ProductBrowseEvent()
}
