<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.profile.ProfileFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Merchant Info Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/image_merchant_avatar"
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:background="@drawable/circle_background"
                        android:padding="16dp"
                        android:src="@drawable/ic_business_24"
                        app:tint="@color/primary_color" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/text_merchant_name"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="快洁洗衣店"
                                android:textColor="@color/primary_text"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/text_verification_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/verified"
                                android:textColor="@android:color/holo_green_dark"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/text_merchant_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="13800138000"
                            android:textColor="@color/secondary_text"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/text_merchant_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:text="<EMAIL>"
                            android:textColor="@color/secondary_text"
                            android:textSize="12sp" />

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="12dp"
                    android:background="@color/divider_color" />

                <TextView
                    android:id="@+id/text_merchant_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="北京市朝阳区某某街道123号"
                    android:textColor="@color/secondary_text"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="评分: "
                        android:textColor="@color/secondary_text"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/text_merchant_rating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.6"
                        android:textColor="@color/accent_color"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="总订单: "
                        android:textColor="@color/secondary_text"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/text_total_orders"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1250单"
                        android:textColor="@color/primary_color"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Menu Items -->
        <LinearLayout
            android:id="@+id/layout_service_management"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_services_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/service_management"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_business_info"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_business_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/business_info"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_financial_records"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_money_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/financial_records"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_settings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_settings_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/settings"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_help_support"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_help_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/help_support"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_about"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_info_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/about"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <!-- Logout Button -->
        <Button
            android:id="@+id/button_logout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:background="@drawable/button_outline"
            android:text="@string/logout"
            android:textColor="@color/error_color"
            android:textSize="16sp" />

    </LinearLayout>

</ScrollView>
