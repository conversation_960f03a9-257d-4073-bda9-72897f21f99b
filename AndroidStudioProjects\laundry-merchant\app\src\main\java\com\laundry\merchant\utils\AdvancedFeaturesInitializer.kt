package com.laundry.merchant.utils

import android.content.Context
import com.laundry.merchant.analytics.AnalyticsManager
import com.laundry.merchant.service.RealTimeUpdateService
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AdvancedFeaturesInitializer @Inject constructor(
    private val analyticsManager: AnalyticsManager
) {
    
    fun initializeAdvancedFeatures(context: Context, userId: String) {
        // 初始化分析系统
        initializeAnalytics(userId)
        
        // 启动实时更新服务
        startRealTimeService(context)
        
        // 设置用户属性
        setupUserProperties(userId)
    }
    
    private fun initializeAnalytics(userId: String) {
        // 设置用户ID
        analyticsManager.setUserId(userId)
        
        // 设置默认用户属性
        analyticsManager.setUserProperties(mapOf(
            "app_version" to getAppVersion(),
            "platform" to "Android",
            "user_type" to "merchant"
        ))
        
        // 记录应用启动事件
        analyticsManager.trackCustomEvent("app_start", mapOf(
            "timestamp" to System.currentTimeMillis(),
            "user_id" to userId
        ))
    }
    
    private fun startRealTimeService(context: Context) {
        try {
            RealTimeUpdateService.start(context)
        } catch (e: Exception) {
            // 记录错误但不影响应用启动
            analyticsManager.trackError(
                userId = "system",
                errorType = "service_start_error",
                errorMessage = e.message ?: "Unknown error",
                pageName = "app_initialization"
            )
        }
    }
    
    private fun setupUserProperties(userId: String) {
        // 可以根据用户信息设置更多属性
        // 例如：会员等级、注册时间、地理位置等
    }
    
    private fun getAppVersion(): String {
        return "1.0.0" // 实际应用中应该从BuildConfig获取
    }
    
    fun trackUserSession(userId: String, sessionStart: Long) {
        val sessionDuration = System.currentTimeMillis() - sessionStart
        analyticsManager.trackSessionDuration(userId, sessionDuration / 1000)
    }
    
    fun trackFeatureUsage(userId: String, featureName: String, parameters: Map<String, Any> = emptyMap()) {
        analyticsManager.trackCustomEvent("feature_usage", mapOf(
            "feature_name" to featureName,
            "user_id" to userId
        ) + parameters)
    }
    
    fun trackPerformanceMetric(operationName: String, durationMs: Long, success: Boolean) {
        analyticsManager.trackPerformance(operationName, durationMs, success)
    }
    
    fun trackBusinessEvent(eventName: String, value: Double, currency: String = "CNY") {
        analyticsManager.trackCustomEvent("business_event", mapOf(
            "event_name" to eventName,
            "value" to value,
            "currency" to currency,
            "timestamp" to System.currentTimeMillis()
        ))
    }
}
