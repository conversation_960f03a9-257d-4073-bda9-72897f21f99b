package com.laundry.merchant.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.fragment.app.FragmentActivity

class ThirdPartyLoginManager(private val context: Context) {

    interface LoginCallback {
        fun onSuccess(platform: LoginPlatform, userInfo: ThirdPartyUserInfo)
        fun onError(platform: LoginPlatform, error: String)
        fun onCancel(platform: LoginPlatform)
    }

    /**
     * 微信登录
     */
    fun loginWithWechat(callback: LoginCallback) {
        try {
            // TODO: 集成微信SDK
            // 这里需要集成微信开放平台SDK
            // 1. 添加微信SDK依赖
            // 2. 配置微信AppID
            // 3. 调用微信登录API
            
            // 模拟登录流程
            simulateLogin(LoginPlatform.WECHAT, callback)
        } catch (e: Exception) {
            callback.onError(LoginPlatform.WECHAT, "微信登录失败: ${e.message}")
        }
    }

    /**
     * QQ登录
     */
    fun loginWithQQ(activity: Activity, callback: LoginCallback) {
        try {
            // TODO: 集成QQ SDK
            // 这里需要集成腾讯QQ SDK
            // 1. 添加QQ SDK依赖
            // 2. 配置QQ AppID
            // 3. 调用QQ登录API
            
            // 模拟登录流程
            simulateLogin(LoginPlatform.QQ, callback)
        } catch (e: Exception) {
            callback.onError(LoginPlatform.QQ, "QQ登录失败: ${e.message}")
        }
    }

    /**
     * 支付宝登录
     */
    fun loginWithAlipay(activity: Activity, callback: LoginCallback) {
        try {
            // TODO: 集成支付宝SDK
            // 这里需要集成支付宝SDK
            // 1. 添加支付宝SDK依赖
            // 2. 配置支付宝AppID
            // 3. 调用支付宝登录API
            
            // 模拟登录流程
            simulateLogin(LoginPlatform.ALIPAY, callback)
        } catch (e: Exception) {
            callback.onError(LoginPlatform.ALIPAY, "支付宝登录失败: ${e.message}")
        }
    }

    /**
     * Apple ID登录
     */
    fun loginWithApple(activity: FragmentActivity, callback: LoginCallback) {
        try {
            // TODO: 集成Apple Sign In
            // 这里需要集成Apple Sign In
            // 1. 添加相关依赖
            // 2. 配置Apple开发者账号
            // 3. 调用Apple登录API
            
            // 模拟登录流程
            simulateLogin(LoginPlatform.APPLE, callback)
        } catch (e: Exception) {
            callback.onError(LoginPlatform.APPLE, "Apple ID登录失败: ${e.message}")
        }
    }

    /**
     * 检查第三方登录平台是否可用
     */
    fun isPlatformAvailable(platform: LoginPlatform): Boolean {
        return when (platform) {
            LoginPlatform.WECHAT -> {
                // 检查是否安装微信
                isWechatInstalled()
            }
            LoginPlatform.QQ -> {
                // 检查是否安装QQ
                isQQInstalled()
            }
            LoginPlatform.ALIPAY -> {
                // 检查是否安装支付宝
                isAlipayInstalled()
            }
            LoginPlatform.APPLE -> {
                // Apple ID登录在iOS设备上可用
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M
            }
        }
    }

    private fun isWechatInstalled(): Boolean {
        return try {
            val packageManager = context.packageManager
            packageManager.getPackageInfo("com.tencent.mm", 0)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun isQQInstalled(): Boolean {
        return try {
            val packageManager = context.packageManager
            packageManager.getPackageInfo("com.tencent.mobileqq", 0)
            true
        } catch (e: Exception) {
            false
        }
    }

    private fun isAlipayInstalled(): Boolean {
        return try {
            val packageManager = context.packageManager
            packageManager.getPackageInfo("com.eg.android.AlipayGphone", 0)
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 模拟登录流程（用于演示）
     */
    private fun simulateLogin(platform: LoginPlatform, callback: LoginCallback) {
        // 模拟网络请求延迟
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            val userInfo = ThirdPartyUserInfo(
                openId = "mock_open_id_${platform.name.lowercase()}",
                unionId = "mock_union_id_${platform.name.lowercase()}",
                nickname = "测试用户_${platform.name}",
                avatar = "",
                gender = "unknown",
                platform = platform
            )
            callback.onSuccess(platform, userInfo)
        }, 1000)
    }

    /**
     * 处理第三方登录回调
     */
    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        // 处理各个平台的回调结果
        when (requestCode) {
            REQUEST_CODE_WECHAT -> {
                // 处理微信登录回调
            }
            REQUEST_CODE_QQ -> {
                // 处理QQ登录回调
            }
            REQUEST_CODE_ALIPAY -> {
                // 处理支付宝登录回调
            }
        }
    }

    companion object {
        const val REQUEST_CODE_WECHAT = 1001
        const val REQUEST_CODE_QQ = 1002
        const val REQUEST_CODE_ALIPAY = 1003
    }
}

enum class LoginPlatform {
    WECHAT,
    QQ,
    ALIPAY,
    APPLE
}

data class ThirdPartyUserInfo(
    val openId: String,
    val unionId: String?,
    val nickname: String,
    val avatar: String,
    val gender: String,
    val platform: LoginPlatform
)
