package com.laundry.admin.ui.finance

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.data.*
import com.github.mikephil.charting.utils.ColorTemplate
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.tabs.TabLayout
import com.laundry.admin.R
import com.laundry.admin.data.model.PayoutStatus
import com.laundry.admin.databinding.ActivityFinanceManagementBinding
import com.laundry.admin.ui.finance.adapter.PayoutRequestAdapter
import com.laundry.admin.ui.finance.adapter.TransactionRecordAdapter
import com.laundry.admin.utils.formatCurrency
import com.laundry.admin.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class FinanceManagementActivity : AppCompatActivity() {

    private lateinit var binding: ActivityFinanceManagementBinding
    private val viewModel: FinanceManagementViewModel by viewModels()
    
    private lateinit var payoutAdapter: PayoutRequestAdapter
    private lateinit var transactionAdapter: TransactionRecordAdapter
    private var currentTab = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFinanceManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerViews()
        setupTabs()
        setupCharts()
        observeViewModel()
        
        // 加载财务数据
        viewModel.loadFinancialOverview()
        viewModel.loadPendingPayouts()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "财务管理"

        // 设置刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            refreshData()
        }

        // 设置快捷操作按钮
        binding.buttonBatchApproval.setOnClickListener {
            showBatchApprovalDialog()
        }

        binding.buttonExportReport.setOnClickListener {
            showExportReportDialog()
        }

        binding.buttonFinancialSettings.setOnClickListener {
            showFinancialSettingsDialog()
        }
    }

    private fun setupRecyclerViews() {
        // 提现申请列表
        payoutAdapter = PayoutRequestAdapter(
            onApprove = { payout ->
                showApprovalDialog(payout, PayoutStatus.APPROVED)
            },
            onReject = { payout ->
                showApprovalDialog(payout, PayoutStatus.CANCELLED)
            },
            onViewDetails = { payout ->
                showPayoutDetailDialog(payout)
            }
        )
        
        binding.recyclerViewPayouts.apply {
            layoutManager = LinearLayoutManager(this@FinanceManagementActivity)
            adapter = payoutAdapter
        }

        // 交易记录列表
        transactionAdapter = TransactionRecordAdapter(
            onTransactionClick = { transaction ->
                showTransactionDetailDialog(transaction)
            }
        )
        
        binding.recyclerViewTransactions.apply {
            layoutManager = LinearLayoutManager(this@FinanceManagementActivity)
            adapter = transactionAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("待审核提现"))
            addTab(newTab().setText("交易记录"))
            addTab(newTab().setText("财务报表"))
            addTab(newTab().setText("风控监控"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    currentTab = tab?.position ?: 0
                    updateTabContent(currentTab)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun setupCharts() {
        setupRevenueChart()
        setupTransactionChart()
    }

    private fun setupRevenueChart() {
        binding.chartRevenue.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            legend.isEnabled = true
        }
    }

    private fun setupTransactionChart() {
        binding.chartTransactionDistribution.apply {
            description.isEnabled = false
            setUsePercentValues(true)
            setDrawHoleEnabled(true)
            setHoleColor(android.graphics.Color.WHITE)
            holeRadius = 58f
            transparentCircleRadius = 61f
            setDrawCenterText(true)
            centerText = "交易分布"
            isRotationEnabled = true
            legend.isEnabled = true
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: FinanceManagementUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新财务概览
        state.financialOverview?.let { overview ->
            binding.textViewTotalRevenue.text = overview.totalRevenue.formatCurrency()
            binding.textViewPlatformRevenue.text = overview.platformRevenue.formatCurrency()
            binding.textViewMerchantRevenue.text = overview.merchantRevenue.formatCurrency()
            binding.textViewPendingPayouts.text = overview.pendingPayouts.formatCurrency()
            binding.textViewRefundAmount.text = overview.refundAmount.formatCurrency()
            binding.textViewTransactionFees.text = overview.transactionFees.formatCurrency()
            
            // 计算增长率
            val growthRate = calculateGrowthRate(overview.totalRevenue, state.previousRevenue)
            binding.textViewRevenueGrowth.text = "${String.format("%.1f", growthRate)}%"
            
            val growthColor = if (growthRate >= 0) R.color.green_500 else R.color.red_500
            binding.textViewRevenueGrowth.setTextColor(getColor(growthColor))
        }

        // 更新提现申请
        payoutAdapter.updateData(state.payoutRequests)
        binding.textViewPendingPayoutsCount.text = "待处理: ${state.payoutRequests.size}"

        // 更新交易记录
        transactionAdapter.updateData(state.transactionRecords)

        // 更新图表
        updateRevenueChart(state.revenueData)
        updateTransactionChart(state.transactionDistribution)

        // 更新风控指标
        state.riskMetrics?.let { metrics ->
            updateRiskMetrics(metrics)
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun updateTabContent(tabIndex: Int) {
        // 隐藏所有内容
        binding.layoutPayouts.visibility = View.GONE
        binding.layoutTransactions.visibility = View.GONE
        binding.layoutReports.visibility = View.GONE
        binding.layoutRiskMonitoring.visibility = View.GONE

        when (tabIndex) {
            0 -> {
                binding.layoutPayouts.visibility = View.VISIBLE
                viewModel.loadPendingPayouts()
            }
            1 -> {
                binding.layoutTransactions.visibility = View.VISIBLE
                viewModel.loadTransactionRecords()
            }
            2 -> {
                binding.layoutReports.visibility = View.VISIBLE
                viewModel.loadFinancialReports()
            }
            3 -> {
                binding.layoutRiskMonitoring.visibility = View.VISIBLE
                viewModel.loadRiskMetrics()
            }
        }
    }

    private fun updateRevenueChart(revenueData: List<RevenueData>) {
        if (revenueData.isEmpty()) return

        val entries = revenueData.mapIndexed { index, data ->
            Entry(index.toFloat(), data.amount.toFloat())
        }

        val dataSet = LineDataSet(entries, "收入趋势").apply {
            color = getColor(R.color.colorPrimary)
            setCircleColor(getColor(R.color.colorPrimary))
            lineWidth = 2f
            circleRadius = 4f
            setDrawCircleHole(false)
            valueTextSize = 10f
        }

        val lineData = LineData(dataSet)
        binding.chartRevenue.apply {
            data = lineData
            invalidate()
        }
    }

    private fun updateTransactionChart(distribution: Map<String, Double>) {
        if (distribution.isEmpty()) return

        val entries = distribution.map { (type, amount) ->
            PieEntry(amount.toFloat(), type)
        }

        val dataSet = PieDataSet(entries, "").apply {
            colors = ColorTemplate.MATERIAL_COLORS.toList()
            valueTextSize = 12f
            valueTextColor = android.graphics.Color.WHITE
        }

        val pieData = PieData(dataSet)
        binding.chartTransactionDistribution.apply {
            data = pieData
            invalidate()
        }
    }

    private fun updateRiskMetrics(metrics: RiskMetrics) {
        binding.textViewSuspiciousTransactions.text = "${metrics.suspiciousTransactions}"
        binding.textViewHighRiskMerchants.text = "${metrics.highRiskMerchants}"
        binding.textViewChargebackRate.text = "${String.format("%.2f", metrics.chargebackRate)}%"
        binding.textViewFraudScore.text = "${String.format("%.1f", metrics.fraudScore)}/10"
        
        // 设置风险等级颜色
        val riskColor = when {
            metrics.fraudScore >= 7 -> R.color.red_500
            metrics.fraudScore >= 4 -> R.color.orange_500
            else -> R.color.green_500
        }
        binding.textViewFraudScore.setTextColor(getColor(riskColor))
    }

    private fun handleEvent(event: FinanceManagementEvent) {
        when (event) {
            is FinanceManagementEvent.ShowError -> {
                showError(event.message)
            }
            is FinanceManagementEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is FinanceManagementEvent.PayoutApproved -> {
                showToast("提现申请已批准")
                refreshData()
            }
            is FinanceManagementEvent.PayoutRejected -> {
                showToast("提现申请已拒绝")
                refreshData()
            }
            is FinanceManagementEvent.ReportGenerated -> {
                showToast("报表已生成")
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_finance_management, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_filter -> {
                showFilterDialog()
                true
            }
            R.id.action_reconciliation -> {
                showReconciliationDialog()
                true
            }
            R.id.action_audit_trail -> {
                showAuditTrailDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showApprovalDialog(payout: com.laundry.admin.data.model.PayoutRequest, newStatus: PayoutStatus) {
        val statusText = if (newStatus == PayoutStatus.APPROVED) "批准" else "拒绝"
        
        MaterialAlertDialogBuilder(this)
            .setTitle("确认操作")
            .setMessage("确定要${statusText}商家 ${payout.merchantName} 的提现申请吗？\n金额: ${payout.amount.formatCurrency()}")
            .setPositiveButton("确定") { _, _ ->
                if (newStatus == PayoutStatus.APPROVED) {
                    showApprovalReasonDialog(payout)
                } else {
                    showRejectionReasonDialog(payout)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showApprovalReasonDialog(payout: com.laundry.admin.data.model.PayoutRequest) {
        val dialog = PayoutApprovalDialog.newInstance(payout) { reason ->
            viewModel.approvePayout(payout.id, reason)
        }
        dialog.show(supportFragmentManager, "PayoutApprovalDialog")
    }

    private fun showRejectionReasonDialog(payout: com.laundry.admin.data.model.PayoutRequest) {
        val dialog = PayoutRejectionDialog.newInstance(payout) { reason ->
            viewModel.rejectPayout(payout.id, reason)
        }
        dialog.show(supportFragmentManager, "PayoutRejectionDialog")
    }

    private fun showPayoutDetailDialog(payout: com.laundry.admin.data.model.PayoutRequest) {
        val dialog = PayoutDetailDialog.newInstance(payout)
        dialog.show(supportFragmentManager, "PayoutDetailDialog")
    }

    private fun showTransactionDetailDialog(transaction: com.laundry.admin.data.model.TransactionRecord) {
        val dialog = TransactionDetailDialog.newInstance(transaction)
        dialog.show(supportFragmentManager, "TransactionDetailDialog")
    }

    private fun showBatchApprovalDialog() {
        val selectedPayouts = payoutAdapter.getSelectedPayouts()
        if (selectedPayouts.isEmpty()) {
            showToast("请先选择要批量处理的提现申请")
            return
        }

        val operations = arrayOf("批量批准", "批量拒绝")
        
        MaterialAlertDialogBuilder(this)
            .setTitle("批量操作")
            .setItems(operations) { _, which ->
                val newStatus = if (which == 0) PayoutStatus.APPROVED else PayoutStatus.CANCELLED
                showBatchConfirmDialog(selectedPayouts, newStatus)
            }
            .show()
    }

    private fun showBatchConfirmDialog(
        payouts: List<com.laundry.admin.data.model.PayoutRequest>,
        newStatus: PayoutStatus
    ) {
        val statusText = if (newStatus == PayoutStatus.APPROVED) "批准" else "拒绝"
        val totalAmount = payouts.sumOf { it.amount }
        
        MaterialAlertDialogBuilder(this)
            .setTitle("批量${statusText}")
            .setMessage("确定要${statusText} ${payouts.size} 个提现申请吗？\n总金额: ${totalAmount.formatCurrency()}")
            .setPositiveButton("确定") { _, _ ->
                viewModel.batchProcessPayouts(payouts.map { it.id }, newStatus, "批量操作")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showExportReportDialog() {
        val reportTypes = arrayOf("日报", "周报", "月报", "年报", "自定义时间")
        
        MaterialAlertDialogBuilder(this)
            .setTitle("导出财务报表")
            .setItems(reportTypes) { _, which ->
                when (which) {
                    0 -> viewModel.exportDailyReport()
                    1 -> viewModel.exportWeeklyReport()
                    2 -> viewModel.exportMonthlyReport()
                    3 -> viewModel.exportYearlyReport()
                    4 -> showCustomReportDialog()
                }
            }
            .show()
    }

    private fun showCustomReportDialog() {
        val dialog = CustomReportDialog.newInstance { startDate, endDate, reportType ->
            viewModel.exportCustomReport(startDate, endDate, reportType)
        }
        dialog.show(supportFragmentManager, "CustomReportDialog")
    }

    private fun showFinancialSettingsDialog() {
        val dialog = FinancialSettingsDialog.newInstance()
        dialog.show(supportFragmentManager, "FinancialSettingsDialog")
    }

    private fun showFilterDialog() {
        val dialog = FinanceFilterDialog.newInstance { filter ->
            viewModel.applyFilter(filter)
        }
        dialog.show(supportFragmentManager, "FinanceFilterDialog")
    }

    private fun showReconciliationDialog() {
        val dialog = ReconciliationDialog.newInstance()
        dialog.show(supportFragmentManager, "ReconciliationDialog")
    }

    private fun showAuditTrailDialog() {
        val dialog = AuditTrailDialog.newInstance("finance")
        dialog.show(supportFragmentManager, "AuditTrailDialog")
    }

    private fun refreshData() {
        viewModel.loadFinancialOverview()
        when (currentTab) {
            0 -> viewModel.loadPendingPayouts()
            1 -> viewModel.loadTransactionRecords()
            2 -> viewModel.loadFinancialReports()
            3 -> viewModel.loadRiskMetrics()
        }
    }

    private fun calculateGrowthRate(current: Double, previous: Double?): Double {
        if (previous == null || previous == 0.0) return 0.0
        return ((current - previous) / previous) * 100
    }

    private fun showError(message: String) {
        showToast(message)
    }
}
