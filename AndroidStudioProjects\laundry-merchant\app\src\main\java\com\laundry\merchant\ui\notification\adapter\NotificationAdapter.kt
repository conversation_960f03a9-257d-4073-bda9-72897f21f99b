package com.laundry.merchant.ui.notification.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.ui.notification.NotificationData
import java.text.SimpleDateFormat
import java.util.Locale

class NotificationAdapter(
    private val onNotificationClick: (NotificationData) -> Unit,
    private val onDeleteClick: (NotificationData) -> Unit
) : ListAdapter<NotificationData, NotificationAdapter.ViewHolder>(NotificationDiffCallback()) {

    private val timeFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_notification, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<NotificationData>) {
        submitList(newData)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val iconImageView: ImageView = itemView.findViewById(R.id.imageViewIcon)
        private val titleTextView: TextView = itemView.findViewById(R.id.textViewTitle)
        private val contentTextView: TextView = itemView.findViewById(R.id.textViewContent)
        private val timeTextView: TextView = itemView.findViewById(R.id.textViewTime)
        private val unreadIndicator: View = itemView.findViewById(R.id.viewUnreadIndicator)
        private val priorityIndicator: View = itemView.findViewById(R.id.viewPriorityIndicator)
        private val actionButton: Button = itemView.findViewById(R.id.buttonAction)
        private val deleteButton: ImageView = itemView.findViewById(R.id.imageViewDelete)

        fun bind(notification: NotificationData) {
            titleTextView.text = notification.title
            contentTextView.text = notification.content
            timeTextView.text = timeFormat.format(notification.createdAt)

            // 设置图标
            val iconRes = when (notification.type) {
                "order" -> R.drawable.ic_orders
                "finance" -> R.drawable.ic_finance
                "promotion" -> R.drawable.ic_promotion
                "system" -> R.drawable.ic_settings
                else -> R.drawable.ic_notifications
            }
            iconImageView.setImageResource(iconRes)

            // 设置图标颜色
            val iconColor = when (notification.type) {
                "order" -> R.color.blue_500
                "finance" -> R.color.green_500
                "promotion" -> R.color.orange_500
                "system" -> R.color.gray_500
                else -> R.color.gray_500
            }
            iconImageView.setColorFilter(
                ContextCompat.getColor(itemView.context, iconColor)
            )

            // 设置未读状态
            unreadIndicator.visibility = if (!notification.isRead) View.VISIBLE else View.GONE
            
            // 设置背景色（未读通知高亮）
            val backgroundColor = if (!notification.isRead) {
                R.color.blue_50
            } else {
                android.R.color.white
            }
            itemView.setBackgroundColor(
                ContextCompat.getColor(itemView.context, backgroundColor)
            )

            // 设置优先级指示器
            when (notification.priority) {
                "high" -> {
                    priorityIndicator.visibility = View.VISIBLE
                    priorityIndicator.setBackgroundColor(
                        ContextCompat.getColor(itemView.context, R.color.red_500)
                    )
                }
                "normal" -> {
                    priorityIndicator.visibility = View.GONE
                }
                "low" -> {
                    priorityIndicator.visibility = View.VISIBLE
                    priorityIndicator.setBackgroundColor(
                        ContextCompat.getColor(itemView.context, R.color.gray_300)
                    )
                }
            }

            // 设置操作按钮
            if (notification.actionText != null) {
                actionButton.visibility = View.VISIBLE
                actionButton.text = notification.actionText
                actionButton.setOnClickListener {
                    onNotificationClick(notification)
                }
            } else {
                actionButton.visibility = View.GONE
            }

            // 设置删除按钮
            deleteButton.setOnClickListener {
                onDeleteClick(notification)
            }

            // 设置整体点击
            itemView.setOnClickListener {
                onNotificationClick(notification)
            }
        }
    }

    private class NotificationDiffCallback : DiffUtil.ItemCallback<NotificationData>() {
        override fun areItemsTheSame(oldItem: NotificationData, newItem: NotificationData): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: NotificationData, newItem: NotificationData): Boolean {
            return oldItem == newItem
        }
    }
}
