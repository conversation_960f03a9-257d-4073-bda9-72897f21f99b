package com.laundry.merchant.ui.product

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.laundry.merchant.databinding.ActivityProductBrowseBinding
import com.laundry.merchant.ui.product.adapter.ProductCategoryAdapter
import com.laundry.merchant.ui.product.adapter.ProductGridAdapter
import com.laundry.merchant.ui.search.SearchActivity
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ProductBrowseActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProductBrowseBinding
    private val viewModel: ProductBrowseViewModel by viewModels()
    
    private lateinit var categoryAdapter: ProductCategoryAdapter
    private lateinit var productAdapter: ProductGridAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductBrowseBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerViews()
        setupTabs()
        observeViewModel()
        
        // 加载数据
        viewModel.loadCategories()
        viewModel.loadProducts()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "商品浏览"

        // 设置搜索框点击
        binding.layoutSearch.setOnClickListener {
            startActivity(Intent(this, SearchActivity::class.java))
        }

        // 设置刷新监听
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshData()
        }

        // 设置筛选按钮
        binding.buttonFilter.setOnClickListener {
            showFilterDialog()
        }

        // 设置排序按钮
        binding.buttonSort.setOnClickListener {
            showSortDialog()
        }
    }

    private fun setupRecyclerViews() {
        // 设置热门分类
        categoryAdapter = ProductCategoryAdapter { category ->
            viewModel.selectCategory(category.id)
        }
        binding.recyclerViewHotCategories.apply {
            layoutManager = LinearLayoutManager(this@ProductBrowseActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = categoryAdapter
        }

        // 设置商品网格
        productAdapter = ProductGridAdapter(
            onProductClick = { product ->
                val intent = Intent(this, ProductDetailActivity::class.java)
                intent.putExtra("product_id", product.id)
                startActivity(intent)
            },
            onAddToCartClick = { product ->
                viewModel.addToCart(product)
            }
        )
        binding.recyclerViewProducts.apply {
            layoutManager = GridLayoutManager(this@ProductBrowseActivity, 2)
            adapter = productAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("全部"))
            addTab(newTab().setText("洗衣"))
            addTab(newTab().setText("干洗"))
            addTab(newTab().setText("床品"))
            addTab(newTab().setText("皮具"))
            addTab(newTab().setText("其他"))

            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    val categoryId = when (tab?.position) {
                        0 -> null
                        1 -> "cat_1"
                        2 -> "cat_2"
                        3 -> "cat_3"
                        4 -> "cat_5"
                        5 -> "cat_other"
                        else -> null
                    }
                    viewModel.selectCategory(categoryId)
                }

                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: ProductBrowseUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新热门分类
        categoryAdapter.updateData(state.hotCategories)

        // 更新商品列表
        productAdapter.updateData(state.products)

        // 更新空状态
        binding.emptyView.visibility = if (state.products.isEmpty() && !state.isLoading) {
            View.VISIBLE
        } else {
            View.GONE
        }

        // 更新筛选状态
        binding.buttonFilter.text = if (state.hasActiveFilter) "筛选 ●" else "筛选"

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: ProductBrowseEvent) {
        when (event) {
            is ProductBrowseEvent.ShowError -> {
                showError(event.message)
            }
            is ProductBrowseEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is ProductBrowseEvent.NavigateToDetail -> {
                val intent = Intent(this, ProductDetailActivity::class.java)
                intent.putExtra("product_id", event.productId)
                startActivity(intent)
            }
        }
    }

    private fun showFilterDialog() {
        // TODO: 显示筛选对话框
        showToast("筛选功能开发中")
    }

    private fun showSortDialog() {
        val sortOptions = arrayOf("默认排序", "价格从低到高", "价格从高到低", "销量排序", "评分排序", "最新上架")
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("排序方式")
            .setItems(sortOptions) { _, which ->
                val sortType = when (which) {
                    0 -> com.laundry.merchant.data.model.SortType.DEFAULT
                    1 -> com.laundry.merchant.data.model.SortType.PRICE_LOW
                    2 -> com.laundry.merchant.data.model.SortType.PRICE_HIGH
                    3 -> com.laundry.merchant.data.model.SortType.SALES
                    4 -> com.laundry.merchant.data.model.SortType.RATING
                    5 -> com.laundry.merchant.data.model.SortType.NEWEST
                    else -> com.laundry.merchant.data.model.SortType.DEFAULT
                }
                viewModel.setSortType(sortType)
            }
            .show()
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
