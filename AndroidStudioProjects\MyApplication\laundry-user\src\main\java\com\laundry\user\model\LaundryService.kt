package com.laundry.user.model

import java.util.Date

/**
 * 洗护服务
 */
data class LaundryService(
    val id: String,
    val name: String,
    val description: String,
    val basePrice: Double,
    val imageUrl: String,
    val category: ServiceCategory,
    val subCategory: String,
    val merchantId: String,
    val merchantName: String,
    val merchantAvatar: String,
    val rating: Double = 0.0,
    val reviewCount: Int = 0,
    val completedOrders: Int = 0,
    val isActive: Boolean = true,
    val isRecommended: Boolean = false,
    val tags: List<String> = emptyList(),
    val serviceArea: List<String> = emptyList(),
    val workingHours: WorkingHours,
    val priceRange: PriceRange,
    val features: List<String> = emptyList(),
    val estimatedTime: String = "1-2天",
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)

/**
 * 服务分类
 */
enum class ServiceCategory(val displayName: String, val icon: String) {
    CLOTHING("衣物洗护", "ic_clothing"),
    SHOES("鞋类清洗", "ic_shoes"),
    PET("萌宠洗护", "ic_pet"),
    BAGS("包包清洗", "ic_bags"),
    BEDDING("床品洗护", "ic_bedding"),
    CAR("汽车清洗", "ic_car"),
    HOME_CLEANING("上门清洗", "ic_home_cleaning"),
    PLUMBING("疏通服务", "ic_plumbing"),
    CARPET("地毯清洗", "ic_carpet"),
    CURTAIN("窗帘清洗", "ic_curtain"),
    SOFA("沙发清洗", "ic_sofa"),
    APPLIANCE("家电清洗", "ic_appliance")
}

/**
 * 营业时间
 */
data class WorkingHours(
    val monday: TimeSlot?,
    val tuesday: TimeSlot?,
    val wednesday: TimeSlot?,
    val thursday: TimeSlot?,
    val friday: TimeSlot?,
    val saturday: TimeSlot?,
    val sunday: TimeSlot?,
    val is24Hours: Boolean = false
)

data class TimeSlot(
    val startTime: String, // "09:00"
    val endTime: String    // "18:00"
)

/**
 * 价格区间
 */
data class PriceRange(
    val minPrice: Double,
    val maxPrice: Double,
    val unit: String = "件" // 件、套、次、小时等
)

/**
 * 服务详情
 */
data class ServiceDetail(
    val service: LaundryService,
    val detailDescription: String,
    val serviceProcess: List<String>,
    val includedItems: List<String>,
    val excludedItems: List<String>,
    val additionalServices: List<AdditionalService>,
    val faq: List<FAQ>,
    val gallery: List<String>
)

data class AdditionalService(
    val id: String,
    val name: String,
    val description: String,
    val price: Double,
    val isOptional: Boolean = true
)

data class FAQ(
    val question: String,
    val answer: String
)

/**
 * 搜索过滤条件
 */
data class ServiceFilter(
    val category: ServiceCategory? = null,
    val subCategory: String? = null,
    val priceRange: PriceRange? = null,
    val rating: Double? = null,
    val distance: Double? = null,
    val sortBy: SortType = SortType.COMPREHENSIVE,
    val tags: List<String> = emptyList(),
    val isRecommended: Boolean? = null,
    val merchantId: String? = null,
    val keyword: String? = null
)

enum class SortType(val displayName: String) {
    COMPREHENSIVE("综合排序"),
    PRICE_LOW_TO_HIGH("价格从低到高"),
    PRICE_HIGH_TO_LOW("价格从高到低"),
    RATING("评分最高"),
    DISTANCE("距离最近"),
    ORDERS("订单最多"),
    NEWEST("最新发布")
}

/**
 * 搜索结果
 */
data class ServiceSearchResult(
    val services: List<LaundryService>,
    val totalCount: Int,
    val hasMore: Boolean,
    val recommendations: List<LaundryService> = emptyList(),
    val popularCategories: List<ServiceCategory> = emptyList()
)
