package com.laundry.merchant.ui.profile

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.laundry.merchant.model.Merchant

class ProfileViewModel : ViewModel() {

    private val _merchantInfo = MutableLiveData<Merchant>()
    val merchantInfo: LiveData<Merchant> = _merchantInfo

    init {
        loadMerchantInfo()
    }

    private fun loadMerchantInfo() {
        // Mock data - in real app, this would come from repository/API
        val mockMerchant = Merchant(
            id = "MERCHANT001",
            name = "快洁洗衣店",
            phone = "***********",
            email = "<EMAIL>",
            businessLicense = "91110000123456789X",
            address = "北京市朝阳区某某街道123号",
            avatar = "",
            rating = 4.6f,
            totalOrders = 1250,
            isVerified = true,
            isActive = true
        )
        
        _merchantInfo.value = mockMerchant
    }

    fun logout() {
        // Handle logout logic
        // Clear merchant session, navigate to login screen, etc.
    }
}
