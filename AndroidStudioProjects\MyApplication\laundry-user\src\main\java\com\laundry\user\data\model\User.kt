package com.laundry.user.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 用户数据模型
 */
@Entity(tableName = "users")
data class User(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("phone")
    val phone: String = "",
    
    @SerializedName("nickname")
    val nickname: String = "",
    
    @SerializedName("avatar")
    val avatar: String = "",
    
    @SerializedName("gender")
    val gender: Int = 0, // 0: 未知, 1: 男, 2: 女
    
    @SerializedName("birthday")
    val birthday: String = "",
    
    @SerializedName("email")
    val email: String = "",
    
    @SerializedName("address")
    val address: String = "",
    
    @SerializedName("latitude")
    val latitude: Double = 0.0,
    
    @SerializedName("longitude")
    val longitude: Double = 0.0,
    
    @SerializedName("points")
    val points: Int = 0,
    
    @SerializedName("membership_level")
    val membershipLevel: Int = 0, // 0: 普通, 1: 银卡, 2: 金卡, 3: 钻石
    
    @SerializedName("is_verified")
    val isVerified: Boolean = false,
    
    @SerializedName("created_at")
    val createdAt: String = "",
    
    @SerializedName("updated_at")
    val updatedAt: String = ""
)

/**
 * 服务分类
 */
@Entity(tableName = "service_categories")
data class ServiceCategory(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("name")
    val name: String = "",
    
    @SerializedName("icon")
    val icon: String = "",
    
    @SerializedName("description")
    val description: String = "",
    
    @SerializedName("sort_order")
    val sortOrder: Int = 0,
    
    @SerializedName("is_active")
    val isActive: Boolean = true
)

/**
 * 商家信息
 */
@Entity(tableName = "merchants")
data class Merchant(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("name")
    val name: String = "",
    
    @SerializedName("avatar")
    val avatar: String = "",
    
    @SerializedName("phone")
    val phone: String = "",
    
    @SerializedName("address")
    val address: String = "",
    
    @SerializedName("latitude")
    val latitude: Double = 0.0,
    
    @SerializedName("longitude")
    val longitude: Double = 0.0,
    
    @SerializedName("rating")
    val rating: Float = 0f,
    
    @SerializedName("review_count")
    val reviewCount: Int = 0,
    
    @SerializedName("service_categories")
    val serviceCategories: List<String> = emptyList(),
    
    @SerializedName("business_hours")
    val businessHours: String = "",
    
    @SerializedName("is_verified")
    val isVerified: Boolean = false,
    
    @SerializedName("is_promoted")
    val isPromoted: Boolean = false, // 是否付费推广
    
    @SerializedName("promotion_level")
    val promotionLevel: Int = 0, // 推广等级
    
    @SerializedName("deposit_amount")
    val depositAmount: Double = 0.0, // 保证金
    
    @SerializedName("created_at")
    val createdAt: String = ""
)

/**
 * 服务项目
 */
@Entity(tableName = "services")
data class Service(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("merchant_id")
    val merchantId: String = "",
    
    @SerializedName("category_id")
    val categoryId: String = "",
    
    @SerializedName("name")
    val name: String = "",
    
    @SerializedName("description")
    val description: String = "",
    
    @SerializedName("price")
    val price: Double = 0.0,
    
    @SerializedName("unit")
    val unit: String = "", // 单位：件、双、套等
    
    @SerializedName("images")
    val images: List<String> = emptyList(),
    
    @SerializedName("estimated_time")
    val estimatedTime: Int = 0, // 预计完成时间（分钟）
    
    @SerializedName("is_available")
    val isAvailable: Boolean = true
)
