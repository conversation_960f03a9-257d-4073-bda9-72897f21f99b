package com.laundry.merchant.ui.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.laundry.merchant.databinding.FragmentMerchantProfileBinding

class ProfileFragment : Fragment() {

    private var _binding: FragmentMerchantProfileBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var profileViewModel: ProfileViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        profileViewModel = ViewModelProvider(this)[ProfileViewModel::class.java]

        _binding = FragmentMerchantProfileBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupViews()
        observeViewModel()

        return root
    }

    private fun setupViews() {
        binding.apply {
            // Service management
            layoutServiceManagement.setOnClickListener {
                // Navigate to service management
            }
            
            // Business info
            layoutBusinessInfo.setOnClickListener {
                // Navigate to business info
            }
            
            // Financial records
            layoutFinancialRecords.setOnClickListener {
                // Navigate to financial records
            }
            
            // Settings
            layoutSettings.setOnClickListener {
                // Navigate to settings
            }
            
            // Help & Support
            layoutHelpSupport.setOnClickListener {
                // Navigate to help & support
            }
            
            // About
            layoutAbout.setOnClickListener {
                // Navigate to about
            }
            
            // Logout
            buttonLogout.setOnClickListener {
                // Handle logout
                profileViewModel.logout()
            }
        }
    }

    private fun observeViewModel() {
        profileViewModel.merchantInfo.observe(viewLifecycleOwner) { merchant ->
            binding.apply {
                textMerchantName.text = merchant.name
                textMerchantPhone.text = merchant.phone
                textMerchantEmail.text = merchant.email
                textMerchantAddress.text = merchant.address
                textMerchantRating.text = String.format("%.1f", merchant.rating)
                textTotalOrders.text = "${merchant.totalOrders}单"
                
                // Show verification status
                if (merchant.isVerified) {
                    textVerificationStatus.text = "已认证"
                    textVerificationStatus.setTextColor(
                        resources.getColor(android.R.color.holo_green_dark, null)
                    )
                } else {
                    textVerificationStatus.text = "未认证"
                    textVerificationStatus.setTextColor(
                        resources.getColor(android.R.color.holo_red_dark, null)
                    )
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
