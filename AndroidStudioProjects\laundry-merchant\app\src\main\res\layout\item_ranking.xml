<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 排名显示 -->
        <FrameLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical">

            <TextView
                android:id="@+id/textViewRank"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/gray_200"
                android:gravity="center"
                android:text="4"
                android:textColor="@color/gray_600"
                android:textSize="16sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/imageViewRankBadge"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_rank_1"
                android:visibility="gone" />

        </FrameLayout>

        <!-- 商家头像 -->
        <ImageView
            android:id="@+id/imageViewAvatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="12dp"
            android:background="@drawable/circle_background"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_merchant_placeholder" />

        <!-- 商家信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="金牌洗衣"
                    android:textColor="@color/gray_800"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewRankChange"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="↑1"
                    android:textColor="@color/green_500"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <TextView
                android:id="@+id/textViewArea"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:text="朝阳区"
                android:textColor="@color/gray_500"
                android:textSize="12sp" />

            <!-- 数据展示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textViewOrderCount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="320单"
                    android:textColor="@color/blue_500"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewRevenue"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="¥18500"
                    android:textColor="@color/green_500"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewRating"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="4.9分"
                    android:textColor="@color/orange_500"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/textViewResponseTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="8分钟"
                    android:textColor="@color/purple_500"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <!-- 综合评分 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewScore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="95.8"
                android:textColor="@color/red_500"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="分"
                android:textColor="@color/gray_500"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- 自己的标识 -->
        <View
            android:id="@+id/viewMyselfIndicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:background="@color/colorPrimary"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
