package com.laundry.merchant.ui.product.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.data.model.ProductCategory
import com.laundry.merchant.databinding.ItemProductCategoryBinding

class ProductCategoryAdapter(
    private val onCategoryClick: (ProductCategory) -> Unit
) : ListAdapter<ProductCategory, ProductCategoryAdapter.ViewHolder>(CategoryDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemProductCategoryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<ProductCategory>) {
        submitList(newData)
    }

    inner class ViewHolder(private val binding: ItemProductCategoryBinding) : 
        RecyclerView.ViewHolder(binding.root) {

        fun bind(category: ProductCategory) {
            binding.textViewCategoryName.text = category.name
            binding.textViewProductCount.text = "${category.productCount}个商品"

            // TODO: 加载分类图标
            // Glide.with(binding.imageViewCategoryIcon.context)
            //     .load(category.iconUrl)
            //     .placeholder(R.drawable.placeholder_category)
            //     .into(binding.imageViewCategoryIcon)

            // 设置点击事件
            binding.root.setOnClickListener {
                onCategoryClick(category)
            }
        }
    }

    private class CategoryDiffCallback : DiffUtil.ItemCallback<ProductCategory>() {
        override fun areItemsTheSame(oldItem: ProductCategory, newItem: ProductCategory): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: ProductCategory, newItem: ProductCategory): Boolean {
            return oldItem == newItem
        }
    }
}
