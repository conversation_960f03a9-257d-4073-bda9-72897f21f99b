package com.laundry.merchant.ui.cart

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.laundry.merchant.databinding.ActivityCartBinding
import com.laundry.merchant.databinding.BottomSheetCouponsBinding
import com.laundry.merchant.ui.cart.adapter.CartItemAdapter
import com.laundry.merchant.ui.cart.adapter.CouponAdapter
import com.laundry.merchant.ui.checkout.CheckoutActivity
import com.laundry.merchant.utils.formatCurrency
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class CartActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCartBinding
    private val viewModel: CartViewModel by viewModels()
    
    private lateinit var cartItemAdapter: CartItemAdapter
    private var couponsBottomSheet: BottomSheetDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCartBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerView()
        observeViewModel()
        
        // 加载购物车数据
        viewModel.loadCart()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "购物车"

        // 设置优惠券选择
        binding.layoutCoupons.setOnClickListener {
            showCouponsBottomSheet()
        }

        // 设置积分使用
        binding.layoutPoints.setOnClickListener {
            showPointsDialog()
        }

        // 设置结算按钮
        binding.buttonCheckout.setOnClickListener {
            proceedToCheckout()
        }

        // 设置清空购物车
        binding.buttonClearCart.setOnClickListener {
            showClearCartDialog()
        }

        // 设置全选
        binding.checkboxSelectAll.setOnCheckedChangeListener { _, isChecked ->
            viewModel.selectAllItems(isChecked)
        }
    }

    private fun setupRecyclerView() {
        cartItemAdapter = CartItemAdapter(
            onQuantityChanged = { item, quantity ->
                viewModel.updateItemQuantity(item.id, quantity)
            },
            onItemRemoved = { item ->
                viewModel.removeItem(item.id)
            },
            onItemSelected = { item, isSelected ->
                viewModel.selectItem(item.id, isSelected)
            }
        )
        
        binding.recyclerViewCartItems.apply {
            layoutManager = LinearLayoutManager(this@CartActivity)
            adapter = cartItemAdapter
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: CartUiState) {
        // 更新加载状态
        binding.progressBar.visibility = if (state.isLoading) View.VISIBLE else View.GONE

        // 更新购物车商品
        cartItemAdapter.updateData(state.cartItems)

        // 更新空状态
        if (state.cartItems.isEmpty() && !state.isLoading) {
            binding.layoutEmpty.visibility = View.VISIBLE
            binding.layoutCart.visibility = View.GONE
            binding.layoutSummary.visibility = View.GONE
        } else {
            binding.layoutEmpty.visibility = View.GONE
            binding.layoutCart.visibility = View.VISIBLE
            binding.layoutSummary.visibility = View.VISIBLE
        }

        // 更新全选状态
        binding.checkboxSelectAll.isChecked = state.isAllSelected

        // 更新价格汇总
        state.cartSummary?.let { summary ->
            binding.textViewSubtotal.text = summary.subtotal.formatCurrency()
            binding.textViewOriginalSubtotal.text = summary.originalSubtotal.formatCurrency()
            binding.textViewTotalSavings.text = "-${summary.totalSavings.formatCurrency()}"
            binding.textViewCouponDiscount.text = "-${summary.couponDiscount.formatCurrency()}"
            binding.textViewPointsDiscount.text = "-${summary.pointsDiscount.formatCurrency()}"
            binding.textViewDeliveryFee.text = summary.deliveryFee.formatCurrency()
            binding.textViewFinalTotal.text = summary.finalTotal.formatCurrency()
            binding.textViewTotalItems.text = "${summary.totalItems}件商品"
            binding.textViewEstimatedDuration.text = "预计${summary.estimatedDuration}分钟"

            // 显示/隐藏折扣行
            binding.layoutTotalSavings.visibility = if (summary.totalSavings > 0) View.VISIBLE else View.GONE
            binding.layoutCouponDiscount.visibility = if (summary.couponDiscount > 0) View.VISIBLE else View.GONE
            binding.layoutPointsDiscount.visibility = if (summary.pointsDiscount > 0) View.VISIBLE else View.GONE
            binding.layoutDeliveryFee.visibility = if (summary.deliveryFee > 0) View.VISIBLE else View.GONE
        }

        // 更新优惠券信息
        binding.textViewCouponInfo.text = if (state.appliedCoupons.isNotEmpty()) {
            "已使用${state.appliedCoupons.size}张优惠券"
        } else {
            "选择优惠券"
        }

        // 更新积分信息
        binding.textViewPointsInfo.text = if (state.usedPoints > 0) {
            "已使用${state.usedPoints}积分"
        } else {
            "使用积分"
        }

        // 更新结算按钮
        binding.buttonCheckout.isEnabled = state.cartItems.isNotEmpty() && 
                                          !state.hasUnavailableItems && 
                                          !state.isLoading

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: CartEvent) {
        when (event) {
            is CartEvent.ShowError -> {
                showError(event.message)
            }
            is CartEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is CartEvent.NavigateToCheckout -> {
                val intent = Intent(this, CheckoutActivity::class.java)
                startActivity(intent)
            }
            is CartEvent.ItemUpdated -> {
                showToast("商品已更新")
            }
            is CartEvent.ItemRemoved -> {
                showToast("商品已移除")
            }
            is CartEvent.CouponApplied -> {
                showToast("优惠券已应用")
                couponsBottomSheet?.dismiss()
            }
            is CartEvent.PointsUsed -> {
                showToast("积分已使用")
            }
        }
    }

    private fun showCouponsBottomSheet() {
        val bottomSheetBinding = BottomSheetCouponsBinding.inflate(layoutInflater)
        couponsBottomSheet = BottomSheetDialog(this)
        couponsBottomSheet?.setContentView(bottomSheetBinding.root)

        val couponAdapter = CouponAdapter { coupon ->
            viewModel.applyCoupon(coupon.id)
        }

        bottomSheetBinding.recyclerViewCoupons.apply {
            layoutManager = LinearLayoutManager(this@CartActivity)
            adapter = couponAdapter
        }

        // 加载可用优惠券
        viewModel.loadAvailableCoupons()
        
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                couponAdapter.updateData(state.availableCoupons)
            }
        }

        couponsBottomSheet?.show()
    }

    private fun showPointsDialog() {
        val currentState = viewModel.uiState.value
        val maxUsablePoints = currentState.cartSummary?.availablePoints ?: 0
        val maxPointsValue = (currentState.cartSummary?.subtotal ?: 0.0) * 100

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("使用积分")
            .setMessage("可用积分: $maxUsablePoints\n最多可抵扣: ${maxPointsValue.toInt()}积分")
            .setView(android.widget.EditText(this).apply {
                hint = "输入要使用的积分"
                inputType = android.text.InputType.TYPE_CLASS_NUMBER
            })
            .setPositiveButton("确定") { dialog, _ ->
                val editText = (dialog as androidx.appcompat.app.AlertDialog).findViewById<android.widget.EditText>(android.R.id.edit)
                val points = editText?.text.toString().toIntOrNull() ?: 0
                if (points > 0) {
                    viewModel.usePoints(points)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showClearCartDialog() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("清空购物车")
            .setMessage("确定要清空购物车吗？")
            .setPositiveButton("确定") { _, _ ->
                viewModel.clearCart()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun proceedToCheckout() {
        val selectedItems = viewModel.uiState.value.cartItems.filter { it.isSelected }
        if (selectedItems.isEmpty()) {
            showToast("请选择要结算的商品")
            return
        }

        if (selectedItems.any { !it.isAvailable || it.isOutOfStock() }) {
            showToast("购物车中有不可用商品，请先处理")
            return
        }

        val intent = Intent(this, CheckoutActivity::class.java)
        startActivity(intent)
    }

    private fun showError(message: String) {
        showToast(message)
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    override fun onDestroy() {
        super.onDestroy()
        couponsBottomSheet?.dismiss()
    }
}
