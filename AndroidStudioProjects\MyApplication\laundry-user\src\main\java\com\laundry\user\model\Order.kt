package com.laundry.user.model

import java.util.Date

data class Order(
    val id: String,
    val userId: String,
    val serviceId: String,
    val serviceName: String,
    val merchantId: String,
    val merchantName: String = "",
    val status: OrderStatus,
    val quantity: Int = 1,
    val originalPrice: Double,
    val finalPrice: Double,
    val createdAt: Date,
    val updatedAt: Date,
    val scheduledTime: Date,
    val completedTime: Date? = null,
    val pickupAddress: UserAddress,
    val deliveryAddress: UserAddress,
    val specialInstructions: String = "",
    val couponId: String? = null,
    val paymentMethod: String? = null, // 简化为字符串
    val trackingInfo: String? = null, // 简化为字符串
    val items: List<OrderItem> = emptyList(),
    val timeline: List<String> = emptyList() // 简化为字符串列表
)

enum class OrderStatus(val displayName: String, val description: String) {
    PENDING("待确认", "等待商家确认"),
    CONFIRMED("已确认", "商家已确认，准备取件"),
    PICKED_UP("已取件", "已上门取件"),
    PROCESSING("处理中", "正在清洗处理"),
    QUALITY_CHECK("质检中", "质量检查中"),
    READY("待配送", "处理完成，准备配送"),
    DELIVERING("配送中", "正在配送中"),
    COMPLETED("已完成", "订单已完成"),
    CANCELLED("已取消", "订单已取消"),
    REFUNDED("已退款", "订单已退款")
}

/**
 * 订单流程状态
 */
data class OrderFlowState(
    val step: OrderStep,
    val service: LaundryService,
    val merchantId: String,
    val orderDraft: OrderDraft,
    val availableTimeSlots: List<TimeSlot>,
    val availableAddresses: List<UserAddress>,
    val availableCoupons: List<String>, // 简化为字符串列表
    val estimatedPrice: Double,
    val finalPrice: Double = estimatedPrice,
    val validationErrors: List<String> = emptyList()
)

enum class OrderStep(val stepNumber: Int, val title: String) {
    SERVICE_SELECTION(1, "选择服务"),
    QUANTITY_SELECTION(2, "选择数量"),
    TIME_SELECTION(3, "选择时间"),
    ADDRESS_SELECTION(4, "选择地址"),
    COUPON_SELECTION(5, "选择优惠券"),
    PAYMENT_SELECTION(6, "选择支付方式"),
    CONFIRMATION(7, "确认订单")
}

/**
 * 订单草稿
 */
data class OrderDraft(
    val quantity: Int = 1,
    val items: List<OrderItem> = emptyList(),
    val scheduledTime: Date? = null,
    val pickupAddress: UserAddress? = null,
    val deliveryAddress: UserAddress? = null,
    val specialInstructions: String = "",
    val selectedCoupon: String? = null, // 简化为字符串
    val paymentMethod: String? = null // 简化为字符串
)

/**
 * 订单项目
 */
data class OrderItem(
    val id: String,
    val name: String,
    val description: String,
    val quantity: Int,
    val unitPrice: Double,
    val totalPrice: Double,
    val category: String,
    val specifications: Map<String, String> = emptyMap(),
    val imageUrl: String? = null
)

/**
 * 订单流程动作
 */
sealed class OrderFlowAction {
    data class SelectQuantity(val quantity: Int, val items: List<OrderItem>) : OrderFlowAction()
    data class SelectTimeSlot(val timeSlot: Date) : OrderFlowAction()
    data class SelectAddress(val address: UserAddress, val deliveryAddress: UserAddress? = null) : OrderFlowAction()
    data class SelectCoupon(val coupon: String?) : OrderFlowAction() // 简化为字符串
    data class SelectPaymentMethod(val paymentMethod: String) : OrderFlowAction() // 简化为字符串
    data class AddSpecialInstructions(val instructions: String) : OrderFlowAction()
    object NextStep : OrderFlowAction()
    object PreviousStep : OrderFlowAction()
}

/**
 * 用户地址
 */
data class UserAddress(
    val id: String,
    val name: String,
    val phone: String,
    val address: String,
    val detailAddress: String,
    val latitude: Double,
    val longitude: Double,
    val isDefault: Boolean = false,
    val tag: String = "", // 家、公司等标签
    val createdAt: Date = Date()
)

// 保留原有的Address类以兼容
data class Address(
    val id: String = "",
    val name: String,
    val phone: String,
    val address: String,
    val detailAddress: String,
    val isDefault: Boolean = false
)
