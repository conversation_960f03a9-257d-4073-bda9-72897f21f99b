package com.laundry.user.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 订单状态枚举
 */
enum class OrderStatus(val value: Int, val displayName: String) {
    PENDING(0, "待接单"),
    ACCEPTED(1, "已接单"),
    PICKED_UP(2, "已取件"),
    PROCESSING(3, "处理中"),
    COMPLETED(4, "已完成"),
    DELIVERED(5, "已送达"),
    CANCELLED(6, "已取消"),
    REFUNDED(7, "已退款")
}

/**
 * 支付状态枚举
 */
enum class PaymentStatus(val value: Int, val displayName: String) {
    UNPAID(0, "未支付"),
    PAID(1, "已支付"),
    REFUNDING(2, "退款中"),
    REFUNDED(3, "已退款"),
    FAILED(4, "支付失败")
}

/**
 * 订单数据模型
 */
@Entity(tableName = "orders")
data class Order(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("order_no")
    val orderNo: String = "",
    
    @SerializedName("user_id")
    val userId: String = "",
    
    @SerializedName("merchant_id")
    val merchantId: String = "",
    
    @SerializedName("merchant_name")
    val merchantName: String = "",
    
    @SerializedName("merchant_phone")
    val merchantPhone: String = "",
    
    @SerializedName("service_items")
    val serviceItems: List<OrderItem> = emptyList(),
    
    @SerializedName("total_amount")
    val totalAmount: Double = 0.0,
    
    @SerializedName("discount_amount")
    val discountAmount: Double = 0.0,
    
    @SerializedName("final_amount")
    val finalAmount: Double = 0.0,
    
    @SerializedName("pickup_address")
    val pickupAddress: String = "",
    
    @SerializedName("pickup_latitude")
    val pickupLatitude: Double = 0.0,
    
    @SerializedName("pickup_longitude")
    val pickupLongitude: Double = 0.0,
    
    @SerializedName("delivery_address")
    val deliveryAddress: String = "",
    
    @SerializedName("delivery_latitude")
    val deliveryLatitude: Double = 0.0,
    
    @SerializedName("delivery_longitude")
    val deliveryLongitude: Double = 0.0,
    
    @SerializedName("pickup_time")
    val pickupTime: String = "",
    
    @SerializedName("delivery_time")
    val deliveryTime: String = "",
    
    @SerializedName("estimated_completion_time")
    val estimatedCompletionTime: String = "",
    
    @SerializedName("status")
    val status: Int = OrderStatus.PENDING.value,
    
    @SerializedName("payment_status")
    val paymentStatus: Int = PaymentStatus.UNPAID.value,
    
    @SerializedName("payment_method")
    val paymentMethod: String = "", // alipay, wechat, cash
    
    @SerializedName("notes")
    val notes: String = "",
    
    @SerializedName("special_requirements")
    val specialRequirements: String = "",
    
    @SerializedName("coupon_id")
    val couponId: String = "",
    
    @SerializedName("points_used")
    val pointsUsed: Int = 0,
    
    @SerializedName("points_earned")
    val pointsEarned: Int = 0,
    
    @SerializedName("created_at")
    val createdAt: String = "",
    
    @SerializedName("updated_at")
    val updatedAt: String = ""
)

/**
 * 订单项目
 */
data class OrderItem(
    @SerializedName("service_id")
    val serviceId: String = "",
    
    @SerializedName("service_name")
    val serviceName: String = "",
    
    @SerializedName("quantity")
    val quantity: Int = 1,
    
    @SerializedName("unit_price")
    val unitPrice: Double = 0.0,
    
    @SerializedName("total_price")
    val totalPrice: Double = 0.0,
    
    @SerializedName("notes")
    val notes: String = ""
)

/**
 * 订单评价
 */
@Entity(tableName = "reviews")
data class Review(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("order_id")
    val orderId: String = "",
    
    @SerializedName("user_id")
    val userId: String = "",
    
    @SerializedName("merchant_id")
    val merchantId: String = "",
    
    @SerializedName("rating")
    val rating: Float = 0f, // 1-5星
    
    @SerializedName("service_rating")
    val serviceRating: Float = 0f, // 服务评分
    
    @SerializedName("quality_rating")
    val qualityRating: Float = 0f, // 质量评分
    
    @SerializedName("speed_rating")
    val speedRating: Float = 0f, // 速度评分
    
    @SerializedName("comment")
    val comment: String = "",
    
    @SerializedName("images")
    val images: List<String> = emptyList(),
    
    @SerializedName("is_anonymous")
    val isAnonymous: Boolean = false,
    
    @SerializedName("created_at")
    val createdAt: String = ""
)

/**
 * 优惠券
 */
@Entity(tableName = "coupons")
data class Coupon(
    @PrimaryKey
    @SerializedName("id")
    val id: String = "",
    
    @SerializedName("title")
    val title: String = "",
    
    @SerializedName("description")
    val description: String = "",
    
    @SerializedName("type")
    val type: Int = 0, // 0: 满减, 1: 折扣, 2: 免费服务
    
    @SerializedName("discount_amount")
    val discountAmount: Double = 0.0,
    
    @SerializedName("discount_rate")
    val discountRate: Float = 0f, // 折扣率 0.1-1.0
    
    @SerializedName("min_amount")
    val minAmount: Double = 0.0, // 最低消费金额
    
    @SerializedName("max_discount")
    val maxDiscount: Double = 0.0, // 最大优惠金额
    
    @SerializedName("valid_from")
    val validFrom: String = "",
    
    @SerializedName("valid_until")
    val validUntil: String = "",
    
    @SerializedName("usage_limit")
    val usageLimit: Int = 1, // 使用次数限制
    
    @SerializedName("used_count")
    val usedCount: Int = 0,
    
    @SerializedName("is_used")
    val isUsed: Boolean = false,
    
    @SerializedName("applicable_categories")
    val applicableCategories: List<String> = emptyList(),
    
    @SerializedName("applicable_merchants")
    val applicableMerchants: List<String> = emptyList()
)
