<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗护帮 - 管理端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .phone-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }
        
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #FF9800, #F57C00);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .app-header {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 20px 20px 100px;
        }
        
        .admin-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .panel-header {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .admin-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .admin-metric {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 10px;
            text-align: center;
        }
        
        .metric-number {
            font-weight: bold;
            color: #FF9800;
            font-size: 16px;
            margin-bottom: 4px;
        }
        
        .metric-text {
            font-size: 10px;
            color: #666;
        }
        
        .control-section {
            margin-bottom: 15px;
        }
        
        .control-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
        }
        
        .control-label {
            font-size: 14px;
            color: #333;
        }
        
        .control-input {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 4px 8px;
            width: 60px;
            font-size: 12px;
        }
        
        .range-input {
            width: 80px;
        }
        
        .btn {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 12px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .merchant-list {
            margin-top: 15px;
        }
        
        .merchant-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .merchant-item:last-child {
            border-bottom: none;
        }
        
        .merchant-info {
            flex: 1;
        }
        
        .merchant-name {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 2px;
        }
        
        .merchant-details {
            font-size: 10px;
            color: #666;
        }
        
        .merchant-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .status-active {
            background: #e8f5e8;
            color: #4caf50;
        }
        
        .status-pending {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .status-suspended {
            background: #ffeaea;
            color: #f44336;
        }
        
        .alert-item {
            background: #fff3e0;
            border-left: 4px solid #FF9800;
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 0 8px 8px 0;
        }
        
        .alert-title {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 4px;
        }
        
        .alert-content {
            font-size: 10px;
            color: #666;
        }
        
        .alert-time {
            font-size: 9px;
            color: #999;
            margin-top: 4px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 80px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-radius: 0 0 32px 32px;
        }
        
        .nav-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            padding: 8px;
        }
        
        .nav-item.active {
            color: #FF9800;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .real-time-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background: #e8f5e8;
            color: #4caf50;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .pulse {
            width: 6px;
            height: 6px;
            background: #4caf50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .keyword-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        
        .keyword-text {
            font-size: 12px;
            font-weight: 500;
        }
        
        .keyword-price {
            font-size: 12px;
            color: #FF9800;
            font-weight: bold;
        }
        
        .keyword-status {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: bold;
        }
        
        .status-approved {
            background: #e8f5e8;
            color: #4caf50;
        }
        
        .status-review {
            background: #fff3e0;
            color: #f57c00;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>⚙️ 平台管理</span>
                    <span>🔋100%</span>
                </div>
                
                <div class="app-header">
                    <div class="app-title">平台管理中心</div>
                    <div class="app-subtitle">投流控制 · 数据监控</div>
                </div>
                
                <div class="content-area" id="contentArea">
                    <!-- 首页内容 -->
                    <div id="homeContent">
                        <div class="real-time-indicator">
                            <div class="pulse"></div>
                            实时数据监控中
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">📈 投流数据概览</div>
                            <div class="admin-metrics">
                                <div class="admin-metric">
                                    <div class="metric-number">¥125,680</div>
                                    <div class="metric-text">总投入</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">¥25,136</div>
                                    <div class="metric-text">平台收入</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">1,245</div>
                                    <div class="metric-text">活跃商家</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">¥1.85</div>
                                    <div class="metric-text">平均CPC</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">⚙️ 算法配置</div>
                            <div class="control-section">
                                <div class="control-row">
                                    <span class="control-label">投流权重:</span>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <input type="range" min="0" max="100" value="30" class="range-input">
                                        <span style="font-size: 12px; color: #FF9800; font-weight: bold;">30%</span>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">质量权重:</span>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <input type="range" min="0" max="100" value="40" class="range-input">
                                        <span style="font-size: 12px; color: #FF9800; font-weight: bold;">40%</span>
                                    </div>
                                </div>
                                <div class="control-row">
                                    <span class="control-label">相关性权重:</span>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <input type="range" min="0" max="100" value="30" class="range-input">
                                        <span style="font-size: 12px; color: #FF9800; font-weight: bold;">30%</span>
                                    </div>
                                </div>
                                <button class="btn" style="width: 100%; margin-top: 10px;">应用配置</button>
                            </div>
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">⚠️ 系统告警</div>
                            <div class="alert-item">
                                <div class="alert-title">异常竞价检测</div>
                                <div class="alert-content">检测到商家"快速洗护"出价异常波动</div>
                                <div class="alert-time">2分钟前</div>
                            </div>
                            <div class="alert-item">
                                <div class="alert-title">高频点击预警</div>
                                <div class="alert-content">IP ************* 短时间内大量点击</div>
                                <div class="alert-time">5分钟前</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商家管理内容 -->
                    <div id="merchantContent" style="display: none;">
                        <div class="section-title">
                            🏪 商家管理
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">商家审核</div>
                            <div class="merchant-list">
                                <div class="merchant-item">
                                    <div class="merchant-info">
                                        <div class="merchant-name">新申请洗衣店</div>
                                        <div class="merchant-details">申请时间: 2小时前</div>
                                    </div>
                                    <div class="merchant-status status-pending">待审核</div>
                                </div>
                                <div class="merchant-item">
                                    <div class="merchant-info">
                                        <div class="merchant-name">优质洗衣店</div>
                                        <div class="merchant-details">投流金额: ¥8,560</div>
                                    </div>
                                    <div class="merchant-status status-active">正常</div>
                                </div>
                                <div class="merchant-item">
                                    <div class="merchant-info">
                                        <div class="merchant-name">问题商家</div>
                                        <div class="merchant-details">违规次数: 3次</div>
                                    </div>
                                    <div class="merchant-status status-suspended">已暂停</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">投流监控</div>
                            <div class="admin-metrics">
                                <div class="admin-metric">
                                    <div class="metric-number">156</div>
                                    <div class="metric-text">活跃投流</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">23</div>
                                    <div class="metric-text">暂停投流</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">¥2,340</div>
                                    <div class="metric-text">今日消费</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">12.5%</div>
                                    <div class="metric-text">平均转化</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 关键词管理内容 -->
                    <div id="keywordContent" style="display: none;">
                        <div class="section-title">
                            🔍 关键词管理
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">热门关键词</div>
                            <div class="keyword-item">
                                <div>
                                    <div class="keyword-text">衣物洗护</div>
                                    <div class="keyword-status status-approved">已审核</div>
                                </div>
                                <div class="keyword-price">¥2.50</div>
                            </div>
                            <div class="keyword-item">
                                <div>
                                    <div class="keyword-text">鞋类清洗</div>
                                    <div class="keyword-status status-approved">已审核</div>
                                </div>
                                <div class="keyword-price">¥1.80</div>
                            </div>
                            <div class="keyword-item">
                                <div>
                                    <div class="keyword-text">萌宠洗护</div>
                                    <div class="keyword-status status-review">审核中</div>
                                </div>
                                <div class="keyword-price">¥3.20</div>
                            </div>
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">竞价统计</div>
                            <div class="admin-metrics">
                                <div class="admin-metric">
                                    <div class="metric-number">2,856</div>
                                    <div class="metric-text">总关键词</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">1,234</div>
                                    <div class="metric-text">竞价关键词</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">23</div>
                                    <div class="metric-text">待审核</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">¥2.15</div>
                                    <div class="metric-text">平均竞价</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 违规监控内容 -->
                    <div id="violationContent" style="display: none;">
                        <div class="section-title">
                            🛡️ 违规监控
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">今日监控</div>
                            <div class="admin-metrics">
                                <div class="admin-metric">
                                    <div class="metric-number">5,680</div>
                                    <div class="metric-text">检测次数</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">3</div>
                                    <div class="metric-text">发现违规</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">2</div>
                                    <div class="metric-text">自动处理</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">1</div>
                                    <div class="metric-text">人工审核</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="admin-panel">
                            <div class="panel-header">违规记录</div>
                            <div class="alert-item">
                                <div class="alert-title">恶意点击</div>
                                <div class="alert-content">商家"问题商家"存在刷点击行为</div>
                                <div class="alert-time">已处理 - 1小时前</div>
                            </div>
                            <div class="alert-item">
                                <div class="alert-title">虚假信息</div>
                                <div class="alert-content">服务描述与实际不符</div>
                                <div class="alert-time">处理中 - 3小时前</div>
                            </div>
                            <div class="alert-item">
                                <div class="alert-title">价格异常</div>
                                <div class="alert-content">出价远超市场平均水平</div>
                                <div class="alert-time">已警告 - 昨天</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="switchContent('home')">
                        <div class="nav-icon">🏠</div>
                        <div class="nav-label">首页</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('merchant')">
                        <div class="nav-icon">🏪</div>
                        <div class="nav-label">商家</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('keyword')">
                        <div class="nav-icon">🔍</div>
                        <div class="nav-label">关键词</div>
                    </div>
                    <div class="nav-item" onclick="switchContent('violation')">
                        <div class="nav-icon">🛡️</div>
                        <div class="nav-label">监控</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchContent(content) {
            // 隐藏所有内容
            document.querySelectorAll('#contentArea > div').forEach(div => {
                div.style.display = 'none';
            });
            
            // 移除所有导航项的active状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示对应内容
            document.getElementById(content + 'Content').style.display = 'block';
            
            // 激活对应导航项
            event.target.closest('.nav-item').classList.add('active');
        }
        
        // 模拟实时数据更新
        function updateRealTimeData() {
            // 更新总投入
            const totalInvestment = document.querySelector('.metric-number');
            if (totalInvestment && totalInvestment.textContent.includes('¥125,680')) {
                const currentValue = 125680;
                const change = Math.floor(Math.random() * 1000);
                const newValue = currentValue + change;
                totalInvestment.textContent = `¥${newValue.toLocaleString()}`;
            }
            
            // 更新活跃商家数
            const activeMerchants = document.querySelectorAll('.metric-number')[2];
            if (activeMerchants && activeMerchants.textContent === '1,245') {
                const currentCount = 1245;
                const change = Math.floor(Math.random() * 10) - 5;
                const newCount = Math.max(0, currentCount + change);
                activeMerchants.textContent = newCount.toLocaleString();
            }
            
            // 更新检测次数
            const detectionCount = document.querySelector('.admin-metric .metric-number');
            if (detectionCount && detectionCount.textContent === '5,680') {
                const currentCount = 5680;
                const change = Math.floor(Math.random() * 50);
                const newCount = currentCount + change;
                detectionCount.textContent = newCount.toLocaleString();
            }
        }
        
        // 每5秒更新一次数据
        setInterval(updateRealTimeData, 5000);
        
        // 算法配置滑块事件
        document.querySelectorAll('.range-input').forEach(slider => {
            slider.addEventListener('input', function() {
                const value = this.value;
                const label = this.nextElementSibling;
                label.textContent = value + '%';
            });
        });
        
        // 应用配置按钮事件
        document.querySelector('.btn').addEventListener('click', function() {
            alert('算法配置已更新');
        });
        
        // 商家状态点击事件
        document.querySelectorAll('.merchant-status').forEach(status => {
            status.addEventListener('click', function() {
                if (this.textContent === '待审核') {
                    if (confirm('是否通过审核？')) {
                        this.textContent = '正常';
                        this.className = 'merchant-status status-active';
                    }
                } else if (this.textContent === '已暂停') {
                    if (confirm('是否恢复商家？')) {
                        this.textContent = '正常';
                        this.className = 'merchant-status status-active';
                    }
                }
            });
        });
    </script>
</body>
</html>
