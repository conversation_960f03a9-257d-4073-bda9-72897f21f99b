@echo off
chcp 65001 >nul
echo.
echo ================================
echo    Laundry User App Launcher
echo ================================
echo.

echo [1/4] Checking Java environment...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java not found
    echo Please install JDK 11 or 17 from https://adoptium.net/
    pause
    exit /b 1
) else (
    echo ✅ Java environment OK
)

echo [2/4] Checking project structure...
if exist "MyApplication\gradlew.bat" (
    echo ✅ Found Kotlin Multiplatform project
    set "PROJECT_DIR=MyApplication"
    set "BUILD_TASK=:app:assembleDebug"
) else if exist "laundry-user\gradlew.bat" (
    echo ✅ Found Android project
    set "PROJECT_DIR=laundry-user"
    set "BUILD_TASK=assembleDebug"
) else (
    echo ❌ No valid Android project found
    pause
    exit /b 1
)

echo [3/4] Checking Android SDK...
if not defined ANDROID_HOME (
    echo ⚠️  ANDROID_HOME not set
    echo Please set ANDROID_HOME environment variable
) else (
    echo ✅ Android SDK: %ANDROID_HOME%
)

echo [4/4] Launch options...
echo.
echo ================================
echo    🚀 How to start the app
echo ================================
echo.
echo Option 1: Android Studio (Recommended)
echo   1. Open Android Studio
echo   2. Import project: %PROJECT_DIR%
echo   3. Wait for Gradle sync
echo   4. Click Run button
echo.
echo Option 2: Command Line
echo   1. cd %PROJECT_DIR%
echo   2. gradlew %BUILD_TASK%
echo   3. gradlew installDebug
echo.
echo Option 3: VSCode
echo   1. Install Java and Android extensions
echo   2. Open project folder
echo   3. Press F5 to debug
echo.
echo ================================

echo.
echo Try to build the project? (y/n)
set /p build_choice=
if /i "%build_choice%"=="y" (
    echo.
    echo Building project...
    cd "%PROJECT_DIR%"
    call gradlew.bat %BUILD_TASK%
    
    if %errorlevel% equ 0 (
        echo ✅ Build successful
        echo APK location: app\build\outputs\apk\debug\app-debug.apk
    ) else (
        echo ❌ Build failed
        echo Please check the error messages above
    )
    cd ..
)

echo.
echo ================================
echo    Setup complete!
echo ================================
pause
