package com.laundry.merchant.data.repository

import com.laundry.merchant.data.local.PreferencesManager
import com.laundry.merchant.data.model.MerchantProfile
import com.laundry.merchant.network.ApiService
import com.laundry.merchant.network.LoginRequest
import com.laundry.merchant.network.NetworkResult
import com.laundry.merchant.network.RegisterRequest
import com.laundry.merchant.network.ResetPasswordRequest
import com.laundry.merchant.network.SendCodeRequest
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

interface AuthRepository {
    suspend fun login(phone: String, password: String): NetworkResult<Unit>
    suspend fun register(phone: String, password: String, verificationCode: String, merchantName: String): NetworkResult<Unit>
    suspend fun sendVerificationCode(phone: String): NetworkResult<Unit>
    suspend fun resetPassword(phone: String, verificationCode: String, newPassword: String): NetworkResult<Unit>
    suspend fun quickLogin(phone: String, verificationCode: String): NetworkResult<Unit>
    suspend fun logout()
    fun isLoggedIn(): Flow<Boolean>
    suspend fun refreshToken(): NetworkResult<Unit>
}

@Singleton
class AuthRepositoryImpl @Inject constructor(
    private val apiService: ApiService,
    private val preferencesManager: PreferencesManager
) : AuthRepository {

    override suspend fun login(phone: String, password: String): NetworkResult<Unit> {
        return try {
            val response = apiService.login(LoginRequest(phone, password))
            if (response.isSuccessful && response.body()?.isSuccess() == true) {
                val loginResponse = response.body()?.data
                loginResponse?.let {
                    // 保存token和用户信息
                    preferencesManager.saveAccessToken(it.accessToken)
                    preferencesManager.saveRefreshToken(it.refreshToken)
                    preferencesManager.saveMerchantId(it.merchantProfile.id)
                    preferencesManager.saveLoginState(true)
                }
                NetworkResult.Success(Unit)
            } else {
                NetworkResult.Error(response.body()?.getErrorMessage() ?: "登录失败")
            }
        } catch (e: Exception) {
            NetworkResult.Error("网络错误: ${e.message}")
        }
    }

    override suspend fun register(phone: String, password: String, verificationCode: String, merchantName: String): NetworkResult<Unit> {
        return try {
            val response = apiService.register(RegisterRequest(phone, password, verificationCode, merchantName))
            if (response.isSuccessful && response.body()?.isSuccess() == true) {
                NetworkResult.Success(Unit)
            } else {
                NetworkResult.Error(response.body()?.getErrorMessage() ?: "注册失败")
            }
        } catch (e: Exception) {
            NetworkResult.Error("网络错误: ${e.message}")
        }
    }

    override suspend fun sendVerificationCode(phone: String): NetworkResult<Unit> {
        return try {
            val response = apiService.sendVerificationCode(SendCodeRequest(phone))
            if (response.isSuccessful && response.body()?.isSuccess() == true) {
                NetworkResult.Success(Unit)
            } else {
                NetworkResult.Error(response.body()?.getErrorMessage() ?: "发送验证码失败")
            }
        } catch (e: Exception) {
            NetworkResult.Error("网络错误: ${e.message}")
        }
    }

    override suspend fun resetPassword(phone: String, verificationCode: String, newPassword: String): NetworkResult<Unit> {
        return try {
            val response = apiService.resetPassword(ResetPasswordRequest(phone, verificationCode, newPassword))
            if (response.isSuccessful && response.body()?.isSuccess() == true) {
                NetworkResult.Success(Unit)
            } else {
                NetworkResult.Error(response.body()?.getErrorMessage() ?: "重置密码失败")
            }
        } catch (e: Exception) {
            NetworkResult.Error("网络错误: ${e.message}")
        }
    }

    override suspend fun quickLogin(phone: String, verificationCode: String): NetworkResult<Unit> {
        return try {
            val response = apiService.quickLogin(QuickLoginRequest(phone, verificationCode))
            if (response.isSuccessful && response.body()?.isSuccess() == true) {
                val loginResponse = response.body()?.data
                loginResponse?.let {
                    // 保存token和用户信息
                    preferencesManager.saveAccessToken(it.accessToken)
                    preferencesManager.saveRefreshToken(it.refreshToken)
                    preferencesManager.saveMerchantId(it.merchantProfile.id)
                    preferencesManager.saveLoginState(true)
                }
                NetworkResult.Success(Unit)
            } else {
                NetworkResult.Error(response.body()?.getErrorMessage() ?: "快速登录失败")
            }
        } catch (e: Exception) {
            NetworkResult.Error("网络错误: ${e.message}")
        }
    }

    override suspend fun logout() {
        try {
            // 调用服务器登出接口
            apiService.logout()
        } catch (e: Exception) {
            // 即使服务器调用失败，也要清除本地数据
        } finally {
            // 清除本地数据
            preferencesManager.clearTokens()
        }
    }

    override fun isLoggedIn(): Flow<Boolean> {
        return preferencesManager.getLoginState()
    }

    override suspend fun refreshToken(): NetworkResult<Unit> {
        return try {
            val refreshToken = preferencesManager.getRefreshToken()
            // TODO: 实现token刷新逻辑
            NetworkResult.Success(Unit)
        } catch (e: Exception) {
            NetworkResult.Error("刷新token失败: ${e.message}")
        }
    }
}

// 请求数据类
data class RegisterRequest(
    val phone: String,
    val password: String,
    val verificationCode: String,
    val merchantName: String
)

data class SendCodeRequest(val phone: String)

data class ResetPasswordRequest(
    val phone: String,
    val verificationCode: String,
    val newPassword: String
)

data class QuickLoginRequest(
    val phone: String,
    val verificationCode: String
)
