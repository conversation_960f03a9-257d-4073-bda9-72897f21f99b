package com.laundry.user

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.laundry.user.data.api.NetworkModule
import com.laundry.user.ui.auth.LoginActivity
import com.laundry.user.ui.home.HomeFragment
import com.laundry.user.ui.orders.OrdersFragment
import com.laundry.user.ui.profile.ProfileFragment
import android.widget.FrameLayout

class MainActivity : AppCompatActivity() {

    private lateinit var bottomNavigation: BottomNavigationView
    private lateinit var fragmentContainer: FrameLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化网络模块
        NetworkModule.initialize(this)

        // 检查登录状态
        if (!NetworkModule.getTokenManager().isLoggedIn()) {
            startActivity(Intent(this, LoginActivity::class.java))
            finish()
            return
        }

        setupUI()
        setupBottomNavigation()

        // 默认显示首页
        if (savedInstanceState == null) {
            showFragment(HomeFragment())
        }
    }

    private fun setupUI() {
        // 创建主布局
        val mainLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT
            )
        }

        // 创建Fragment容器
        fragmentContainer = FrameLayout(this).apply {
            id = android.view.View.generateViewId()
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
        }

        // 创建底部导航
        bottomNavigation = BottomNavigationView(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }

        mainLayout.addView(fragmentContainer)
        mainLayout.addView(bottomNavigation)

        setContentView(mainLayout)
    }

    private fun setupBottomNavigation() {
        // 创建菜单项
        val menu = bottomNavigation.menu
        menu.add(0, R.id.nav_home, 0, "首页").setIcon(android.R.drawable.ic_menu_view)
        menu.add(0, R.id.nav_orders, 1, "订单").setIcon(android.R.drawable.ic_menu_agenda)
        menu.add(0, R.id.nav_profile, 2, "我的").setIcon(android.R.drawable.ic_menu_myplaces)

        bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_home -> {
                    showFragment(HomeFragment())
                    true
                }
                R.id.nav_orders -> {
                    showFragment(OrdersFragment())
                    true
                }
                R.id.nav_profile -> {
                    showFragment(ProfileFragment())
                    true
                }
                else -> false
            }
        }

        // 默认选中首页
        bottomNavigation.selectedItemId = R.id.nav_home
    }

    private fun showFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction()
            .replace(fragmentContainer.id, fragment)
            .commit()
    }
}
