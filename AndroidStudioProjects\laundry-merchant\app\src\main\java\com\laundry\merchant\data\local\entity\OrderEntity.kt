package com.laundry.merchant.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "orders")
data class OrderEntity(
    @PrimaryKey
    val id: String,
    val customerName: String,
    val customerPhone: String,
    val serviceName: String,
    val serviceDescription: String,
    val amount: Double,
    val status: String,
    val pickupAddress: String,
    val deliveryAddress: String,
    val pickupTime: Date?,
    val deliveryTime: Date?,
    val createdAt: Date,
    val updatedAt: Date,
    val notes: String?,
    val isUrgent: Boolean = false,
    val estimatedDuration: Int? = null,
    val isSynced: Boolean = false
)
