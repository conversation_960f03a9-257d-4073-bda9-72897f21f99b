package com.laundry.merchant.ui.promotion

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.laundry.merchant.data.repository.PromotionRepository
import com.laundry.merchant.ui.main.PromotionStatus
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class PromotionViewModel @Inject constructor(
    private val promotionRepository: PromotionRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(PromotionUiState())
    val uiState: StateFlow<PromotionUiState> = _uiState.asStateFlow()

    private val _events = MutableSharedFlow<PromotionEvent>()
    val events: SharedFlow<PromotionEvent> = _events.asSharedFlow()

    fun loadPromotionData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val promotionStatus = promotionRepository.getPromotionStatus()
                val statistics = promotionRepository.getPromotionStatistics()
                val campaigns = promotionRepository.getPromotionCampaigns()
                val keywords = promotionRepository.getPromotionKeywords()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    promotionStatus = promotionStatus,
                    statistics = statistics,
                    campaigns = campaigns,
                    keywords = keywords
                )
                
                // 检查余额警告
                if (promotionStatus.isLowBalance) {
                    _events.emit(PromotionEvent.ShowLowBalanceWarning(promotionStatus.balance))
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载推广数据失败: ${e.message}"
                )
                _events.emit(PromotionEvent.ShowError("加载推广数据失败"))
            }
        }
    }

    fun refreshData() {
        loadPromotionData()
    }

    fun startPromotion() {
        viewModelScope.launch {
            try {
                promotionRepository.startPromotion()
                _events.emit(PromotionEvent.ShowSuccess("推广已开启"))
                refreshData()
            } catch (e: Exception) {
                _events.emit(PromotionEvent.ShowError("开启推广失败: ${e.message}"))
            }
        }
    }

    fun pausePromotion() {
        viewModelScope.launch {
            try {
                promotionRepository.pausePromotion()
                _events.emit(PromotionEvent.ShowSuccess("推广已暂停"))
                refreshData()
            } catch (e: Exception) {
                _events.emit(PromotionEvent.ShowError("暂停推广失败: ${e.message}"))
            }
        }
    }

    fun adjustDailyBudget(adjustment: Double) {
        viewModelScope.launch {
            try {
                val currentBudget = _uiState.value.promotionStatus?.dailyBudget ?: 0.0
                val newBudget = (currentBudget + adjustment).coerceAtLeast(10.0)
                
                promotionRepository.updateDailyBudget(newBudget)
                _events.emit(PromotionEvent.ShowSuccess("日预算已调整为 ¥${String.format("%.2f", newBudget)}"))
                refreshData()
            } catch (e: Exception) {
                _events.emit(PromotionEvent.ShowError("调整预算失败: ${e.message}"))
            }
        }
    }

    fun toggleCampaignStatus(campaignId: String) {
        viewModelScope.launch {
            try {
                promotionRepository.toggleCampaignStatus(campaignId)
                _events.emit(PromotionEvent.ShowSuccess("推广计划状态已更新"))
                refreshData()
            } catch (e: Exception) {
                _events.emit(PromotionEvent.ShowError("更新计划状态失败: ${e.message}"))
            }
        }
    }

    fun toggleKeywordStatus(keywordId: String) {
        viewModelScope.launch {
            try {
                promotionRepository.toggleKeywordStatus(keywordId)
                _events.emit(PromotionEvent.ShowSuccess("关键词状态已更新"))
                refreshData()
            } catch (e: Exception) {
                _events.emit(PromotionEvent.ShowError("更新关键词状态失败: ${e.message}"))
            }
        }
    }

    fun adjustKeywordBid(keywordId: String, newBid: Double) {
        viewModelScope.launch {
            try {
                promotionRepository.updateKeywordBid(keywordId, newBid)
                _events.emit(PromotionEvent.ShowSuccess("关键词出价已更新"))
                refreshData()
            } catch (e: Exception) {
                _events.emit(PromotionEvent.ShowError("更新出价失败: ${e.message}"))
            }
        }
    }
}

// UI状态数据类
data class PromotionUiState(
    val isLoading: Boolean = false,
    val promotionStatus: PromotionStatus? = null,
    val statistics: PromotionStatistics? = null,
    val campaigns: List<PromotionCampaign> = emptyList(),
    val keywords: List<PromotionKeyword> = emptyList(),
    val error: String? = null
)

// 事件数据类
sealed class PromotionEvent {
    data class ShowError(val message: String) : PromotionEvent()
    data class ShowSuccess(val message: String) : PromotionEvent()
    data class ShowLowBalanceWarning(val balance: Double) : PromotionEvent()
    object NavigateToRecharge : PromotionEvent()
}

// 推广统计数据类
data class PromotionStatistics(
    val totalClicks: Int,
    val totalImpressions: Int,
    val clickRate: Double,
    val averageCpc: Double,
    val conversionRate: Double,
    val totalSpent: Double,
    val totalOrders: Int
)

// 推广计划数据类
data class PromotionCampaign(
    val id: String,
    val name: String,
    val type: String, // keyword, shop, service
    val status: String, // active, paused, stopped
    val dailyBudget: Double,
    val todaySpent: Double,
    val clicks: Int,
    val impressions: Int,
    val orders: Int,
    val createdAt: Date
)

// 推广关键词数据类
data class PromotionKeyword(
    val id: String,
    val keyword: String,
    val bid: Double,
    val status: String, // active, paused
    val quality: Int, // 1-10
    val clicks: Int,
    val impressions: Int,
    val averagePosition: Double,
    val cost: Double
)
