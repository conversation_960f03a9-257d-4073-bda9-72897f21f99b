package com.laundry.user.data.api

import com.google.gson.annotations.SerializedName

/**
 * API通用响应格式
 */
data class ApiResponse<T>(
    @SerializedName("code")
    val code: Int = 0,
    
    @SerializedName("message")
    val message: String = "",
    
    @SerializedName("data")
    val data: T? = null,
    
    @SerializedName("success")
    val success: Boolean = false,
    
    @SerializedName("timestamp")
    val timestamp: Long = 0L
)

/**
 * 分页响应格式
 */
data class PagedResponse<T>(
    @SerializedName("items")
    val items: List<T> = emptyList(),
    
    @SerializedName("total")
    val total: Int = 0,
    
    @SerializedName("page")
    val page: Int = 1,
    
    @SerializedName("size")
    val size: Int = 20,
    
    @SerializedName("pages")
    val pages: Int = 0,
    
    @SerializedName("has_next")
    val hasNext: Boolean = false,
    
    @SerializedName("has_prev")
    val hasPrev: Boolean = false
)

// ==================== 认证相关 ====================

/**
 * 发送验证码请求
 */
data class SendCodeRequest(
    @SerializedName("phone")
    val phone: String,
    
    @SerializedName("type")
    val type: String = "login" // login, register, reset_password
)

/**
 * 登录请求
 */
data class LoginRequest(
    @SerializedName("phone")
    val phone: String,
    
    @SerializedName("code")
    val code: String,
    
    @SerializedName("device_id")
    val deviceId: String = "",
    
    @SerializedName("device_type")
    val deviceType: String = "android",
    
    @SerializedName("app_version")
    val appVersion: String = ""
)

/**
 * 登录响应
 */
data class LoginResponse(
    @SerializedName("access_token")
    val accessToken: String = "",
    
    @SerializedName("refresh_token")
    val refreshToken: String = "",
    
    @SerializedName("expires_in")
    val expiresIn: Long = 0L,
    
    @SerializedName("user")
    val user: com.laundry.user.data.model.User? = null
)

/**
 * 刷新Token请求
 */
data class RefreshTokenRequest(
    @SerializedName("refresh_token")
    val refreshToken: String
)

// ==================== 订单相关 ====================

/**
 * 创建订单请求
 */
data class CreateOrderRequest(
    @SerializedName("merchant_id")
    val merchantId: String,
    
    @SerializedName("service_items")
    val serviceItems: List<com.laundry.user.data.model.OrderItem>,
    
    @SerializedName("pickup_address_id")
    val pickupAddressId: String,
    
    @SerializedName("delivery_address_id")
    val deliveryAddressId: String,
    
    @SerializedName("pickup_time")
    val pickupTime: String = "",
    
    @SerializedName("delivery_time")
    val deliveryTime: String = "",
    
    @SerializedName("notes")
    val notes: String = "",
    
    @SerializedName("special_requirements")
    val specialRequirements: String = "",
    
    @SerializedName("coupon_id")
    val couponId: String = "",
    
    @SerializedName("points_used")
    val pointsUsed: Int = 0
)

/**
 * 取消订单请求
 */
data class CancelOrderRequest(
    @SerializedName("reason")
    val reason: String,
    
    @SerializedName("description")
    val description: String = ""
)

// ==================== 支付相关 ====================

/**
 * 创建支付请求
 */
data class CreatePaymentRequest(
    @SerializedName("order_id")
    val orderId: String,
    
    @SerializedName("payment_method")
    val paymentMethod: String, // alipay, wechat
    
    @SerializedName("return_url")
    val returnUrl: String = ""
)

/**
 * 支付响应
 */
data class PaymentResponse(
    @SerializedName("payment_id")
    val paymentId: String = "",
    
    @SerializedName("payment_url")
    val paymentUrl: String = "",
    
    @SerializedName("payment_data")
    val paymentData: String = "", // 支付宝/微信支付参数
    
    @SerializedName("qr_code")
    val qrCode: String = ""
)

/**
 * 支付状态响应
 */
data class PaymentStatusResponse(
    @SerializedName("status")
    val status: String, // pending, success, failed, cancelled
    
    @SerializedName("transaction_id")
    val transactionId: String = "",
    
    @SerializedName("paid_at")
    val paidAt: String = ""
)

// ==================== 评价相关 ====================

/**
 * 提交评价请求
 */
data class SubmitReviewRequest(
    @SerializedName("order_id")
    val orderId: String,
    
    @SerializedName("merchant_id")
    val merchantId: String,
    
    @SerializedName("rating")
    val rating: Float,
    
    @SerializedName("service_rating")
    val serviceRating: Float,
    
    @SerializedName("quality_rating")
    val qualityRating: Float,
    
    @SerializedName("speed_rating")
    val speedRating: Float,
    
    @SerializedName("comment")
    val comment: String = "",
    
    @SerializedName("images")
    val images: List<String> = emptyList(),
    
    @SerializedName("is_anonymous")
    val isAnonymous: Boolean = false
)

// ==================== 收藏相关 ====================

/**
 * 添加收藏请求
 */
data class AddFavoriteRequest(
    @SerializedName("merchant_id")
    val merchantId: String
)

// ==================== 聊天相关 ====================

/**
 * 发送消息请求
 */
data class SendMessageRequest(
    @SerializedName("session_id")
    val sessionId: String = "",
    
    @SerializedName("merchant_id")
    val merchantId: String = "",
    
    @SerializedName("message_type")
    val messageType: Int,
    
    @SerializedName("content")
    val content: String = "",
    
    @SerializedName("media_url")
    val mediaUrl: String = "",
    
    @SerializedName("media_duration")
    val mediaDuration: Int = 0,
    
    @SerializedName("location_latitude")
    val locationLatitude: Double = 0.0,
    
    @SerializedName("location_longitude")
    val locationLongitude: Double = 0.0,
    
    @SerializedName("location_address")
    val locationAddress: String = ""
)

// ==================== 其他 ====================

/**
 * 系统配置
 */
data class SystemConfig(
    @SerializedName("app_version")
    val appVersion: String = "",
    
    @SerializedName("force_update")
    val forceUpdate: Boolean = false,
    
    @SerializedName("update_url")
    val updateUrl: String = "",
    
    @SerializedName("customer_service_phone")
    val customerServicePhone: String = "",
    
    @SerializedName("customer_service_hours")
    val customerServiceHours: String = "",
    
    @SerializedName("privacy_policy_url")
    val privacyPolicyUrl: String = "",
    
    @SerializedName("terms_of_service_url")
    val termsOfServiceUrl: String = "",
    
    @SerializedName("about_us_url")
    val aboutUsUrl: String = ""
)

/**
 * 反馈请求
 */
data class FeedbackRequest(
    @SerializedName("type")
    val type: String, // bug, suggestion, complaint, other
    
    @SerializedName("title")
    val title: String,
    
    @SerializedName("content")
    val content: String,
    
    @SerializedName("contact_info")
    val contactInfo: String = "",
    
    @SerializedName("images")
    val images: List<String> = emptyList(),
    
    @SerializedName("device_info")
    val deviceInfo: String = "",
    
    @SerializedName("app_version")
    val appVersion: String = ""
)
