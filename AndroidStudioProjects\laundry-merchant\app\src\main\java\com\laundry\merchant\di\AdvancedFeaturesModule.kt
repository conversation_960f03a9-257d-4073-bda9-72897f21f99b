package com.laundry.merchant.di

import android.content.Context
import com.laundry.merchant.analytics.AnalyticsManager
import com.laundry.merchant.map.MapManager
import com.laundry.merchant.payment.PaymentManager
import com.laundry.merchant.websocket.WebSocketManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AdvancedFeaturesModule {

    @Provides
    @Singleton
    fun provideWebSocketManager(): WebSocketManager {
        return WebSocketManager()
    }

    @Provides
    @Singleton
    fun provideMapManager(@ApplicationContext context: Context): MapManager {
        return MapManager(context)
    }

    @Provides
    @Singleton
    fun providePaymentManager(@ApplicationContext context: Context): PaymentManager {
        return PaymentManager(context)
    }

    @Provides
    @Singleton
    fun provideAnalyticsManager(@ApplicationContext context: Context): AnalyticsManager {
        return AnalyticsManager(context)
    }
}
