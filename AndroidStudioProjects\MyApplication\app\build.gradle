plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'com.google.firebase.firebase-perf'
}

android {
    namespace 'com.laundry.app'
    compileSdk 33

    defaultConfig {
        applicationId "com.laundry.app"
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        // 多语言支持
        resConfigs "zh", "zh-rCN", "zh-rTW", "en"
        
        // 矢量图支持
        vectorDrawables.useSupportLibrary = true
        
        // 数据库配置
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += [
                    "room.schemaLocation": "$projectDir/schemas".toString(),
                    "room.incremental": "true"
                ]
            }
        }
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            storeFile file('release.keystore')
            storePassword project.hasProperty('KEYSTORE_PASSWORD') ? KEYSTORE_PASSWORD : ''
            keyAlias project.hasProperty('KEY_ALIAS') ? KEY_ALIAS : ''
            keyPassword project.hasProperty('KEY_PASSWORD') ? KEY_PASSWORD : ''
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.debug
            
            buildConfigField "String", "API_BASE_URL", '"https://dev-api.laundryhelper.com/v1/"'
            buildConfigField "String", "WEBSOCKET_URL", '"wss://dev-ws.laundryhelper.com/"'
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
            buildConfigField "boolean", "ENABLE_DEBUG_TOOLS", "true"
            
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            
            resValue "string", "app_name", "洗护帮-测试"
        }
        
        staging {
            debuggable false
            minifyEnabled true
            shrinkResources true
            signingConfig signingConfigs.debug
            
            buildConfigField "String", "API_BASE_URL", '"https://staging-api.laundryhelper.com/v1/"'
            buildConfigField "String", "WEBSOCKET_URL", '"wss://staging-ws.laundryhelper.com/"'
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
            buildConfigField "boolean", "ENABLE_DEBUG_TOOLS", "false"
            
            applicationIdSuffix ".staging"
            versionNameSuffix "-staging"
            
            resValue "string", "app_name", "洗护帮-预发布"
            
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            signingConfig signingConfigs.release
            
            buildConfigField "String", "API_BASE_URL", '"https://api.laundryhelper.com/v1/"'
            buildConfigField "String", "WEBSOCKET_URL", '"wss://ws.laundryhelper.com/"'
            buildConfigField "boolean", "ENABLE_LOGGING", "false"
            buildConfigField "boolean", "ENABLE_DEBUG_TOOLS", "false"
            
            resValue "string", "app_name", "洗护帮"
            
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // 启用资源优化
            postprocessing {
                removeUnusedCode true
                removeUnusedResources true
                obfuscate true
                optimizeCode true
                proguardFile 'proguard-rules.pro'
            }
        }
    }
    
    flavorDimensions "version"
    productFlavors {
        free {
            dimension "version"
            applicationIdSuffix ".free"
            versionNameSuffix "-free"
            
            buildConfigField "boolean", "IS_PRO_VERSION", "false"
            buildConfigField "int", "MAX_FAVORITES", "50"
            buildConfigField "boolean", "ENABLE_ADS", "true"
        }
        
        pro {
            dimension "version"
            applicationIdSuffix ".pro"
            versionNameSuffix "-pro"
            
            buildConfigField "boolean", "IS_PRO_VERSION", "true"
            buildConfigField "int", "MAX_FAVORITES", "999"
            buildConfigField "boolean", "ENABLE_ADS", "false"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        
        // 启用脱糖支持
        coreLibraryDesugaringEnabled true
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
        
        // 启用协程支持
        freeCompilerArgs += [
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-opt-in=kotlinx.coroutines.FlowPreview"
        ]
    }
    
    buildFeatures {
        viewBinding true
        dataBinding true
        buildConfig true
        compose true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }
    
    packagingOptions {
        resources {
            excludes += [
                '/META-INF/{AL2.0,LGPL2.1}',
                '/META-INF/DEPENDENCIES',
                '/META-INF/LICENSE',
                '/META-INF/LICENSE.txt',
                '/META-INF/NOTICE',
                '/META-INF/NOTICE.txt'
            ]
        }
    }
    
    // 测试配置
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
        
        animationsDisabled = true
    }
    
    // Lint配置
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
        disable 'MissingTranslation', 'ExtraTranslation'
    }
    
    // 分包配置
    dexOptions {
        javaMaxHeapSize "4g"
        preDexLibraries = false
    }
    
    // Bundle配置
    bundle {
        language {
            enableSplit = true
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}

dependencies {
    // 脱糖支持
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
    
    // Android核心库
    implementation 'androidx.core:core-ktx:1.10.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    
    // Activity和Fragment
    implementation 'androidx.activity:activity-ktx:1.7.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.0'
    
    // 生命周期组件
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.6.1'
    
    // 导航组件
    implementation 'androidx.navigation:navigation-fragment-ktx:2.6.0'
    implementation 'androidx.navigation:navigation-ui-ktx:2.6.0'
    
    // Room数据库
    implementation 'androidx.room:room-runtime:2.5.0'
    implementation 'androidx.room:room-ktx:2.5.0'
    kapt 'androidx.room:room-compiler:2.5.0'
    
    // WorkManager
    implementation 'androidx.work:work-runtime-ktx:2.8.1'
    
    // DataStore
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    
    // 协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.1'
    
    // 网络请求
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'
    
    // JSON解析
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // 图片加载
    implementation 'com.github.bumptech.glide:glide:4.15.1'
    kapt 'com.github.bumptech.glide:compiler:4.15.1'
    implementation 'com.github.bumptech.glide:okhttp3-integration:4.15.1'
    
    // 权限管理
    implementation 'com.permissionx.guolindev:permissionx:1.7.1'
    
    // 事件总线
    implementation 'org.greenrobot:eventbus:3.3.1'
    
    // 依赖注入
    implementation 'com.google.dagger:hilt-android:2.46'
    kapt 'com.google.dagger:hilt-compiler:2.46'
    implementation 'androidx.hilt:hilt-work:1.0.0'
    kapt 'androidx.hilt:hilt-compiler:1.0.0'
    
    // Firebase
    implementation platform('com.google.firebase:firebase-bom:32.1.1')
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-perf-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'
    implementation 'com.google.firebase:firebase-storage-ktx'
    
    // 地图服务
    implementation 'com.google.android.gms:play-services-maps:18.1.0'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    
    // 支付SDK
    implementation 'com.alipay.sdk:alipaysdk-android:15.8.11'
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.0'
    
    // 二维码
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    
    // 图片选择器
    implementation 'com.github.LuckSiege.PictureSelector:picture_library:v3.10.7'
    
    // 轮播图
    implementation 'com.github.zhpanvip:bannerviewpager:3.5.12'
    
    // 下拉刷新
    implementation 'io.github.scwang90:refresh-layout-kernel:2.0.6'
    implementation 'io.github.scwang90:refresh-header-classics:2.0.6'
    implementation 'io.github.scwang90:refresh-footer-classics:2.0.6'
    
    // 状态栏
    implementation 'com.geyifeng.immersionbar:immersionbar:3.2.2'
    implementation 'com.geyifeng.immersionbar:immersionbar-ktx:3.2.2'
    
    // 加载动画
    implementation 'com.airbnb.android:lottie:6.0.1'
    
    // 测试依赖
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.3.1'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.0.0'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.1'
    testImplementation 'androidx.room:room-testing:2.5.0'
    
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-contrib:3.5.1'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.5.1'
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'
    androidTestImplementation 'androidx.work:work-testing:2.8.1'
}
