package com.laundry.merchant.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment

object PermissionUtils {

    // 权限请求码
    const val REQUEST_CAMERA = 1001
    const val REQUEST_PHONE = 1002
    const val REQUEST_LOCATION = 1003
    const val REQUEST_STORAGE = 1004
    const val REQUEST_NOTIFICATION = 1005
    const val REQUEST_ALL_PERMISSIONS = 1006

    // 常用权限组
    val CAMERA_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)
    
    val PHONE_PERMISSIONS = arrayOf(Manifest.permission.CALL_PHONE)
    
    val LOCATION_PERMISSIONS = arrayOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )
    
    val STORAGE_PERMISSIONS = arrayOf(
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    )
    
    val NOTIFICATION_PERMISSIONS = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
        arrayOf(Manifest.permission.POST_NOTIFICATIONS)
    } else {
        emptyArray()
    }

    val ALL_PERMISSIONS = CAMERA_PERMISSIONS + PHONE_PERMISSIONS + LOCATION_PERMISSIONS + STORAGE_PERMISSIONS + NOTIFICATION_PERMISSIONS

    /**
     * 检查单个权限是否已授予
     */
    fun hasPermission(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 检查多个权限是否都已授予
     */
    fun hasPermissions(context: Context, permissions: Array<String>): Boolean {
        return permissions.all { hasPermission(context, it) }
    }

    /**
     * 请求单个权限
     */
    fun requestPermission(activity: Activity, permission: String, requestCode: Int) {
        ActivityCompat.requestPermissions(activity, arrayOf(permission), requestCode)
    }

    /**
     * 请求多个权限
     */
    fun requestPermissions(activity: Activity, permissions: Array<String>, requestCode: Int) {
        ActivityCompat.requestPermissions(activity, permissions, requestCode)
    }

    /**
     * Fragment请求权限
     */
    fun requestPermissions(fragment: Fragment, permissions: Array<String>, requestCode: Int) {
        fragment.requestPermissions(permissions, requestCode)
    }

    /**
     * 检查权限请求结果
     */
    fun isPermissionGranted(grantResults: IntArray): Boolean {
        return grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }
    }

    /**
     * 检查是否应该显示权限说明
     */
    fun shouldShowRequestPermissionRationale(activity: Activity, permission: String): Boolean {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
    }

    /**
     * 显示权限说明对话框
     */
    fun showPermissionRationaleDialog(
        context: Context,
        title: String,
        message: String,
        onPositive: () -> Unit,
        onNegative: (() -> Unit)? = null
    ) {
        AlertDialog.Builder(context)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("授予权限") { _, _ -> onPositive() }
            .setNegativeButton("拒绝") { _, _ -> onNegative?.invoke() }
            .setCancelable(false)
            .show()
    }

    /**
     * 显示前往设置页面的对话框
     */
    fun showGoToSettingsDialog(context: Context, message: String = "需要在设置中手动开启权限") {
        AlertDialog.Builder(context)
            .setTitle("权限被拒绝")
            .setMessage(message)
            .setPositiveButton("去设置") { _, _ ->
                openAppSettings(context)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 打开应用设置页面
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
        }
        context.startActivity(intent)
    }

    /**
     * 请求相机权限
     */
    fun requestCameraPermission(activity: Activity) {
        when {
            hasPermissions(activity, CAMERA_PERMISSIONS) -> {
                // 权限已授予
                return
            }
            shouldShowRequestPermissionRationale(activity, Manifest.permission.CAMERA) -> {
                showPermissionRationaleDialog(
                    activity,
                    "相机权限",
                    "需要相机权限来扫描二维码",
                    { requestPermissions(activity, CAMERA_PERMISSIONS, REQUEST_CAMERA) }
                )
            }
            else -> {
                requestPermissions(activity, CAMERA_PERMISSIONS, REQUEST_CAMERA)
            }
        }
    }

    /**
     * 请求电话权限
     */
    fun requestPhonePermission(activity: Activity) {
        when {
            hasPermissions(activity, PHONE_PERMISSIONS) -> {
                return
            }
            shouldShowRequestPermissionRationale(activity, Manifest.permission.CALL_PHONE) -> {
                showPermissionRationaleDialog(
                    activity,
                    "电话权限",
                    "需要电话权限来联系客户",
                    { requestPermissions(activity, PHONE_PERMISSIONS, REQUEST_PHONE) }
                )
            }
            else -> {
                requestPermissions(activity, PHONE_PERMISSIONS, REQUEST_PHONE)
            }
        }
    }

    /**
     * 请求位置权限
     */
    fun requestLocationPermission(activity: Activity) {
        when {
            hasPermissions(activity, LOCATION_PERMISSIONS) -> {
                return
            }
            shouldShowRequestPermissionRationale(activity, Manifest.permission.ACCESS_FINE_LOCATION) -> {
                showPermissionRationaleDialog(
                    activity,
                    "位置权限",
                    "需要位置权限来获取您的位置信息",
                    { requestPermissions(activity, LOCATION_PERMISSIONS, REQUEST_LOCATION) }
                )
            }
            else -> {
                requestPermissions(activity, LOCATION_PERMISSIONS, REQUEST_LOCATION)
            }
        }
    }

    /**
     * 请求存储权限
     */
    fun requestStoragePermission(activity: Activity) {
        when {
            hasPermissions(activity, STORAGE_PERMISSIONS) -> {
                return
            }
            shouldShowRequestPermissionRationale(activity, Manifest.permission.READ_EXTERNAL_STORAGE) -> {
                showPermissionRationaleDialog(
                    activity,
                    "存储权限",
                    "需要存储权限来保存图片和文件",
                    { requestPermissions(activity, STORAGE_PERMISSIONS, REQUEST_STORAGE) }
                )
            }
            else -> {
                requestPermissions(activity, STORAGE_PERMISSIONS, REQUEST_STORAGE)
            }
        }
    }

    /**
     * 请求通知权限（Android 13+）
     */
    fun requestNotificationPermission(activity: Activity) {
        if (NOTIFICATION_PERMISSIONS.isEmpty()) return
        
        when {
            hasPermissions(activity, NOTIFICATION_PERMISSIONS) -> {
                return
            }
            shouldShowRequestPermissionRationale(activity, Manifest.permission.POST_NOTIFICATIONS) -> {
                showPermissionRationaleDialog(
                    activity,
                    "通知权限",
                    "需要通知权限来接收重要消息",
                    { requestPermissions(activity, NOTIFICATION_PERMISSIONS, REQUEST_NOTIFICATION) }
                )
            }
            else -> {
                requestPermissions(activity, NOTIFICATION_PERMISSIONS, REQUEST_NOTIFICATION)
            }
        }
    }

    /**
     * 请求所有必要权限
     */
    fun requestAllPermissions(activity: Activity) {
        val deniedPermissions = ALL_PERMISSIONS.filter { !hasPermission(activity, it) }
        
        if (deniedPermissions.isEmpty()) {
            return
        }

        requestPermissions(activity, deniedPermissions.toTypedArray(), REQUEST_ALL_PERMISSIONS)
    }

    /**
     * 处理权限请求结果
     */
    fun handlePermissionResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray,
        onGranted: () -> Unit,
        onDenied: () -> Unit,
        onShowRationale: (() -> Unit)? = null
    ) {
        when (requestCode) {
            REQUEST_CAMERA, REQUEST_PHONE, REQUEST_LOCATION, REQUEST_STORAGE, REQUEST_NOTIFICATION, REQUEST_ALL_PERMISSIONS -> {
                if (isPermissionGranted(grantResults)) {
                    onGranted()
                } else {
                    if (onShowRationale != null) {
                        onShowRationale()
                    } else {
                        onDenied()
                    }
                }
            }
        }
    }
}
