package com.laundry.merchant.ui.promotion

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.laundry.merchant.databinding.FragmentPromotionBinding
import com.laundry.merchant.ui.promotion.adapter.PromotionCampaignAdapter
import com.laundry.merchant.ui.promotion.adapter.PromotionKeywordAdapter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class PromotionFragment : Fragment() {

    private var _binding: FragmentPromotionBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PromotionViewModel by viewModels()
    
    private lateinit var campaignAdapter: PromotionCampaignAdapter
    private lateinit var keywordAdapter: PromotionKeywordAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPromotionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupViews()
        setupRecyclerViews()
        setupTabs()
        observeViewModel()
        
        // 加载数据
        viewModel.loadPromotionData()
    }

    private fun setupViews() {
        // 设置刷新监听
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshData()
        }
        
        // 设置按钮点击监听
        binding.buttonStartPromotion.setOnClickListener {
            if (binding.buttonStartPromotion.text == "开始投流") {
                viewModel.startPromotion()
            } else {
                viewModel.pausePromotion()
            }
        }
        
        binding.buttonRecharge.setOnClickListener {
            navigateToPromotionRecharge()
        }
        
        binding.buttonCreateCampaign.setOnClickListener {
            navigateToCreateCampaign()
        }
        
        binding.buttonAddKeyword.setOnClickListener {
            showAddKeywordDialog()
        }
        
        // 设置预算调整
        binding.buttonIncreaseBudget.setOnClickListener {
            viewModel.adjustDailyBudget(50.0)
        }
        
        binding.buttonDecreaseBudget.setOnClickListener {
            viewModel.adjustDailyBudget(-50.0)
        }
    }

    private fun setupRecyclerViews() {
        // 推广计划
        campaignAdapter = PromotionCampaignAdapter(
            onCampaignClick = { campaign ->
                navigateToCampaignDetail(campaign.id)
            },
            onToggleStatus = { campaign ->
                viewModel.toggleCampaignStatus(campaign.id)
            },
            onEditCampaign = { campaign ->
                navigateToEditCampaign(campaign.id)
            }
        )
        binding.recyclerViewCampaigns.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = campaignAdapter
        }
        
        // 关键词
        keywordAdapter = PromotionKeywordAdapter(
            onKeywordClick = { keyword ->
                showKeywordDetailDialog(keyword)
            },
            onToggleKeyword = { keyword ->
                viewModel.toggleKeywordStatus(keyword.id)
            },
            onAdjustBid = { keyword, newBid ->
                viewModel.adjustKeywordBid(keyword.id, newBid)
            }
        )
        binding.recyclerViewKeywords.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = keywordAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("概览"))
            addTab(newTab().setText("推广计划"))
            addTab(newTab().setText("关键词"))
            addTab(newTab().setText("数据报告"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    when (tab?.position) {
                        0 -> showOverviewContent()
                        1 -> showCampaignsContent()
                        2 -> showKeywordsContent()
                        3 -> showReportsContent()
                    }
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
        
        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: PromotionUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading
        
        // 更新推广状态
        state.promotionStatus?.let { status ->
            binding.textPromotionStatus.text = when (status.status) {
                "active" -> "投流中"
                "paused" -> "已暂停"
                "stopped" -> "已停止"
                else -> "异常"
            }
            
            binding.textBalance.text = "余额: ¥${String.format("%.2f", status.balance)}"
            binding.textDailyBudget.text = "日预算: ¥${String.format("%.2f", status.dailyBudget)}"
            binding.textTodaySpent.text = "今日消费: ¥${String.format("%.2f", status.todaySpent)}"
            
            // 更新按钮状态
            binding.buttonStartPromotion.text = if (status.status == "active") "暂停投流" else "开始投流"
            binding.buttonStartPromotion.isEnabled = status.balance > 0
        }
        
        // 更新统计数据
        state.statistics?.let { stats ->
            binding.textTotalClicks.text = stats.totalClicks.toString()
            binding.textTotalImpressions.text = stats.totalImpressions.toString()
            binding.textClickRate.text = "${String.format("%.2f", stats.clickRate * 100)}%"
            binding.textAverageCpc.text = "¥${String.format("%.2f", stats.averageCpc)}"
            binding.textConversionRate.text = "${String.format("%.2f", stats.conversionRate * 100)}%"
        }
        
        // 更新列表数据
        campaignAdapter.updateData(state.campaigns)
        keywordAdapter.updateData(state.keywords)
        
        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: PromotionEvent) {
        when (event) {
            is PromotionEvent.ShowError -> {
                showError(event.message)
            }
            is PromotionEvent.ShowSuccess -> {
                showSuccess(event.message)
            }
            is PromotionEvent.ShowLowBalanceWarning -> {
                showLowBalanceWarning(event.balance)
            }
            is PromotionEvent.NavigateToRecharge -> {
                navigateToPromotionRecharge()
            }
        }
    }

    private fun showOverviewContent() {
        binding.layoutOverview.visibility = View.VISIBLE
        binding.layoutCampaigns.visibility = View.GONE
        binding.layoutKeywords.visibility = View.GONE
        binding.layoutReports.visibility = View.GONE
    }

    private fun showCampaignsContent() {
        binding.layoutOverview.visibility = View.GONE
        binding.layoutCampaigns.visibility = View.VISIBLE
        binding.layoutKeywords.visibility = View.GONE
        binding.layoutReports.visibility = View.GONE
    }

    private fun showKeywordsContent() {
        binding.layoutOverview.visibility = View.GONE
        binding.layoutCampaigns.visibility = View.GONE
        binding.layoutKeywords.visibility = View.VISIBLE
        binding.layoutReports.visibility = View.GONE
    }

    private fun showReportsContent() {
        binding.layoutOverview.visibility = View.GONE
        binding.layoutCampaigns.visibility = View.GONE
        binding.layoutKeywords.visibility = View.GONE
        binding.layoutReports.visibility = View.VISIBLE
    }

    private fun navigateToPromotionRecharge() {
        // TODO: 导航到推广充值页面
    }

    private fun navigateToCreateCampaign() {
        // TODO: 导航到创建推广计划页面
    }

    private fun navigateToCampaignDetail(campaignId: String) {
        // TODO: 导航到推广计划详情页面
    }

    private fun navigateToEditCampaign(campaignId: String) {
        // TODO: 导航到编辑推广计划页面
    }

    private fun showAddKeywordDialog() {
        // TODO: 显示添加关键词对话框
    }

    private fun showKeywordDetailDialog(keyword: PromotionKeyword) {
        // TODO: 显示关键词详情对话框
    }

    private fun showError(message: String) {
        // TODO: 显示错误信息
    }

    private fun showSuccess(message: String) {
        // TODO: 显示成功信息
    }

    private fun showLowBalanceWarning(balance: Double) {
        // TODO: 显示余额不足警告
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
