package com.laundry.merchant.ui.auth

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.laundry.merchant.databinding.ActivityLoginBinding
import com.laundry.merchant.ui.main.MainActivity
import com.laundry.merchant.utils.hideKeyboard
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding
    private val viewModel: AuthViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        observeViewModel()
        checkLoginState()
    }

    private fun setupViews() {
        // 设置输入监听
        binding.editTextPhone.addTextChangedListener(createTextWatcher())
        binding.editTextPassword.addTextChangedListener(createTextWatcher())

        // 设置点击监听
        binding.buttonLogin.setOnClickListener {
            performLogin()
        }

        binding.textViewRegister.setOnClickListener {
            startActivity(Intent(this, RegisterActivity::class.java))
        }

        binding.textViewForgotPassword.setOnClickListener {
            startActivity(Intent(this, ForgotPasswordActivity::class.java))
        }

        binding.textViewQuickLogin.setOnClickListener {
            startActivity(Intent(this, QuickLoginActivity::class.java))
        }

        // 设置协议点击
        binding.checkBoxAgreement.setOnCheckedChangeListener { _, isChecked ->
            updateLoginButtonState()
        }

        binding.textViewUserAgreement.setOnClickListener {
            // TODO: 打开用户协议页面
        }

        binding.textViewPrivacyPolicy.setOnClickListener {
            // TODO: 打开隐私政策页面
        }

        // 设置根布局点击隐藏键盘
        binding.root.setOnClickListener {
            hideKeyboard()
        }
    }

    private fun createTextWatcher(): TextWatcher {
        return object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                updateLoginButtonState()
                clearErrors()
            }
        }
    }

    private fun updateLoginButtonState() {
        val phone = binding.editTextPhone.text.toString().trim()
        val password = binding.editTextPassword.text.toString().trim()
        val isAgreed = binding.checkBoxAgreement.isChecked

        binding.buttonLogin.isEnabled = phone.isNotEmpty() && 
                password.isNotEmpty() && 
                isAgreed &&
                isValidPhone(phone)
    }

    private fun isValidPhone(phone: String): Boolean {
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }

    private fun clearErrors() {
        binding.textInputLayoutPhone.error = null
        binding.textInputLayoutPassword.error = null
    }

    private fun performLogin() {
        val phone = binding.editTextPhone.text.toString().trim()
        val password = binding.editTextPassword.text.toString().trim()

        // 验证输入
        if (!validateInput(phone, password)) {
            return
        }

        hideKeyboard()
        viewModel.login(phone, password)
    }

    private fun validateInput(phone: String, password: String): Boolean {
        var isValid = true

        if (phone.isEmpty()) {
            binding.textInputLayoutPhone.error = "请输入手机号"
            isValid = false
        } else if (!isValidPhone(phone)) {
            binding.textInputLayoutPhone.error = "请输入正确的手机号"
            isValid = false
        }

        if (password.isEmpty()) {
            binding.textInputLayoutPassword.error = "请输入密码"
            isValid = false
        } else if (password.length < 6) {
            binding.textInputLayoutPassword.error = "密码至少6位"
            isValid = false
        }

        return isValid
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: AuthUiState) {
        // 更新加载状态
        binding.progressBar.visibility = if (state.isLoading) View.VISIBLE else View.GONE
        binding.buttonLogin.isEnabled = !state.isLoading && 
                binding.editTextPhone.text.toString().trim().isNotEmpty() &&
                binding.editTextPassword.text.toString().trim().isNotEmpty() &&
                binding.checkBoxAgreement.isChecked

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun handleEvent(event: AuthEvent) {
        when (event) {
            is AuthEvent.LoginSuccess -> {
                showToast("登录成功")
                navigateToMain()
            }
            is AuthEvent.LoginError -> {
                showError(event.message)
            }
            is AuthEvent.ValidationError -> {
                showValidationError(event.field, event.message)
            }
        }
    }

    private fun showError(message: String) {
        showToast(message)
    }

    private fun showValidationError(field: String, message: String) {
        when (field) {
            "phone" -> binding.textInputLayoutPhone.error = message
            "password" -> binding.textInputLayoutPassword.error = message
        }
    }

    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    private fun checkLoginState() {
        lifecycleScope.launch {
            if (viewModel.isLoggedIn()) {
                navigateToMain()
            }
        }
    }
}
