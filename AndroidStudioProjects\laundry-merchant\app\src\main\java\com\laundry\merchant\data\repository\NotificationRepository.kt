package com.laundry.merchant.data.repository

import com.laundry.merchant.network.ApiService
import com.laundry.merchant.ui.notification.NotificationData
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

interface NotificationRepository {
    suspend fun getNotifications(): List<NotificationData>
    suspend fun getUnreadCount(): Int
    suspend fun markAsRead(notificationId: String)
    suspend fun markAllAsRead()
    suspend fun deleteNotification(notificationId: String)
    suspend fun clearAllNotifications()
}

@Singleton
class NotificationRepositoryImpl @Inject constructor(
    private val apiService: ApiService
) : NotificationRepository {

    override suspend fun getNotifications(): List<NotificationData> {
        return try {
            val response = apiService.getNotifications()
            if (response.isSuccessful && response.body()?.isSuccess() == true) {
                response.body()?.data?.map { notification ->
                    NotificationData(
                        id = notification.id,
                        title = notification.title,
                        content = notification.content,
                        type = notification.type,
                        isRead = notification.isRead,
                        createdAt = parseDate(notification.createdAt),
                        relatedId = extractRelatedId(notification.content),
                        priority = determinePriority(notification.type),
                        actionText = getActionText(notification.type),
                        actionUrl = notification.actionUrl
                    )
                } ?: emptyList()
            } else {
                // 返回模拟数据
                getMockNotifications()
            }
        } catch (e: Exception) {
            // 返回模拟数据
            getMockNotifications()
        }
    }

    override suspend fun getUnreadCount(): Int {
        return try {
            val response = apiService.getUnreadNotificationCount()
            if (response.isSuccessful && response.body()?.isSuccess() == true) {
                response.body()?.data ?: 0
            } else {
                0
            }
        } catch (e: Exception) {
            0
        }
    }

    override suspend fun markAsRead(notificationId: String) {
        try {
            apiService.markNotificationAsRead(notificationId)
        } catch (e: Exception) {
            // 处理错误
        }
    }

    override suspend fun markAllAsRead() {
        try {
            // TODO: 实现批量标记已读的API
            val notifications = getNotifications()
            notifications.filter { !it.isRead }.forEach { notification ->
                markAsRead(notification.id)
            }
        } catch (e: Exception) {
            // 处理错误
        }
    }

    override suspend fun deleteNotification(notificationId: String) {
        try {
            // TODO: 实现删除通知的API
        } catch (e: Exception) {
            // 处理错误
        }
    }

    override suspend fun clearAllNotifications() {
        try {
            // TODO: 实现清空所有通知的API
        } catch (e: Exception) {
            // 处理错误
        }
    }

    private fun getMockNotifications(): List<NotificationData> {
        return listOf(
            NotificationData(
                id = "notif_001",
                title = "新订单提醒",
                content = "您有一个新的洗衣订单，订单号：ORD001，请及时处理",
                type = "order",
                isRead = false,
                createdAt = Date(System.currentTimeMillis() - 300000), // 5分钟前
                relatedId = "ORD001",
                priority = "high",
                actionText = "查看订单"
            ),
            NotificationData(
                id = "notif_002",
                title = "投流余额不足",
                content = "您的推广账户余额不足100元，请及时充值以免影响推广效果",
                type = "promotion",
                isRead = false,
                createdAt = Date(System.currentTimeMillis() - 1800000), // 30分钟前
                priority = "high",
                actionText = "立即充值"
            ),
            NotificationData(
                id = "notif_003",
                title = "订单已完成",
                content = "订单ORD002已完成，客户已确认收货，款项将在24小时内到账",
                type = "order",
                isRead = true,
                createdAt = Date(System.currentTimeMillis() - 3600000), // 1小时前
                relatedId = "ORD002",
                priority = "normal",
                actionText = "查看详情"
            ),
            NotificationData(
                id = "notif_004",
                title = "提现到账通知",
                content = "您申请的提现500元已到账，请查收",
                type = "finance",
                isRead = true,
                createdAt = Date(System.currentTimeMillis() - 7200000), // 2小时前
                priority = "normal",
                actionText = "查看财务"
            ),
            NotificationData(
                id = "notif_005",
                title = "排名变化提醒",
                content = "恭喜！您的综合排名上升至第8名，继续保持优秀表现",
                type = "system",
                isRead = false,
                createdAt = Date(System.currentTimeMillis() - 10800000), // 3小时前
                priority = "normal"
            ),
            NotificationData(
                id = "notif_006",
                title = "系统维护通知",
                content = "系统将于今晚23:00-01:00进行维护，期间可能影响部分功能使用",
                type = "system",
                isRead = true,
                createdAt = Date(System.currentTimeMillis() - 86400000), // 1天前
                priority = "low"
            )
        )
    }

    private fun parseDate(dateString: String): Date {
        return try {
            // TODO: 实现日期解析
            Date()
        } catch (e: Exception) {
            Date()
        }
    }

    private fun extractRelatedId(content: String): String? {
        // 从内容中提取相关ID（如订单号）
        val orderPattern = Regex("订单号：(\\w+)")
        val match = orderPattern.find(content)
        return match?.groupValues?.get(1)
    }

    private fun determinePriority(type: String): String {
        return when (type) {
            "order" -> "high"
            "promotion" -> "high"
            "finance" -> "normal"
            "system" -> "low"
            else -> "normal"
        }
    }

    private fun getActionText(type: String): String? {
        return when (type) {
            "order" -> "查看订单"
            "finance" -> "查看财务"
            "promotion" -> "查看投流"
            else -> null
        }
    }
}
