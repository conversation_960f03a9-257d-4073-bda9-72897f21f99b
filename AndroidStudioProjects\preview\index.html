<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗护帮 - 系统预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .app-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .app-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .app-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .app-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            margin-bottom: 20px;
        }
        
        .user-app .app-icon { background: linear-gradient(45deg, #4CAF50, #45a049); }
        .merchant-app .app-icon { background: linear-gradient(45deg, #2196F3, #1976D2); }
        .admin-app .app-icon { background: linear-gradient(45deg, #FF9800, #F57C00); }
        
        .app-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .app-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .launch-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .stats-section {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .tech-stack {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .tech-stack h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        
        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .tech-tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 90%;
            max-height: 90%;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .close-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 2rem;
            cursor: pointer;
            color: #999;
        }
        
        .close-btn:hover {
            color: #333;
        }
        
        .demo-screen {
            width: 300px;
            height: 600px;
            background: #f5f5f5;
            border-radius: 30px;
            margin: 20px auto;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .demo-content {
            height: calc(100% - 80px);
            overflow-y: auto;
        }
        
        .demo-item {
            background: white;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .promotion-badge {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            display: inline-block;
            margin-left: 10px;
        }
        
        .ranking-number {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .app-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧺 洗护帮</h1>
            <p>企业级洗护服务平台 - 完整投流竞价系统</p>
        </div>
        
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">功能完成度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">应用端</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">核心功能</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">💰</div>
                    <div class="stat-label">投流竞价</div>
                </div>
            </div>
        </div>
        
        <div class="app-grid">
            <div class="app-card user-app" onclick="window.open('user-app.html', '_blank')">
                <div class="app-icon">📱</div>
                <div class="app-title">用户端应用</div>
                <div class="app-description">为用户提供便捷的洗护服务预订平台，支持智能搜索和个性化推荐</div>
                <ul class="feature-list">
                    <li>智能搜索排序（含投流）</li>
                    <li>服务预订和支付</li>
                    <li>实时订单跟踪</li>
                    <li>评价收藏系统</li>
                    <li>客服聊天功能</li>
                    <li>个性化推荐</li>
                </ul>
                <button class="launch-btn">启动用户端应用</button>
            </div>

            <div class="app-card merchant-app" onclick="window.open('merchant-app.html', '_blank')">
                <div class="app-icon">🏪</div>
                <div class="app-title">商家端应用</div>
                <div class="app-description">商家管理平台，包含完整的投流竞价系统和排行榜功能</div>
                <ul class="feature-list">
                    <li>投流充值竞价系统</li>
                    <li>竞争排行榜展示</li>
                    <li>服务发布管理</li>
                    <li>订单处理系统</li>
                    <li>财务数据统计</li>
                    <li>客户沟通工具</li>
                </ul>
                <button class="launch-btn">启动商家端应用</button>
            </div>

            <div class="app-card admin-app" onclick="window.open('admin-app.html', '_blank')">
                <div class="app-icon">⚙️</div>
                <div class="app-title">管理端系统</div>
                <div class="app-description">平台管理后台，全面控制投流系统和平台运营</div>
                <ul class="feature-list">
                    <li>投流系统控制</li>
                    <li>商家审核管理</li>
                    <li>用户行为监控</li>
                    <li>财务数据分析</li>
                    <li>违规处理系统</li>
                    <li>算法配置管理</li>
                </ul>
                <button class="launch-btn">启动管理端系统</button>
            </div>
        </div>
        
        <div class="tech-stack">
            <h3>🚀 技术栈</h3>
            <div class="tech-tags">
                <span class="tech-tag">Kotlin</span>
                <span class="tech-tag">Android Jetpack</span>
                <span class="tech-tag">MVVM</span>
                <span class="tech-tag">Retrofit</span>
                <span class="tech-tag">Room</span>
                <span class="tech-tag">Firebase</span>
                <span class="tech-tag">支付宝SDK</span>
                <span class="tech-tag">微信SDK</span>
                <span class="tech-tag">投流竞价引擎</span>
                <span class="tech-tag">智能排序算法</span>
                <span class="tech-tag">实时数据分析</span>
                <span class="tech-tag">企业级安全</span>
            </div>
        </div>
    </div>
    
    <!-- 用户端模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('userModal')">&times;</span>
            <h2>📱 用户端应用预览</h2>
            <div class="demo-screen">
                <div class="demo-header">
                    <h3>洗护帮</h3>
                    <p>找到最适合的洗护服务</p>
                </div>
                <div class="demo-content">
                    <div class="demo-item">
                        <h4>🔍 智能搜索</h4>
                        <p>搜索: "衣物洗护"</p>
                        <div style="margin-top: 10px;">
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <span class="ranking-number">1</span>
                                <span>优质洗衣店</span>
                                <span class="promotion-badge">推广</span>
                            </div>
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <span class="ranking-number">2</span>
                                <span>专业干洗店</span>
                                <span class="promotion-badge">推广</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <span class="ranking-number">3</span>
                                <span>快速洗护</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="demo-item">
                        <h4>⭐ 个性化推荐</h4>
                        <p>基于您的历史偏好推荐</p>
                        <ul style="margin-top: 10px; padding-left: 20px;">
                            <li>附近高评分商家</li>
                            <li>经常使用的服务</li>
                            <li>优惠活动推荐</li>
                        </ul>
                    </div>
                    
                    <div class="demo-item">
                        <h4>📋 我的订单</h4>
                        <p>订单状态: 服务中</p>
                        <p>预计完成: 2小时后</p>
                        <p>实时跟踪: 已取件 → 清洗中</p>
                    </div>
                    
                    <div class="demo-item">
                        <h4>💬 客服聊天</h4>
                        <p>与商家实时沟通</p>
                        <p>24小时在线客服</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 商家端模态框 -->
    <div id="merchantModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('merchantModal')">&times;</span>
            <h2>🏪 商家端应用预览</h2>
            <div class="demo-screen">
                <div class="demo-header">
                    <h3>商家管理中心</h3>
                    <p>投流竞价 · 排名提升</p>
                </div>
                <div class="demo-content">
                    <div class="demo-item">
                        <h4>💰 投流充值</h4>
                        <p>账户余额: ¥1,280.50</p>
                        <p>今日消费: ¥156.30</p>
                        <button style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 5px;">立即充值</button>
                    </div>
                    
                    <div class="demo-item">
                        <h4>🎯 推广计划</h4>
                        <p>关键词: "衣物洗护"</p>
                        <p>出价: ¥2.50/点击</p>
                        <p>排名: 第2位 ↑</p>
                        <p>今日点击: 45次</p>
                    </div>
                    
                    <div class="demo-item">
                        <h4>🏆 排行榜</h4>
                        <p>分类排名: 第3位</p>
                        <p>地区排名: 第1位 🥇</p>
                        <p>投流排名: 第2位</p>
                        <p>综合评分: 4.8⭐</p>
                    </div>
                    
                    <div class="demo-item">
                        <h4>📊 数据统计</h4>
                        <p>本月订单: 156单</p>
                        <p>投流ROI: 3.2</p>
                        <p>转化率: 12.5%</p>
                        <p>客户满意度: 98%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 管理端模态框 -->
    <div id="adminModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('adminModal')">&times;</span>
            <h2>⚙️ 管理端系统预览</h2>
            <div class="demo-screen">
                <div class="demo-header">
                    <h3>平台管理中心</h3>
                    <p>投流控制 · 数据监控</p>
                </div>
                <div class="demo-content">
                    <div class="demo-item">
                        <h4>📈 投流概览</h4>
                        <p>总投入: ¥125,680</p>
                        <p>平台收入: ¥25,136</p>
                        <p>活跃商家: 1,245家</p>
                        <p>平均CPC: ¥1.85</p>
                    </div>
                    
                    <div class="demo-item">
                        <h4>🎛️ 算法配置</h4>
                        <p>投流权重: 30%</p>
                        <p>质量权重: 40%</p>
                        <p>相关性权重: 30%</p>
                        <button style="background: #2196F3; color: white; border: none; padding: 8px 16px; border-radius: 5px;">调整算法</button>
                    </div>
                    
                    <div class="demo-item">
                        <h4>🔍 关键词管理</h4>
                        <p>总关键词: 2,856个</p>
                        <p>竞价关键词: 1,234个</p>
                        <p>平均竞价: ¥2.15</p>
                        <p>待审核: 23个</p>
                    </div>
                    
                    <div class="demo-item">
                        <h4>⚠️ 违规监控</h4>
                        <p>今日检测: 5,680次</p>
                        <p>发现违规: 3起</p>
                        <p>自动处理: 2起</p>
                        <p>人工审核: 1起</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });
        }
        
        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            // 统计数字动画
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                if (stat.textContent.includes('%')) {
                    animateNumber(stat, 0, 100, '%');
                } else if (stat.textContent.includes('+')) {
                    const num = parseInt(stat.textContent);
                    animateNumber(stat, 0, num, '+');
                }
            });
        });
        
        function animateNumber(element, start, end, suffix) {
            const duration = 2000;
            const startTime = performance.now();
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = Math.floor(start + (end - start) * progress);
                element.textContent = current + suffix;
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }
    </script>
</body>
</html>
