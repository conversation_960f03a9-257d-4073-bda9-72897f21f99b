package com.laundry.admin.ui.analytics

import android.graphics.Color
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mikephil.charting.charts.*
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.*
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.utils.ColorTemplate
import com.google.android.material.tabs.TabLayout
import com.laundry.admin.R
import com.laundry.admin.databinding.ActivityAdvancedAnalyticsBinding
import com.laundry.admin.ui.analytics.adapter.InsightAdapter
import com.laundry.admin.ui.analytics.adapter.PredictionAdapter
import com.laundry.admin.utils.formatCurrency
import com.laundry.admin.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class AdvancedAnalyticsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAdvancedAnalyticsBinding
    private val viewModel: AdvancedAnalyticsViewModel by viewModels()
    
    private lateinit var insightAdapter: InsightAdapter
    private lateinit var predictionAdapter: PredictionAdapter
    private var currentTab = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAdvancedAnalyticsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
        setupRecyclerViews()
        setupTabs()
        setupCharts()
        observeViewModel()
        
        // 加载分析数据
        viewModel.loadBusinessInsights()
    }

    private fun setupViews() {
        // 设置工具栏
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "高级数据分析"

        // 设置刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            refreshData()
        }

        // 设置时间范围选择
        binding.chipGroupTimeRange.setOnCheckedChangeListener { _, checkedId ->
            val timeRange = when (checkedId) {
                R.id.chip_7days -> "7d"
                R.id.chip_30days -> "30d"
                R.id.chip_90days -> "90d"
                R.id.chip_1year -> "1y"
                else -> "30d"
            }
            viewModel.setTimeRange(timeRange)
        }

        // 设置导出按钮
        binding.buttonExportReport.setOnClickListener {
            showExportDialog()
        }

        // 设置预测按钮
        binding.buttonGeneratePrediction.setOnClickListener {
            viewModel.generatePredictions()
        }
    }

    private fun setupRecyclerViews() {
        // 业务洞察
        insightAdapter = InsightAdapter { insight ->
            showInsightDetailDialog(insight)
        }
        
        binding.recyclerViewInsights.apply {
            layoutManager = LinearLayoutManager(this@AdvancedAnalyticsActivity)
            adapter = insightAdapter
        }

        // 预测分析
        predictionAdapter = PredictionAdapter { prediction ->
            showPredictionDetailDialog(prediction)
        }
        
        binding.recyclerViewPredictions.apply {
            layoutManager = LinearLayoutManager(this@AdvancedAnalyticsActivity)
            adapter = predictionAdapter
        }
    }

    private fun setupTabs() {
        binding.tabLayout.apply {
            addTab(newTab().setText("业务概览"))
            addTab(newTab().setText("用户分析"))
            addTab(newTab().setText("商家分析"))
            addTab(newTab().setText("预测分析"))
            addTab(newTab().setText("实时监控"))
            
            addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    currentTab = tab?.position ?: 0
                    updateTabContent(currentTab)
                }
                
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun setupCharts() {
        setupRevenueChart()
        setupUserChart()
        setupConversionChart()
        setupCohortChart()
        setupPredictionChart()
    }

    private fun setupRevenueChart() {
        binding.chartRevenueTrend.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            setDrawGridBackground(false)
            
            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                setDrawGridLines(false)
                granularity = 1f
            }
            
            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }
            
            axisRight.isEnabled = false
            legend.isEnabled = true
        }
    }

    private fun setupUserChart() {
        binding.chartUserGrowth.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            legend.isEnabled = true
        }
    }

    private fun setupConversionChart() {
        binding.chartConversionFunnel.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            legend.isEnabled = true
        }
    }

    private fun setupCohortChart() {
        binding.chartCohortAnalysis.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            legend.isEnabled = true
        }
    }

    private fun setupPredictionChart() {
        binding.chartPredictions.apply {
            description.isEnabled = false
            setTouchEnabled(true)
            isDragEnabled = true
            setScaleEnabled(true)
            setPinchZoom(true)
            legend.isEnabled = true
        }
    }

    private fun updateTabContent(tabIndex: Int) {
        // 隐藏所有内容
        binding.layoutBusinessOverview.visibility = View.GONE
        binding.layoutUserAnalysis.visibility = View.GONE
        binding.layoutMerchantAnalysis.visibility = View.GONE
        binding.layoutPredictiveAnalysis.visibility = View.GONE
        binding.layoutRealTimeMonitoring.visibility = View.GONE

        when (tabIndex) {
            0 -> {
                binding.layoutBusinessOverview.visibility = View.VISIBLE
                viewModel.loadBusinessOverview()
            }
            1 -> {
                binding.layoutUserAnalysis.visibility = View.VISIBLE
                viewModel.loadUserAnalysis()
            }
            2 -> {
                binding.layoutMerchantAnalysis.visibility = View.VISIBLE
                viewModel.loadMerchantAnalysis()
            }
            3 -> {
                binding.layoutPredictiveAnalysis.visibility = View.VISIBLE
                viewModel.loadPredictiveAnalysis()
            }
            4 -> {
                binding.layoutRealTimeMonitoring.visibility = View.VISIBLE
                viewModel.loadRealTimeMonitoring()
            }
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: AdvancedAnalyticsUiState) {
        // 更新加载状态
        binding.swipeRefreshLayout.isRefreshing = state.isLoading

        // 更新关键指标
        state.keyMetrics?.let { metrics ->
            updateKeyMetrics(metrics)
        }

        // 更新业务洞察
        insightAdapter.updateData(state.businessInsights)

        // 更新预测分析
        predictionAdapter.updateData(state.predictions)

        // 更新图表数据
        state.chartData?.let { data ->
            updateCharts(data)
        }

        // 更新用户分析
        state.userAnalysis?.let { analysis ->
            updateUserAnalysis(analysis)
        }

        // 更新商家分析
        state.merchantAnalysis?.let { analysis ->
            updateMerchantAnalysis(analysis)
        }

        // 更新实时监控
        state.realTimeData?.let { data ->
            updateRealTimeData(data)
        }

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun updateKeyMetrics(metrics: KeyMetrics) {
        binding.textViewTotalRevenue.text = metrics.totalRevenue.formatCurrency()
        binding.textViewRevenueGrowth.text = "${String.format("%.1f", metrics.revenueGrowth)}%"
        
        binding.textViewActiveUsers.text = "${metrics.activeUsers}"
        binding.textViewUserGrowth.text = "${String.format("%.1f", metrics.userGrowth)}%"
        
        binding.textViewActiveMerchants.text = "${metrics.activeMerchants}"
        binding.textViewMerchantGrowth.text = "${String.format("%.1f", metrics.merchantGrowth)}%"
        
        binding.textViewConversionRate.text = "${String.format("%.2f", metrics.conversionRate)}%"
        binding.textViewRetentionRate.text = "${String.format("%.2f", metrics.retentionRate)}%"
        
        binding.textViewAverageOrderValue.text = metrics.averageOrderValue.formatCurrency()
        binding.textViewCustomerLifetimeValue.text = metrics.customerLifetimeValue.formatCurrency()

        // 设置增长率颜色
        setGrowthColor(binding.textViewRevenueGrowth, metrics.revenueGrowth)
        setGrowthColor(binding.textViewUserGrowth, metrics.userGrowth)
        setGrowthColor(binding.textViewMerchantGrowth, metrics.merchantGrowth)
    }

    private fun updateUserAnalysis(analysis: UserAnalysis) {
        // 更新用户细分
        binding.textViewNewUsers.text = "${analysis.newUsers}"
        binding.textViewReturningUsers.text = "${analysis.returningUsers}"
        binding.textViewChurnedUsers.text = "${analysis.churnedUsers}"
        
        // 更新用户行为
        binding.textViewAverageSessionDuration.text = "${analysis.averageSessionDuration}分钟"
        binding.textViewBounceRate.text = "${String.format("%.2f", analysis.bounceRate)}%"
        binding.textViewPageViewsPerSession.text = "${String.format("%.1f", analysis.pageViewsPerSession)}"
        
        // 更新用户价值分布
        updateUserValueChart(analysis.userValueDistribution)
    }

    private fun updateMerchantAnalysis(analysis: MerchantAnalysis) {
        // 更新商家表现
        binding.textViewTopPerformingMerchants.text = "${analysis.topPerformingMerchants}"
        binding.textViewUnderperformingMerchants.text = "${analysis.underperformingMerchants}"
        binding.textViewAverageMerchantRating.text = "${String.format("%.1f", analysis.averageMerchantRating)}"
        
        // 更新商家收入分布
        binding.textViewHighRevenueMerchants.text = "${analysis.highRevenueMerchants}"
        binding.textViewMediumRevenueMerchants.text = "${analysis.mediumRevenueMerchants}"
        binding.textViewLowRevenueMerchants.text = "${analysis.lowRevenueMerchants}"
        
        // 更新商家地理分布
        updateMerchantGeographyChart(analysis.geographicDistribution)
    }

    private fun updateRealTimeData(data: RealTimeAnalyticsData) {
        binding.textViewCurrentActiveUsers.text = "${data.currentActiveUsers}"
        binding.textViewCurrentSessions.text = "${data.currentSessions}"
        binding.textViewRealtimeRevenue.text = data.realtimeRevenue.formatCurrency()
        binding.textViewRealtimeOrders.text = "${data.realtimeOrders}"
        
        binding.textViewSystemLoad.text = "${data.systemLoad}%"
        binding.textViewResponseTime.text = "${String.format("%.2f", data.responseTime)}ms"
        binding.textViewErrorRate.text = "${String.format("%.2f", data.errorRate)}%"
        
        // 更新实时图表
        updateRealTimeChart(data.realtimeMetrics)
    }

    private fun updateCharts(data: ChartData) {
        updateRevenueChart(data.revenueData)
        updateUserGrowthChart(data.userGrowthData)
        updateConversionFunnelChart(data.conversionData)
        updateCohortChart(data.cohortData)
        updatePredictionChart(data.predictionData)
    }

    private fun updateRevenueChart(revenueData: List<RevenueDataPoint>) {
        val entries = revenueData.mapIndexed { index, data ->
            Entry(index.toFloat(), data.revenue.toFloat())
        }

        val dataSet = LineDataSet(entries, "收入趋势").apply {
            color = getColor(R.color.colorPrimary)
            setCircleColor(getColor(R.color.colorPrimary))
            lineWidth = 3f
            circleRadius = 5f
            setDrawCircleHole(false)
            valueTextSize = 10f
            setDrawFilled(true)
            fillColor = getColor(R.color.colorPrimaryLight)
        }

        val lineData = LineData(dataSet)
        binding.chartRevenueTrend.apply {
            data = lineData
            xAxis.valueFormatter = IndexAxisValueFormatter(revenueData.map { it.date })
            invalidate()
        }
    }

    private fun updateUserGrowthChart(userGrowthData: List<UserGrowthDataPoint>) {
        val newUserEntries = userGrowthData.mapIndexed { index, data ->
            Entry(index.toFloat(), data.newUsers.toFloat())
        }
        
        val returningUserEntries = userGrowthData.mapIndexed { index, data ->
            Entry(index.toFloat(), data.returningUsers.toFloat())
        }

        val newUserDataSet = LineDataSet(newUserEntries, "新用户").apply {
            color = getColor(R.color.blue_500)
            setCircleColor(getColor(R.color.blue_500))
            lineWidth = 2f
            circleRadius = 4f
        }
        
        val returningUserDataSet = LineDataSet(returningUserEntries, "回访用户").apply {
            color = getColor(R.color.green_500)
            setCircleColor(getColor(R.color.green_500))
            lineWidth = 2f
            circleRadius = 4f
        }

        val lineData = LineData(newUserDataSet, returningUserDataSet)
        binding.chartUserGrowth.apply {
            data = lineData
            xAxis.valueFormatter = IndexAxisValueFormatter(userGrowthData.map { it.date })
            invalidate()
        }
    }

    private fun updateConversionFunnelChart(conversionData: List<ConversionStep>) {
        val entries = conversionData.mapIndexed { index, step ->
            BarEntry(index.toFloat(), step.count.toFloat())
        }

        val dataSet = BarDataSet(entries, "转化漏斗").apply {
            colors = ColorTemplate.MATERIAL_COLORS.toList()
            valueTextSize = 12f
        }

        val barData = BarData(dataSet)
        binding.chartConversionFunnel.apply {
            data = barData
            xAxis.valueFormatter = IndexAxisValueFormatter(conversionData.map { it.stepName })
            invalidate()
        }
    }

    private fun updateCohortChart(cohortData: List<CohortDataPoint>) {
        // 这里可以使用热力图或其他方式展示队列分析
        // 由于MPAndroidChart限制，这里简化处理
    }

    private fun updatePredictionChart(predictionData: List<PredictionDataPoint>) {
        val actualEntries = predictionData.filter { !it.isPrediction }.mapIndexed { index, data ->
            Entry(index.toFloat(), data.value.toFloat())
        }
        
        val predictionEntries = predictionData.filter { it.isPrediction }.mapIndexed { index, data ->
            Entry((actualEntries.size + index).toFloat(), data.value.toFloat())
        }

        val actualDataSet = LineDataSet(actualEntries, "实际数据").apply {
            color = getColor(R.color.colorPrimary)
            lineWidth = 2f
            setDrawCircles(false)
        }
        
        val predictionDataSet = LineDataSet(predictionEntries, "预测数据").apply {
            color = getColor(R.color.orange_500)
            lineWidth = 2f
            setDrawCircles(false)
            enableDashedLine(10f, 5f, 0f)
        }

        val lineData = LineData(actualDataSet, predictionDataSet)
        binding.chartPredictions.apply {
            data = lineData
            invalidate()
        }
    }

    private fun updateUserValueChart(distribution: Map<String, Int>) {
        // 实现用户价值分布图表
    }

    private fun updateMerchantGeographyChart(distribution: Map<String, Int>) {
        // 实现商家地理分布图表
    }

    private fun updateRealTimeChart(metrics: List<RealTimeMetric>) {
        // 实现实时指标图表
    }

    private fun setGrowthColor(textView: android.widget.TextView, growth: Double) {
        val color = if (growth >= 0) R.color.green_500 else R.color.red_500
        textView.setTextColor(getColor(color))
    }

    private fun handleEvent(event: AdvancedAnalyticsEvent) {
        when (event) {
            is AdvancedAnalyticsEvent.ShowError -> {
                showError(event.message)
            }
            is AdvancedAnalyticsEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is AdvancedAnalyticsEvent.PredictionGenerated -> {
                showToast("预测分析已生成")
            }
            is AdvancedAnalyticsEvent.ReportExported -> {
                showToast("报告已导出")
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_advanced_analytics, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_refresh -> {
                refreshData()
                true
            }
            R.id.action_settings -> {
                showAnalyticsSettingsDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showInsightDetailDialog(insight: BusinessInsight) {
        val dialog = InsightDetailDialog.newInstance(insight)
        dialog.show(supportFragmentManager, "InsightDetailDialog")
    }

    private fun showPredictionDetailDialog(prediction: Prediction) {
        val dialog = PredictionDetailDialog.newInstance(prediction)
        dialog.show(supportFragmentManager, "PredictionDetailDialog")
    }

    private fun showExportDialog() {
        val dialog = AnalyticsExportDialog.newInstance { format, timeRange ->
            viewModel.exportAnalyticsReport(format, timeRange)
        }
        dialog.show(supportFragmentManager, "AnalyticsExportDialog")
    }

    private fun showAnalyticsSettingsDialog() {
        val dialog = AnalyticsSettingsDialog.newInstance()
        dialog.show(supportFragmentManager, "AnalyticsSettingsDialog")
    }

    private fun refreshData() {
        when (currentTab) {
            0 -> viewModel.loadBusinessOverview()
            1 -> viewModel.loadUserAnalysis()
            2 -> viewModel.loadMerchantAnalysis()
            3 -> viewModel.loadPredictiveAnalysis()
            4 -> viewModel.loadRealTimeMonitoring()
        }
    }

    private fun showError(message: String) {
        showToast(message)
    }
}
