package com.laundry.merchant.ui.finance.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.laundry.merchant.R
import com.laundry.merchant.ui.finance.Transaction
import com.laundry.merchant.ui.finance.TransactionType
import java.text.SimpleDateFormat
import java.util.Locale

class TransactionAdapter(
    private val onTransactionClick: (Transaction) -> Unit
) : ListAdapter<Transaction, TransactionAdapter.ViewHolder>(TransactionDiffCallback()) {

    private val timeFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_transaction, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateData(newData: List<Transaction>) {
        submitList(newData)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val typeIconTextView: TextView = itemView.findViewById(R.id.textViewTypeIcon)
        private val descriptionTextView: TextView = itemView.findViewById(R.id.textViewDescription)
        private val categoryTextView: TextView = itemView.findViewById(R.id.textViewCategory)
        private val amountTextView: TextView = itemView.findViewById(R.id.textViewAmount)
        private val statusTextView: TextView = itemView.findViewById(R.id.textViewStatus)
        private val timeTextView: TextView = itemView.findViewById(R.id.textViewTime)

        fun bind(transaction: Transaction) {
            descriptionTextView.text = transaction.description
            timeTextView.text = timeFormat.format(transaction.createdAt)
            statusTextView.text = transaction.status.displayName
            
            // 设置分类
            categoryTextView.text = when (transaction.category) {
                "order" -> "订单"
                "promotion" -> "推广"
                "recharge" -> "充值"
                "withdraw" -> "提现"
                else -> "其他"
            }
            
            // 设置类型图标和金额
            when (transaction.type) {
                TransactionType.INCOME -> {
                    typeIconTextView.text = "+"
                    typeIconTextView.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.green_500)
                    )
                    amountTextView.text = "+¥${String.format("%.2f", transaction.amount)}"
                    amountTextView.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.green_500)
                    )
                }
                TransactionType.EXPENSE -> {
                    typeIconTextView.text = "-"
                    typeIconTextView.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.red_500)
                    )
                    amountTextView.text = "-¥${String.format("%.2f", transaction.amount)}"
                    amountTextView.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.red_500)
                    )
                }
            }
            
            // 设置状态颜色
            val statusColor = when (transaction.status.displayName) {
                "已完成" -> R.color.green_500
                "处理中" -> R.color.orange_500
                "失败" -> R.color.red_500
                "已取消" -> R.color.gray_500
                else -> R.color.gray_500
            }
            statusTextView.setTextColor(
                ContextCompat.getColor(itemView.context, statusColor)
            )
            
            itemView.setOnClickListener {
                onTransactionClick(transaction)
            }
        }
    }

    private class TransactionDiffCallback : DiffUtil.ItemCallback<Transaction>() {
        override fun areItemsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem == newItem
        }
    }
}
