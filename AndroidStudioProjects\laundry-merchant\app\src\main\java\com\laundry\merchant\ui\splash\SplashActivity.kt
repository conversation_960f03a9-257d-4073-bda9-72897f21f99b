package com.laundry.merchant.ui.splash

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.laundry.merchant.databinding.ActivitySplashBinding
import com.laundry.merchant.ui.auth.LoginActivity
import com.laundry.merchant.ui.main.MainActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@AndroidEntryPoint
class SplashActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySplashBinding
    private val viewModel: SplashViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)

        observeViewModel()
        startSplashTimer()
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.navigationEvent.collect { event ->
                when (event) {
                    is SplashNavigationEvent.NavigateToMain -> {
                        navigateToMain()
                    }
                    is SplashNavigationEvent.NavigateToLogin -> {
                        navigateToLogin()
                    }
                }
            }
        }
    }

    private fun startSplashTimer() {
        lifecycleScope.launch {
            // 显示启动画面至少2秒
            delay(2000)
            
            // 检查登录状态
            viewModel.checkLoginStatus()
        }
    }

    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }
}
