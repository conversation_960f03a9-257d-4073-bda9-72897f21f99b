package com.laundry.shared.api

import com.laundry.shared.model.*
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.*

/**
 * 真实API接口定义
 */
interface RealApiService {
    
    // ==================== 用户认证 ====================
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<ApiResponse<LoginResponse>>
    
    @POST("auth/register")
    suspend fun register(@Body request: RegisterRequest): Response<ApiResponse<RegisterResponse>>
    
    @POST("auth/refresh")
    suspend fun refreshToken(@Body request: RefreshTokenRequest): Response<ApiResponse<TokenResponse>>
    
    @POST("auth/logout")
    suspend fun logout(@Header("Authorization") token: String): Response<ApiResponse<Unit>>
    
    @POST("auth/verify-phone")
    suspend fun verifyPhone(@Body request: PhoneVerificationRequest): Response<ApiResponse<Unit>>
    
    @POST("auth/send-sms")
    suspend fun sendSmsCode(@Body request: SmsCodeRequest): Response<ApiResponse<Unit>>
    
    // ==================== 用户信息 ====================
    @GET("users/{userId}")
    suspend fun getUserProfile(@Path("userId") userId: String): Response<ApiResponse<UserProfile>>
    
    @PUT("users/{userId}")
    suspend fun updateUserProfile(
        @Path("userId") userId: String,
        @Body profile: UserProfile
    ): Response<ApiResponse<UserProfile>>
    
    @Multipart
    @POST("users/{userId}/avatar")
    suspend fun uploadAvatar(
        @Path("userId") userId: String,
        @Part avatar: MultipartBody.Part
    ): Response<ApiResponse<String>>
    
    // ==================== 服务搜索 ====================
    @GET("services/search")
    suspend fun searchServices(
        @Query("keyword") keyword: String?,
        @Query("category") category: String?,
        @Query("subCategory") subCategory: String?,
        @Query("minPrice") minPrice: Double?,
        @Query("maxPrice") maxPrice: Double?,
        @Query("minRating") minRating: Double?,
        @Query("maxDistance") maxDistance: Double?,
        @Query("latitude") latitude: Double?,
        @Query("longitude") longitude: Double?,
        @Query("sortBy") sortBy: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<LaundryService>>>
    
    @GET("services/{serviceId}")
    suspend fun getServiceDetail(@Path("serviceId") serviceId: String): Response<ApiResponse<ServiceDetail>>
    
    @GET("services/categories")
    suspend fun getServiceCategories(): Response<ApiResponse<List<ServiceCategoryInfo>>>
    
    @GET("services/suggestions")
    suspend fun getSearchSuggestions(
        @Query("keyword") keyword: String,
        @Query("type") type: String
    ): Response<ApiResponse<List<SearchSuggestion>>>
    
    @GET("services/hot-searches")
    suspend fun getHotSearches(
        @Query("type") type: String,
        @Query("limit") limit: Int
    ): Response<ApiResponse<List<HotSearch>>>
    
    @GET("services/nearby")
    suspend fun getNearbyServices(
        @Query("latitude") latitude: Double,
        @Query("longitude") longitude: Double,
        @Query("radius") radius: Double,
        @Query("category") category: String?,
        @Query("sortBy") sortBy: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<LaundryService>>>
    
    // ==================== 商家搜索 ====================
    @GET("merchants/search")
    suspend fun searchMerchants(
        @Query("keyword") keyword: String?,
        @Query("category") category: String?,
        @Query("minRating") minRating: Double?,
        @Query("maxDistance") maxDistance: Double?,
        @Query("latitude") latitude: Double?,
        @Query("longitude") longitude: Double?,
        @Query("isOpen") isOpen: Boolean?,
        @Query("features") features: String?,
        @Query("sortBy") sortBy: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<MerchantInfo>>>
    
    @GET("merchants/{merchantId}")
    suspend fun getMerchantDetail(@Path("merchantId") merchantId: String): Response<ApiResponse<MerchantDetail>>
    
    @GET("merchants/{merchantId}/services")
    suspend fun getMerchantServices(
        @Path("merchantId") merchantId: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<LaundryService>>>
    
    // ==================== 订单管理 ====================
    @POST("orders")
    suspend fun createOrder(@Body order: CreateOrderRequest): Response<ApiResponse<OrderResponse>>
    
    @GET("orders")
    suspend fun getOrders(
        @Query("userId") userId: String,
        @Query("status") status: String?,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<Order>>>
    
    @GET("orders/{orderId}")
    suspend fun getOrderDetail(@Path("orderId") orderId: String): Response<ApiResponse<OrderDetail>>
    
    @PUT("orders/{orderId}/cancel")
    suspend fun cancelOrder(
        @Path("orderId") orderId: String,
        @Body request: CancelOrderRequest
    ): Response<ApiResponse<Unit>>
    
    @PUT("orders/{orderId}/confirm")
    suspend fun confirmOrder(@Path("orderId") orderId: String): Response<ApiResponse<Unit>>
    
    @PUT("orders/{orderId}/status")
    suspend fun updateOrderStatus(
        @Path("orderId") orderId: String,
        @Body request: UpdateOrderStatusRequest
    ): Response<ApiResponse<Unit>>
    
    // ==================== 支付相关 ====================
    @POST("payments")
    suspend fun createPayment(@Body request: PaymentRequest): Response<ApiResponse<PaymentResponse>>
    
    @GET("payments/{paymentId}")
    suspend fun getPaymentStatus(@Path("paymentId") paymentId: String): Response<ApiResponse<PaymentStatus>>
    
    @GET("users/{userId}/payment-methods")
    suspend fun getPaymentMethods(@Path("userId") userId: String): Response<ApiResponse<List<PaymentMethod>>>
    
    @POST("payments/alipay")
    suspend fun createAlipayPayment(@Body request: AlipayPaymentRequest): Response<ApiResponse<AlipayPaymentResponse>>
    
    @POST("payments/wechat")
    suspend fun createWechatPayment(@Body request: WechatPaymentRequest): Response<ApiResponse<WechatPaymentResponse>>
    
    // ==================== 收藏功能 ====================
    @POST("favorites")
    suspend fun addToFavorites(@Body request: FavoriteRequest): Response<ApiResponse<Unit>>
    
    @DELETE("favorites")
    suspend fun removeFromFavorites(
        @Query("userId") userId: String,
        @Query("targetId") targetId: String,
        @Query("targetType") targetType: String
    ): Response<ApiResponse<Unit>>
    
    @GET("favorites/services")
    suspend fun getFavoriteServices(
        @Query("userId") userId: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<FavoriteService>>>
    
    @GET("favorites/merchants")
    suspend fun getFavoriteMerchants(
        @Query("userId") userId: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<FavoriteMerchant>>>
    
    @GET("favorites/check")
    suspend fun checkFavoriteStatus(
        @Query("userId") userId: String,
        @Query("targetId") targetId: String,
        @Query("targetType") targetType: String
    ): Response<ApiResponse<FavoriteStatusResponse>>
    
    @POST("favorites/batch-check")
    suspend fun batchCheckFavoriteStatus(@Body request: BatchFavoriteCheckRequest): Response<ApiResponse<BatchFavoriteCheckResponse>>
    
    @GET("favorites/statistics")
    suspend fun getFavoriteStatistics(@Query("userId") userId: String): Response<ApiResponse<FavoriteStatistics>>
    
    @DELETE("favorites/clear")
    suspend fun clearFavorites(
        @Query("userId") userId: String,
        @Query("targetType") targetType: String?
    ): Response<ApiResponse<Unit>>
    
    // ==================== 评价功能 ====================
    @POST("reviews")
    suspend fun submitOrderReview(@Body review: OrderReview): Response<ApiResponse<OrderReview>>
    
    @GET("reviews/order/{orderId}")
    suspend fun getOrderReview(
        @Path("orderId") orderId: String,
        @Query("userId") userId: String
    ): Response<ApiResponse<OrderReview>>
    
    @GET("reviews/service/{serviceId}")
    suspend fun getServiceReviews(
        @Path("serviceId") serviceId: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int,
        @Query("sortBy") sortBy: String,
        @Query("ratingFilter") ratingFilter: Int?
    ): Response<ApiResponse<PagedResponse<OrderReview>>>
    
    @GET("reviews/merchant/{merchantId}")
    suspend fun getMerchantReviews(
        @Path("merchantId") merchantId: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int,
        @Query("sortBy") sortBy: String
    ): Response<ApiResponse<PagedResponse<OrderReview>>>
    
    @GET("reviews/user/{userId}")
    suspend fun getUserReviews(
        @Path("userId") userId: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<OrderReview>>>
    
    @POST("reviews/{reviewId}/like")
    suspend fun likeReview(
        @Path("reviewId") reviewId: String,
        @Query("userId") userId: String
    ): Response<ApiResponse<Unit>>
    
    @DELETE("reviews/{reviewId}/like")
    suspend fun unlikeReview(
        @Path("reviewId") reviewId: String,
        @Query("userId") userId: String
    ): Response<ApiResponse<Unit>>
    
    @POST("reviews/{reviewId}/report")
    suspend fun reportReview(@Body request: ReviewReportRequest): Response<ApiResponse<Unit>>
    
    @GET("reviews/statistics")
    suspend fun getReviewStatistics(
        @Query("targetId") targetId: String,
        @Query("targetType") targetType: String
    ): Response<ApiResponse<ReviewStatistics>>
    
    @POST("reviews/{reviewId}/reply")
    suspend fun replyToReview(@Body reply: ReviewReply): Response<ApiResponse<ReviewReply>>
    
    @GET("reviews/tags")
    suspend fun getReviewTags(@Query("serviceCategory") serviceCategory: String): Response<ApiResponse<List<ReviewTag>>>
    
    @GET("reviews/eligibility")
    suspend fun checkReviewEligibility(
        @Query("userId") userId: String,
        @Query("orderId") orderId: String
    ): Response<ApiResponse<ReviewEligibility>>
    
    // ==================== 图片上传 ====================
    @Multipart
    @POST("upload/image")
    suspend fun uploadImage(
        @Part image: MultipartBody.Part,
        @Query("uploadType") uploadType: String,
        @Query("userId") userId: String
    ): Response<ApiResponse<ImageUploadResult>>
    
    @DELETE("upload/image")
    suspend fun deleteImage(
        @Query("imageUrl") imageUrl: String,
        @Query("userId") userId: String
    ): Response<ApiResponse<Unit>>
    
    @GET("upload/progress/{uploadId}")
    suspend fun getUploadProgress(@Path("uploadId") uploadId: String): Response<ApiResponse<UploadProgress>>
    
    // ==================== 积分会员 ====================
    @GET("points/{userId}")
    suspend fun getPointsInfo(@Path("userId") userId: String): Response<ApiResponse<PointsInfo>>
    
    @GET("membership/{userId}")
    suspend fun getMembershipInfo(@Path("userId") userId: String): Response<ApiResponse<MembershipInfo>>
    
    @POST("points/sign-in")
    suspend fun dailySignIn(@Query("userId") userId: String): Response<ApiResponse<SignInResult>>
    
    @POST("points/exchange")
    suspend fun exchangePoints(@Body exchange: PointsExchange): Response<ApiResponse<ExchangeResult>>
    
    // ==================== 优惠券 ====================
    @GET("coupons/user/{userId}")
    suspend fun getCoupons(@Path("userId") userId: String): Response<ApiResponse<List<UserCoupon>>>
    
    @POST("coupons/receive")
    suspend fun receiveCoupon(
        @Query("userId") userId: String,
        @Query("couponId") couponId: String
    ): Response<ApiResponse<Unit>>
    
    @POST("coupons/use")
    suspend fun useCoupon(
        @Query("userId") userId: String,
        @Query("couponId") couponId: String,
        @Query("orderId") orderId: String
    ): Response<ApiResponse<Unit>>
    
    // ==================== 地址管理 ====================
    @GET("addresses/user/{userId}")
    suspend fun getAddresses(@Path("userId") userId: String): Response<ApiResponse<List<UserAddress>>>
    
    @POST("addresses")
    suspend fun addAddress(@Body address: UserAddress): Response<ApiResponse<UserAddress>>
    
    @PUT("addresses/{addressId}")
    suspend fun updateAddress(
        @Path("addressId") addressId: String,
        @Body address: UserAddress
    ): Response<ApiResponse<UserAddress>>
    
    @DELETE("addresses/{addressId}")
    suspend fun deleteAddress(@Path("addressId") addressId: String): Response<ApiResponse<Unit>>
    
    // ==================== 客服相关 ====================
    @POST("customer-service/session")
    suspend fun createServiceSession(@Body request: ServiceSessionRequest): Response<ApiResponse<ServiceSession>>
    
    @POST("customer-service/message")
    suspend fun sendMessage(@Body message: ServiceMessage): Response<ApiResponse<Unit>>
    
    @GET("customer-service/session/{sessionId}/history")
    suspend fun getSessionHistory(@Path("sessionId") sessionId: String): Response<ApiResponse<List<ServiceMessage>>>
    
    @GET("customer-service/faq")
    suspend fun getFAQs(@Query("category") category: String?): Response<ApiResponse<List<FAQ>>>
    
    // ==================== 消息通知 ====================
    @GET("notifications/user/{userId}")
    suspend fun getNotifications(
        @Path("userId") userId: String,
        @Query("page") page: Int,
        @Query("pageSize") pageSize: Int
    ): Response<ApiResponse<PagedResponse<Notification>>>
    
    @PUT("notifications/{notificationId}/read")
    suspend fun markNotificationRead(@Path("notificationId") notificationId: String): Response<ApiResponse<Unit>>
    
    @PUT("notifications/settings")
    suspend fun updateNotificationSettings(@Body settings: NotificationSettings): Response<ApiResponse<Unit>>
    
    // ==================== 搜索历史 ====================
    @GET("search/history")
    suspend fun getSearchHistory(
        @Query("userId") userId: String,
        @Query("type") type: String,
        @Query("limit") limit: Int
    ): Response<ApiResponse<List<SearchHistory>>>
    
    @DELETE("search/history")
    suspend fun clearSearchHistory(
        @Query("userId") userId: String,
        @Query("type") type: String?
    ): Response<ApiResponse<Unit>>
    
    @GET("search/filter-options")
    suspend fun getFilterOptions(@Query("type") type: String): Response<ApiResponse<FilterOptions>>
    
    // ==================== 风险评估 ====================
    @GET("security/risk-assessment/{userId}")
    suspend fun getUserRiskAssessment(@Path("userId") userId: String): Response<ApiResponse<UserRiskAssessment>>
}
