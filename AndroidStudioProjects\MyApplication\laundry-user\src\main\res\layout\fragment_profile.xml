<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.profile.ProfileFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- User Info Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/image_avatar"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:background="@drawable/circle_background"
                    android:padding="16dp"
                    android:src="@drawable/ic_person_24"
                    app:tint="@color/primary_color" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/text_user_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="张三"
                        android:textColor="@color/primary_text"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/text_user_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="13800138000"
                        android:textColor="@color/secondary_text"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/text_user_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="<EMAIL>"
                        android:textColor="@color/secondary_text"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Menu Items -->
        <LinearLayout
            android:id="@+id/layout_address_management"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_location_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="地址管理"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_order_history"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_history_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="订单历史"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_settings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_settings_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="设置"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_help_support"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_help_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="帮助与支持"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <LinearLayout
            android:id="@+id/layout_about"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_info_24"
                app:tint="@color/primary_color" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="关于"
                android:textColor="@color/primary_text"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_right_24"
                app:tint="@color/secondary_text" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color" />

        <!-- Logout Button -->
        <Button
            android:id="@+id/button_logout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:background="@drawable/button_outline"
            android:text="退出登录"
            android:textColor="@color/error_color"
            android:textSize="16sp" />

    </LinearLayout>

</ScrollView>
