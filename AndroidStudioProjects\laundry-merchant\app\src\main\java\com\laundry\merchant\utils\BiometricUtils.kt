package com.laundry.merchant.utils

import android.content.Context
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.laundry.merchant.R

object BiometricUtils {

    /**
     * 检查设备是否支持生物识别
     */
    fun isBiometricAvailable(context: Context): BiometricAvailability {
        val biometricManager = BiometricManager.from(context)
        return when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
            BiometricManager.BIOMETRIC_SUCCESS -> BiometricAvailability.AVAILABLE
            BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> BiometricAvailability.NO_HARDWARE
            BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> BiometricAvailability.HARDWARE_UNAVAILABLE
            BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> BiometricAvailability.NONE_ENROLLED
            BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED -> BiometricAvailability.SECURITY_UPDATE_REQUIRED
            BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED -> BiometricAvailability.UNSUPPORTED
            BiometricManager.BIOMETRIC_STATUS_UNKNOWN -> BiometricAvailability.STATUS_UNKNOWN
            else -> BiometricAvailability.NOT_AVAILABLE
        }
    }

    /**
     * 显示生物识别认证对话框
     */
    fun showBiometricPrompt(
        activity: FragmentActivity,
        title: String = activity.getString(R.string.biometric_prompt_title),
        subtitle: String = activity.getString(R.string.biometric_prompt_subtitle),
        description: String = activity.getString(R.string.biometric_prompt_description),
        negativeButtonText: String = activity.getString(R.string.cancel),
        onSuccess: () -> Unit,
        onError: (String) -> Unit,
        onFailed: () -> Unit
    ) {
        val executor = ContextCompat.getMainExecutor(activity)
        val biometricPrompt = BiometricPrompt(activity, executor, object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                when (errorCode) {
                    BiometricPrompt.ERROR_USER_CANCELED,
                    BiometricPrompt.ERROR_NEGATIVE_BUTTON -> {
                        // 用户取消，不显示错误
                    }
                    else -> {
                        onError(errString.toString())
                    }
                }
            }

            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                onSuccess()
            }

            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                onFailed()
            }
        })

        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle(title)
            .setSubtitle(subtitle)
            .setDescription(description)
            .setNegativeButtonText(negativeButtonText)
            .setAllowedAuthenticators(BiometricManager.Authenticators.BIOMETRIC_WEAK)
            .build()

        biometricPrompt.authenticate(promptInfo)
    }

    /**
     * 获取生物识别可用性描述
     */
    fun getBiometricAvailabilityMessage(context: Context, availability: BiometricAvailability): String {
        return when (availability) {
            BiometricAvailability.AVAILABLE -> ""
            BiometricAvailability.NO_HARDWARE -> context.getString(R.string.biometric_not_available)
            BiometricAvailability.HARDWARE_UNAVAILABLE -> context.getString(R.string.biometric_not_available)
            BiometricAvailability.NONE_ENROLLED -> context.getString(R.string.biometric_not_enrolled)
            BiometricAvailability.SECURITY_UPDATE_REQUIRED -> "需要安全更新"
            BiometricAvailability.UNSUPPORTED -> context.getString(R.string.biometric_not_available)
            BiometricAvailability.STATUS_UNKNOWN -> "生物识别状态未知"
            BiometricAvailability.NOT_AVAILABLE -> context.getString(R.string.biometric_not_available)
        }
    }

    /**
     * 检查是否可以使用生物识别
     */
    fun canUseBiometric(context: Context): Boolean {
        return isBiometricAvailable(context) == BiometricAvailability.AVAILABLE
    }

    /**
     * 获取生物识别类型描述
     */
    fun getBiometricTypeDescription(context: Context): String {
        val biometricManager = BiometricManager.from(context)
        return when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
            BiometricManager.BIOMETRIC_SUCCESS -> {
                // 这里可以进一步检测具体的生物识别类型
                // 由于Android API限制，无法直接获取具体类型，返回通用描述
                "指纹或面容识别"
            }
            else -> ""
        }
    }
}

enum class BiometricAvailability {
    AVAILABLE,
    NO_HARDWARE,
    HARDWARE_UNAVAILABLE,
    NONE_ENROLLED,
    SECURITY_UPDATE_REQUIRED,
    UNSUPPORTED,
    STATUS_UNKNOWN,
    NOT_AVAILABLE
}
