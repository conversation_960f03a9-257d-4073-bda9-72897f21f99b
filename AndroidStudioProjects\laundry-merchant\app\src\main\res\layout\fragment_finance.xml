<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/swipeRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 账户概览 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/gradient_background"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="账户余额"
                        android:textColor="@android:color/white"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/textAccountBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="¥3,560.80"
                        android:textColor="@android:color/white"
                        android:textSize="28sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/buttonRecharge"
                            android:layout_width="0dp"
                            android:layout_height="36dp"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:background="@drawable/button_white_background"
                            android:text="充值"
                            android:textColor="@color/colorPrimary"
                            android:textSize="14sp" />

                        <Button
                            android:id="@+id/buttonWithdraw"
                            android:layout_width="0dp"
                            android:layout_height="36dp"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:background="@drawable/button_white_background"
                            android:text="提现"
                            android:textColor="@color/colorPrimary"
                            android:textSize="14sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 推广账户 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="推广余额"
                                android:textColor="@color/gray_600"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/textPromotionBalance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="¥1,280.50"
                                android:textColor="@color/orange_500"
                                android:textSize="20sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <Button
                            android:id="@+id/buttonPromotionRecharge"
                            android:layout_width="wrap_content"
                            android:layout_height="36dp"
                            android:background="@drawable/button_primary_background"
                            android:paddingHorizontal="16dp"
                            android:text="充值"
                            android:textColor="@android:color/white"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/textFrozenAmount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="冻结金额: ¥0.00"
                        android:textColor="@color/gray_500"
                        android:textSize="12sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 收支统计 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardTodayIncome"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/textTodayIncome"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥580"
                            android:textColor="@color/green_500"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="今日收入"
                            android:textColor="@color/gray_600"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/cardMonthIncome"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:id="@+id/textMonthIncome"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥7,850"
                            android:textColor="@color/blue_500"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="月度收入"
                            android:textColor="@color/gray_600"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- 交易记录标签 -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="@android:color/white"
                app:tabIndicatorColor="@color/colorPrimary"
                app:tabSelectedTextColor="@color/colorPrimary"
                app:tabTextColor="@color/gray_600" />

            <!-- 交易记录列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewTransactions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:nestedScrollingEnabled="false" />

            <!-- 空状态 -->
            <LinearLayout
                android:id="@+id/emptyView"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:alpha="0.5"
                    android:src="@drawable/ic_empty_transactions"
                    android:tint="@color/gray_400" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="暂无交易记录"
                    android:textColor="@color/gray_500"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
