package com.laundry.admin.data.model

import java.util.Date

// 管理员数据模型
data class Admin(
    val id: String,
    val username: String,
    val email: String,
    val role: AdminRole,
    val permissions: List<Permission>,
    val isActive: Boolean,
    val lastLoginTime: Date?,
    val createdAt: Date,
    val updatedAt: Date
)

enum class AdminRole {
    SUPER_ADMIN,    // 超级管理员
    ADMIN,          // 管理员
    MODERATOR,      // 审核员
    OPERATOR,       // 运营人员
    FINANCE,        // 财务人员
    CUSTOMER_SERVICE // 客服人员
}

data class Permission(
    val id: String,
    val name: String,
    val description: String,
    val module: String,
    val actions: List<String>
)

// 仪表板数据模型
data class DashboardOverview(
    val totalUsers: Int,
    val activeUsers: Int,
    val newUsersToday: Int,
    val totalMerchants: Int,
    val activeMerchants: Int,
    val pendingMerchants: Int,
    val totalOrders: Int,
    val pendingOrders: Int,
    val completedOrders: Int,
    val totalRevenue: Double,
    val platformRevenue: Double,
    val todayRevenue: Double
)

data class UserStatistics(
    val newUsers: Int,
    val activeUsers: Int,
    val growthRate: Double,
    val retentionRate: Double,
    val averageSessionTime: Long
)

data class MerchantStatistics(
    val newMerchants: Int,
    val activeMerchants: Int,
    val pendingApprovals: Int,
    val growthRate: Double,
    val averageRating: Float
)

data class OrderStatistics(
    val totalOrders: Int,
    val completedOrders: Int,
    val cancelledOrders: Int,
    val averageOrderValue: Double,
    val completionRate: Double,
    val growthRate: Double
)

data class QuickStat(
    val title: String,
    val value: String,
    val change: String,
    val changeType: ChangeType,
    val icon: String
)

enum class ChangeType {
    INCREASE,
    DECREASE,
    NEUTRAL
}

// 系统告警模型
data class SystemAlert(
    val id: String,
    val type: String,
    val title: String,
    val message: String,
    val severity: String,
    val timestamp: Long,
    val isRead: Boolean = false,
    val relatedId: String? = null
)

data class SystemStatus(
    val healthStatus: String,
    val serverLoad: Int,
    val memoryUsage: Int,
    val diskUsage: Int,
    val activeConnections: Int,
    val responseTime: Double
)

// 用户管理模型
data class UserManagement(
    val user: User,
    val status: UserStatus,
    val riskLevel: RiskLevel,
    val violations: List<Violation>,
    val orders: List<OrderSummary>,
    val totalSpent: Double,
    val lastActivity: Date
)

enum class UserStatus {
    ACTIVE,
    SUSPENDED,
    BANNED,
    PENDING_VERIFICATION
}

enum class RiskLevel {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}

data class Violation(
    val id: String,
    val type: ViolationType,
    val description: String,
    val severity: ViolationSeverity,
    val timestamp: Date,
    val status: ViolationStatus,
    val handledBy: String? = null,
    val resolution: String? = null
)

enum class ViolationType {
    FAKE_REVIEW,
    PAYMENT_FRAUD,
    SPAM_BEHAVIOR,
    INAPPROPRIATE_CONTENT,
    POLICY_VIOLATION
}

enum class ViolationSeverity {
    MINOR,
    MODERATE,
    SEVERE,
    CRITICAL
}

enum class ViolationStatus {
    PENDING,
    INVESTIGATING,
    RESOLVED,
    DISMISSED
}

// 商家管理模型
data class MerchantManagement(
    val merchant: Merchant,
    val approvalStatus: ApprovalStatus,
    val documents: List<Document>,
    val businessMetrics: BusinessMetrics,
    val violations: List<Violation>,
    val rating: MerchantRating
)

enum class ApprovalStatus {
    PENDING,
    UNDER_REVIEW,
    APPROVED,
    REJECTED,
    SUSPENDED
}

data class Document(
    val id: String,
    val type: DocumentType,
    val fileName: String,
    val fileUrl: String,
    val uploadTime: Date,
    val verificationStatus: VerificationStatus,
    val verifiedBy: String? = null,
    val verificationTime: Date? = null,
    val notes: String? = null
)

enum class DocumentType {
    BUSINESS_LICENSE,
    ID_CARD,
    BANK_ACCOUNT,
    TAX_CERTIFICATE,
    QUALIFICATION_CERTIFICATE
}

enum class VerificationStatus {
    PENDING,
    VERIFIED,
    REJECTED,
    EXPIRED
}

data class BusinessMetrics(
    val totalOrders: Int,
    val completedOrders: Int,
    val totalRevenue: Double,
    val averageRating: Float,
    val responseTime: Double,
    val completionRate: Double
)

data class MerchantRating(
    val overallRating: Float,
    val serviceQuality: Float,
    val responseSpeed: Float,
    val professionalism: Float,
    val totalReviews: Int
)

// 订单管理模型
data class OrderManagement(
    val order: Order,
    val customer: User,
    val merchant: Merchant,
    val timeline: List<OrderEvent>,
    val disputes: List<Dispute>,
    val refunds: List<RefundRequest>
)

data class OrderEvent(
    val id: String,
    val orderId: String,
    val eventType: OrderEventType,
    val description: String,
    val timestamp: Date,
    val actor: String,
    val metadata: Map<String, String> = emptyMap()
)

enum class OrderEventType {
    CREATED,
    PAID,
    CONFIRMED,
    PICKED_UP,
    IN_PROGRESS,
    COMPLETED,
    CANCELLED,
    REFUNDED,
    DISPUTED
}

data class Dispute(
    val id: String,
    val orderId: String,
    val type: DisputeType,
    val description: String,
    val evidence: List<Evidence>,
    val status: DisputeStatus,
    val resolution: String? = null,
    val createdAt: Date,
    val resolvedAt: Date? = null
)

enum class DisputeType {
    SERVICE_QUALITY,
    DELIVERY_ISSUE,
    PAYMENT_DISPUTE,
    DAMAGE_CLAIM,
    OTHER
}

enum class DisputeStatus {
    PENDING,
    INVESTIGATING,
    RESOLVED,
    ESCALATED
}

data class Evidence(
    val id: String,
    val type: EvidenceType,
    val content: String,
    val fileUrl: String? = null,
    val timestamp: Date
)

enum class EvidenceType {
    TEXT,
    IMAGE,
    VIDEO,
    DOCUMENT
}

// 财务管理模型
data class FinancialOverview(
    val totalRevenue: Double,
    val platformRevenue: Double,
    val merchantRevenue: Double,
    val pendingPayouts: Double,
    val refundAmount: Double,
    val transactionFees: Double
)

data class PayoutRequest(
    val id: String,
    val merchantId: String,
    val merchantName: String,
    val amount: Double,
    val bankAccount: String,
    val status: PayoutStatus,
    val requestTime: Date,
    val processedTime: Date? = null,
    val notes: String? = null
)

enum class PayoutStatus {
    PENDING,
    APPROVED,
    PROCESSING,
    COMPLETED,
    FAILED,
    CANCELLED
}

data class TransactionRecord(
    val id: String,
    val type: TransactionType,
    val amount: Double,
    val fee: Double,
    val netAmount: Double,
    val status: TransactionStatus,
    val timestamp: Date,
    val relatedOrderId: String? = null,
    val description: String
)

enum class TransactionType {
    ORDER_PAYMENT,
    REFUND,
    PAYOUT,
    FEE,
    ADJUSTMENT
}

enum class TransactionStatus {
    PENDING,
    COMPLETED,
    FAILED,
    CANCELLED
}

// 系统配置模型
data class SystemConfig(
    val id: String,
    val category: String,
    val key: String,
    val value: String,
    val description: String,
    val dataType: ConfigDataType,
    val isEditable: Boolean,
    val updatedBy: String,
    val updatedAt: Date
)

enum class ConfigDataType {
    STRING,
    INTEGER,
    DOUBLE,
    BOOLEAN,
    JSON
}

data class OperationLog(
    val id: String,
    val adminId: String,
    val adminName: String,
    val action: String,
    val module: String,
    val targetId: String? = null,
    val details: String,
    val ipAddress: String,
    val userAgent: String,
    val timestamp: Date
)

// 通用模型
data class User(
    val id: String,
    val username: String,
    val email: String,
    val phone: String,
    val avatar: String? = null,
    val isActive: Boolean,
    val createdAt: Date
)

data class Merchant(
    val id: String,
    val name: String,
    val businessName: String,
    val contactPerson: String,
    val phone: String,
    val email: String,
    val address: String,
    val isActive: Boolean,
    val createdAt: Date
)

data class Order(
    val id: String,
    val customerId: String,
    val merchantId: String,
    val serviceType: String,
    val amount: Double,
    val status: String,
    val createdAt: Date
)

data class OrderSummary(
    val orderId: String,
    val amount: Double,
    val status: String,
    val createdAt: Date
)

data class RefundRequest(
    val id: String,
    val orderId: String,
    val amount: Double,
    val reason: String,
    val status: String,
    val requestTime: Date
)
