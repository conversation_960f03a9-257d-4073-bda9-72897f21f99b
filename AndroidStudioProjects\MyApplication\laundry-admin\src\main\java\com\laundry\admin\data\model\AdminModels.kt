package com.laundry.admin.data.model

import java.util.Date

// 管理员数据模型
data class Admin(
    val id: String,
    val username: String,
    val email: String,
    val role: AdminRole,
    val permissions: List<Permission>,
    val isActive: Boolean,
    val lastLoginTime: Date?,
    val createdAt: Date,
    val updatedAt: Date
)

enum class AdminRole {
    SUPER_ADMIN,    // 超级管理员
    ADMIN,          // 管理员
    MODERATOR,      // 审核员
    OPERATOR,       // 运营人员
    FINANCE,        // 财务人员
    CUSTOMER_SERVICE // 客服人员
}

data class Permission(
    val id: String,
    val name: String,
    val description: String,
    val module: String,
    val actions: List<String>
)

// 仪表板数据模型
data class DashboardOverview(
    val totalUsers: Int,
    val activeUsers: Int,
    val newUsersToday: Int,
    val totalMerchants: Int,
    val activeMerchants: Int,
    val pendingMerchants: Int,
    val totalOrders: Int,
    val pendingOrders: Int,
    val completedOrders: Int,
    val totalRevenue: Double,
    val platformRevenue: Double,
    val todayRevenue: Double
)

data class UserStatistics(
    val newUsers: Int,
    val activeUsers: Int,
    val growthRate: Double,
    val retentionRate: Double,
    val averageSessionTime: Long
)

data class MerchantStatistics(
    val newMerchants: Int,
    val activeMerchants: Int,
    val pendingApprovals: Int,
    val growthRate: Double,
    val averageRating: Float
)

data class OrderStatistics(
    val totalOrders: Int,
    val completedOrders: Int,
    val cancelledOrders: Int,
    val averageOrderValue: Double,
    val completionRate: Double,
    val growthRate: Double
)

data class QuickStat(
    val title: String,
    val value: String,
    val change: String,
    val changeType: ChangeType,
    val icon: String
)

enum class ChangeType {
    INCREASE,
    DECREASE,
    NEUTRAL
}

// 系统告警模型
data class SystemAlert(
    val id: String,
    val type: String,
    val title: String,
    val message: String,
    val severity: String,
    val timestamp: Long,
    val isRead: Boolean = false,
    val relatedId: String? = null
)

data class SystemStatus(
    val healthStatus: String,
    val serverLoad: Int,
    val memoryUsage: Int,
    val diskUsage: Int,
    val activeConnections: Int,
    val responseTime: Double
)

// 用户管理模型
data class UserManagement(
    val user: User,
    val status: UserStatus,
    val riskLevel: RiskLevel,
    val violations: List<Violation>,
    val orders: List<OrderSummary>,
    val totalSpent: Double,
    val lastActivity: Date
)

enum class UserStatus {
    ACTIVE,
    SUSPENDED,
    BANNED,
    PENDING_VERIFICATION
}

enum class RiskLevel {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}

data class Violation(
    val id: String,
    val type: ViolationType,
    val description: String,
    val severity: ViolationSeverity,
    val timestamp: Date,
    val status: ViolationStatus,
    val handledBy: String? = null,
    val resolution: String? = null
)

enum class ViolationType {
    FAKE_REVIEW,
    PAYMENT_FRAUD,
    SPAM_BEHAVIOR,
    INAPPROPRIATE_CONTENT,
    POLICY_VIOLATION
}

enum class ViolationSeverity {
    MINOR,
    MODERATE,
    SEVERE,
    CRITICAL
}

enum class ViolationStatus {
    PENDING,
    INVESTIGATING,
    RESOLVED,
    DISMISSED
}

// 商家管理模型
data class MerchantManagement(
    val merchant: Merchant,
    val approvalStatus: ApprovalStatus,
    val documents: List<Document>,
    val businessMetrics: BusinessMetrics,
    val violations: List<Violation>,
    val rating: MerchantRating
)

enum class ApprovalStatus {
    PENDING,
    UNDER_REVIEW,
    APPROVED,
    REJECTED,
    SUSPENDED
}

data class Document(
    val id: String,
    val type: DocumentType,
    val fileName: String,
    val fileUrl: String,
    val uploadTime: Date,
    val verificationStatus: VerificationStatus,
    val verifiedBy: String? = null,
    val verificationTime: Date? = null,
    val notes: String? = null
)

enum class DocumentType {
    BUSINESS_LICENSE,
    ID_CARD,
    BANK_ACCOUNT,
    TAX_CERTIFICATE,
    QUALIFICATION_CERTIFICATE
}

enum class VerificationStatus {
    PENDING,
    VERIFIED,
    REJECTED,
    EXPIRED
}

data class BusinessMetrics(
    val totalOrders: Int,
    val completedOrders: Int,
    val totalRevenue: Double,
    val averageRating: Float,
    val responseTime: Double,
    val completionRate: Double
)

data class MerchantRating(
    val overallRating: Float,
    val serviceQuality: Float,
    val responseSpeed: Float,
    val professionalism: Float,
    val totalReviews: Int
)

// 订单管理模型
data class OrderManagement(
    val order: Order,
    val customer: User,
    val merchant: Merchant,
    val timeline: List<OrderEvent>,
    val disputes: List<Dispute>,
    val refunds: List<RefundRequest>
)

data class OrderEvent(
    val id: String,
    val orderId: String,
    val eventType: OrderEventType,
    val description: String,
    val timestamp: Date,
    val actor: String,
    val metadata: Map<String, String> = emptyMap()
)

enum class OrderEventType {
    CREATED,
    PAID,
    CONFIRMED,
    PICKED_UP,
    IN_PROGRESS,
    COMPLETED,
    CANCELLED,
    REFUNDED,
    DISPUTED
}

data class Dispute(
    val id: String,
    val orderId: String,
    val type: DisputeType,
    val description: String,
    val evidence: List<Evidence>,
    val status: DisputeStatus,
    val resolution: String? = null,
    val createdAt: Date,
    val resolvedAt: Date? = null
)

enum class DisputeType {
    SERVICE_QUALITY,
    DELIVERY_ISSUE,
    PAYMENT_DISPUTE,
    DAMAGE_CLAIM,
    OTHER
}

enum class DisputeStatus {
    PENDING,
    INVESTIGATING,
    RESOLVED,
    ESCALATED
}

data class Evidence(
    val id: String,
    val type: EvidenceType,
    val content: String,
    val fileUrl: String? = null,
    val timestamp: Date
)

enum class EvidenceType {
    TEXT,
    IMAGE,
    VIDEO,
    DOCUMENT
}

// 财务管理模型
data class FinancialOverview(
    val totalRevenue: Double,
    val platformRevenue: Double,
    val merchantRevenue: Double,
    val pendingPayouts: Double,
    val refundAmount: Double,
    val transactionFees: Double
)

data class PayoutRequest(
    val id: String,
    val merchantId: String,
    val merchantName: String,
    val amount: Double,
    val bankAccount: String,
    val status: PayoutStatus,
    val requestTime: Date,
    val processedTime: Date? = null,
    val notes: String? = null
)

enum class PayoutStatus {
    PENDING,
    APPROVED,
    PROCESSING,
    COMPLETED,
    FAILED,
    CANCELLED
}

data class TransactionRecord(
    val id: String,
    val type: TransactionType,
    val amount: Double,
    val fee: Double,
    val netAmount: Double,
    val status: TransactionStatus,
    val timestamp: Date,
    val relatedOrderId: String? = null,
    val description: String
)

enum class TransactionType {
    ORDER_PAYMENT,
    REFUND,
    PAYOUT,
    FEE,
    ADJUSTMENT
}

enum class TransactionStatus {
    PENDING,
    COMPLETED,
    FAILED,
    CANCELLED
}

// 系统配置模型
data class SystemConfig(
    val id: String,
    val category: String,
    val key: String,
    val value: String,
    val description: String,
    val dataType: ConfigDataType,
    val isEditable: Boolean,
    val updatedBy: String,
    val updatedAt: Date
)

enum class ConfigDataType {
    STRING,
    INTEGER,
    DOUBLE,
    BOOLEAN,
    JSON
}

data class OperationLog(
    val id: String,
    val adminId: String,
    val adminName: String,
    val action: String,
    val module: String,
    val targetId: String? = null,
    val details: String,
    val ipAddress: String,
    val userAgent: String,
    val timestamp: Date
)

// 通用模型
data class User(
    val id: String,
    val username: String,
    val email: String,
    val phone: String,
    val avatar: String? = null,
    val isActive: Boolean,
    val createdAt: Date
)

data class Merchant(
    val id: String,
    val name: String,
    val businessName: String,
    val contactPerson: String,
    val phone: String,
    val email: String,
    val address: String,
    val isActive: Boolean,
    val createdAt: Date
)

data class Order(
    val id: String,
    val customerId: String,
    val merchantId: String,
    val serviceType: String,
    val amount: Double,
    val status: String,
    val createdAt: Date
)

data class OrderSummary(
    val orderId: String,
    val amount: Double,
    val status: String,
    val createdAt: Date
)

data class RefundRequest(
    val id: String,
    val orderId: String,
    val amount: Double,
    val reason: String,
    val status: String,
    val requestTime: Date
)

// 运营工具模型
data class Campaign(
    val id: String,
    val name: String,
    val type: CampaignType,
    val description: String,
    val targetAudience: TargetAudience,
    val budget: Double,
    val startDate: Date,
    val endDate: Date,
    val status: CampaignStatus,
    val metrics: CampaignMetrics,
    val createdBy: String,
    val createdAt: Date
)

enum class CampaignType {
    PROMOTION,
    DISCOUNT,
    CASHBACK,
    REFERRAL,
    SEASONAL,
    FLASH_SALE
}

data class TargetAudience(
    val userSegments: List<String>,
    val geographicRegions: List<String>,
    val ageRange: Pair<Int, Int>?,
    val membershipLevels: List<String>,
    val behaviorCriteria: List<String>
)

enum class CampaignStatus {
    DRAFT,
    SCHEDULED,
    ACTIVE,
    PAUSED,
    COMPLETED,
    CANCELLED
}

data class CampaignMetrics(
    val impressions: Long,
    val clicks: Long,
    val conversions: Long,
    val revenue: Double,
    val cost: Double,
    val roi: Double,
    val ctr: Double,
    val conversionRate: Double
)

data class PushNotification(
    val id: String,
    val title: String,
    val message: String,
    val type: NotificationType,
    val targetAudience: TargetAudience,
    val scheduledTime: Date?,
    val status: NotificationStatus,
    val metrics: NotificationMetrics,
    val createdBy: String,
    val createdAt: Date
)

enum class NotificationType {
    PROMOTIONAL,
    TRANSACTIONAL,
    REMINDER,
    ALERT,
    ANNOUNCEMENT
}

enum class NotificationStatus {
    DRAFT,
    SCHEDULED,
    SENT,
    FAILED,
    CANCELLED
}

data class NotificationMetrics(
    val sent: Long,
    val delivered: Long,
    val opened: Long,
    val clicked: Long,
    val deliveryRate: Double,
    val openRate: Double,
    val clickRate: Double
)

data class ABTest(
    val id: String,
    val name: String,
    val description: String,
    val hypothesis: String,
    val variants: List<ABTestVariant>,
    val trafficSplit: Map<String, Double>,
    val metrics: List<String>,
    val startDate: Date,
    val endDate: Date?,
    val status: ABTestStatus,
    val results: ABTestResults?,
    val createdBy: String,
    val createdAt: Date
)

data class ABTestVariant(
    val id: String,
    val name: String,
    val description: String,
    val configuration: Map<String, Any>
)

enum class ABTestStatus {
    DRAFT,
    RUNNING,
    PAUSED,
    COMPLETED,
    CANCELLED
}

data class ABTestResults(
    val variantResults: Map<String, VariantResult>,
    val winningVariant: String?,
    val confidenceLevel: Double,
    val statisticalSignificance: Boolean,
    val summary: String
)

data class VariantResult(
    val participants: Long,
    val conversions: Long,
    val conversionRate: Double,
    val revenue: Double,
    val metrics: Map<String, Double>
)

data class Content(
    val id: String,
    val title: String,
    val type: ContentType,
    val content: String,
    val mediaUrls: List<String>,
    val tags: List<String>,
    val status: ContentStatus,
    val publishDate: Date?,
    val expiryDate: Date?,
    val targetAudience: TargetAudience?,
    val metrics: ContentMetrics,
    val createdBy: String,
    val createdAt: Date,
    val updatedAt: Date
)

enum class ContentType {
    ARTICLE,
    BANNER,
    VIDEO,
    ANNOUNCEMENT,
    HELP_DOCUMENT,
    TERMS_CONDITIONS,
    PRIVACY_POLICY
}

enum class ContentStatus {
    DRAFT,
    REVIEW,
    PUBLISHED,
    ARCHIVED,
    EXPIRED
}

data class ContentMetrics(
    val views: Long,
    val likes: Long,
    val shares: Long,
    val comments: Long,
    val engagementRate: Double
)

// 高级分析模型
data class BusinessInsight(
    val id: String,
    val title: String,
    val description: String,
    val type: InsightType,
    val severity: InsightSeverity,
    val metrics: Map<String, Double>,
    val recommendations: List<String>,
    val generatedAt: Date
)

enum class InsightType {
    REVENUE_TREND,
    USER_BEHAVIOR,
    MERCHANT_PERFORMANCE,
    MARKET_OPPORTUNITY,
    RISK_ALERT,
    OPERATIONAL_EFFICIENCY
}

enum class InsightSeverity {
    INFO,
    WARNING,
    CRITICAL,
    OPPORTUNITY
}

data class Prediction(
    val id: String,
    val type: PredictionType,
    val title: String,
    val description: String,
    val predictedValue: Double,
    val confidence: Double,
    val timeframe: String,
    val factors: List<String>,
    val generatedAt: Date
)

enum class PredictionType {
    REVENUE_FORECAST,
    USER_GROWTH,
    CHURN_PREDICTION,
    DEMAND_FORECAST,
    MARKET_TREND
)

data class KeyMetrics(
    val totalRevenue: Double,
    val revenueGrowth: Double,
    val activeUsers: Int,
    val userGrowth: Double,
    val activeMerchants: Int,
    val merchantGrowth: Double,
    val conversionRate: Double,
    val retentionRate: Double,
    val averageOrderValue: Double,
    val customerLifetimeValue: Double
)

data class UserAnalysis(
    val newUsers: Int,
    val returningUsers: Int,
    val churnedUsers: Int,
    val averageSessionDuration: Double,
    val bounceRate: Double,
    val pageViewsPerSession: Double,
    val userValueDistribution: Map<String, Int>
)

data class MerchantAnalysis(
    val topPerformingMerchants: Int,
    val underperformingMerchants: Int,
    val averageMerchantRating: Double,
    val highRevenueMerchants: Int,
    val mediumRevenueMerchants: Int,
    val lowRevenueMerchants: Int,
    val geographicDistribution: Map<String, Int>
)

data class RealTimeAnalyticsData(
    val currentActiveUsers: Int,
    val currentSessions: Int,
    val realtimeRevenue: Double,
    val realtimeOrders: Int,
    val systemLoad: Double,
    val responseTime: Double,
    val errorRate: Double,
    val realtimeMetrics: List<RealTimeMetric>
)

data class RealTimeMetric(
    val timestamp: Long,
    val metricName: String,
    val value: Double
)

// 系统配置和监控模型
data class SystemMonitoringData(
    val totalApiCalls: Long,
    val apiErrors: Long,
    val apiSuccessRate: Double,
    val dbConnections: Int,
    val dbQueries: Long,
    val dbResponseTime: Double,
    val cacheHitRate: Double,
    val cacheSize: Double,
    val errorRate: Double,
    val criticalErrors: Int
)

data class VersionInfo(
    val currentVersion: String,
    val latestVersion: String,
    val updateAvailable: Boolean,
    val lastUpdateTime: String,
    val isChecking: Boolean,
    val isUpdating: Boolean
)

// 财务管理扩展模型
data class RiskMetrics(
    val suspiciousTransactions: Int,
    val highRiskMerchants: Int,
    val chargebackRate: Double,
    val fraudScore: Double
)

data class RevenueData(
    val date: String,
    val amount: Double
)

// 运营统计模型
data class OperationStats(
    val activeCampaigns: Int,
    val totalReach: Long,
    val conversionRate: Double,
    val engagementRate: Double,
    val pendingNotifications: Int,
    val sentNotifications: Int,
    val notificationOpenRate: Double,
    val runningABTests: Int,
    val completedABTests: Int,
    val publishedContent: Int,
    val draftContent: Int
)

// 图表数据模型
data class ChartData(
    val revenueData: List<RevenueDataPoint>,
    val userGrowthData: List<UserGrowthDataPoint>,
    val conversionData: List<ConversionStep>,
    val cohortData: List<CohortDataPoint>,
    val predictionData: List<PredictionDataPoint>
)

data class RevenueDataPoint(
    val date: String,
    val revenue: Double
)

data class UserGrowthDataPoint(
    val date: String,
    val newUsers: Int,
    val returningUsers: Int
)

data class ConversionStep(
    val stepName: String,
    val count: Int,
    val conversionRate: Double
)

data class CohortDataPoint(
    val cohort: String,
    val period: Int,
    val retentionRate: Double
)

data class PredictionDataPoint(
    val date: String,
    val value: Double,
    val isPrediction: Boolean
)
