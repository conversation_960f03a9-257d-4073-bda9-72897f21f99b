<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Logo区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:src="@mipmap/ic_launcher" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="洗护商家"
                android:textColor="@color/colorPrimary"
                android:textSize="28sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="专业的洗护服务管理平台"
                android:textColor="@color/gray_600"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 登录表单 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 手机号输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="手机号"
                app:startIconDrawable="@drawable/ic_phone">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone"
                    android:maxLength="11" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 密码输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:hint="密码"
                app:endIconMode="password_toggle"
                app:startIconDrawable="@drawable/ic_lock">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 忘记密码 -->
            <TextView
                android:id="@+id/textViewForgotPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginBottom="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="8dp"
                android:text="忘记密码？"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp" />

            <!-- 用户协议 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/checkBoxAgreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="我已阅读并同意"
                    android:textColor="@color/gray_600"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/textViewUserAgreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:text="《用户协议》"
                    android:textColor="@color/colorPrimary"
                    android:textSize="12sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="和"
                    android:textColor="@color/gray_600"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/textViewPrivacyPolicy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    android:text="《隐私政策》"
                    android:textColor="@color/colorPrimary"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- 登录按钮 -->
            <Button
                android:id="@+id/buttonLogin"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="16dp"
                android:enabled="false"
                android:text="登录"
                android:textSize="16sp" />

            <!-- 快速登录 -->
            <TextView
                android:id="@+id/textViewQuickLogin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="12dp"
                android:text="验证码快速登录"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp" />

            <!-- 注册提示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="还没有账号？"
                    android:textColor="@color/gray_600"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/textViewRegister"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="8dp"
                    android:text="立即注册"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <!-- 加载进度 -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:visibility="gone" />

    </LinearLayout>

</ScrollView>
