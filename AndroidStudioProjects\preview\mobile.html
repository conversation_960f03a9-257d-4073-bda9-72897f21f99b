<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗护帮 - 移动端预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .phone-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: white;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .app-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .search-section {
            padding: 20px;
            background: white;
        }
        
        .search-bar {
            background: #f0f0f0;
            border-radius: 25px;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .search-input {
            border: none;
            background: none;
            flex: 1;
            font-size: 16px;
            outline: none;
        }
        
        .filter-tabs {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 5px;
        }
        
        .filter-tab {
            background: #f0f0f0;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-tab.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .service-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .promoted-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .ranking-number {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 12px;
            font-size: 14px;
        }
        
        .service-info {
            flex: 1;
        }
        
        .service-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 4px;
        }
        
        .service-details {
            font-size: 12px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .rating {
            color: #ff9500;
        }
        
        .price {
            color: #667eea;
            font-weight: bold;
        }
        
        .merchant-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .merchant-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .merchant-name {
            font-weight: bold;
            font-size: 16px;
        }
        
        .merchant-ranking {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stat-item {
            text-align: center;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
        }
        
        .stat-value {
            font-weight: bold;
            color: #667eea;
            font-size: 16px;
        }
        
        .stat-label {
            font-size: 10px;
            color: #666;
            margin-top: 2px;
        }
        
        .promotion-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .promotion-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .promotion-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .control-input {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 4px 8px;
            width: 60px;
            font-size: 12px;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 80px;
            background: white;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-radius: 0 0 32px 32px;
        }
        
        .nav-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .admin-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .admin-header {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .admin-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .admin-metric {
            background: white;
            padding: 8px;
            border-radius: 6px;
            text-align: center;
        }
        
        .metric-number {
            font-weight: bold;
            color: #667eea;
            font-size: 14px;
        }
        
        .metric-text {
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🧺 洗护帮</span>
                    <span>100%</span>
                </div>
                
                <div class="app-header">
                    <div class="app-title">洗护帮</div>
                    <div class="app-subtitle">智能投流竞价系统</div>
                </div>
                
                <div class="search-section">
                    <div class="search-bar">
                        <span>🔍</span>
                        <input type="text" class="search-input" placeholder="搜索洗护服务..." value="衣物洗护">
                    </div>
                    <div class="filter-tabs">
                        <button class="filter-tab active">全部</button>
                        <button class="filter-tab">推广</button>
                        <button class="filter-tab">附近</button>
                        <button class="filter-tab">高评分</button>
                        <button class="filter-tab">优惠</button>
                    </div>
                </div>
                
                <div class="content-area">
                    <!-- 用户端内容 -->
                    <div id="userContent" class="tab-content active">
                        <div class="section-title">
                            🔥 推荐服务
                        </div>
                        
                        <div class="service-card">
                            <div class="promoted-badge">推广</div>
                            <div class="service-header">
                                <div class="ranking-number">1</div>
                                <div class="service-info">
                                    <div class="service-name">优质洗衣店</div>
                                    <div class="service-details">
                                        <span class="rating">⭐ 4.9</span>
                                        <span>距离500m</span>
                                        <span class="price">¥15起</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="service-card">
                            <div class="promoted-badge">推广</div>
                            <div class="service-header">
                                <div class="ranking-number">2</div>
                                <div class="service-info">
                                    <div class="service-name">专业干洗店</div>
                                    <div class="service-details">
                                        <span class="rating">⭐ 4.8</span>
                                        <span>距离800m</span>
                                        <span class="price">¥20起</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="service-card">
                            <div class="service-header">
                                <div class="ranking-number">3</div>
                                <div class="service-info">
                                    <div class="service-name">快速洗护</div>
                                    <div class="service-details">
                                        <span class="rating">⭐ 4.7</span>
                                        <span>距离1.2km</span>
                                        <span class="price">¥12起</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商家端内容 -->
                    <div id="merchantContent" class="tab-content">
                        <div class="section-title">
                            💰 投流管理
                        </div>
                        
                        <div class="merchant-card">
                            <div class="merchant-header">
                                <div class="merchant-name">我的洗衣店</div>
                                <div class="merchant-ranking">排名 #3</div>
                            </div>
                            
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">¥1,280</div>
                                    <div class="stat-label">账户余额</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">156</div>
                                    <div class="stat-label">今日点击</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">3.2</div>
                                    <div class="stat-label">ROI</div>
                                </div>
                            </div>
                            
                            <div class="promotion-section">
                                <div class="promotion-title">🎯 推广设置</div>
                                <div class="promotion-controls">
                                    <span>关键词出价:</span>
                                    <input type="text" class="control-input" value="¥2.50">
                                </div>
                                <div class="promotion-controls">
                                    <span>日预算:</span>
                                    <input type="text" class="control-input" value="¥200">
                                </div>
                                <button class="btn">更新设置</button>
                            </div>
                        </div>
                        
                        <div class="merchant-card">
                            <div class="section-title">📊 效果分析</div>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-value">12.5%</div>
                                    <div class="stat-label">转化率</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">¥1.85</div>
                                    <div class="stat-label">平均CPC</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">2,340</div>
                                    <div class="stat-label">总点击</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 管理端内容 -->
                    <div id="adminContent" class="tab-content">
                        <div class="section-title">
                            ⚙️ 平台管理
                        </div>
                        
                        <div class="admin-panel">
                            <div class="admin-header">投流数据概览</div>
                            <div class="admin-metrics">
                                <div class="admin-metric">
                                    <div class="metric-number">¥125,680</div>
                                    <div class="metric-text">总投入</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">¥25,136</div>
                                    <div class="metric-text">平台收入</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">1,245</div>
                                    <div class="metric-text">活跃商家</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">¥1.85</div>
                                    <div class="metric-text">平均CPC</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="admin-panel">
                            <div class="admin-header">算法配置</div>
                            <div class="promotion-controls">
                                <span>投流权重:</span>
                                <input type="range" min="0" max="100" value="30" style="width: 80px;">
                                <span>30%</span>
                            </div>
                            <div class="promotion-controls">
                                <span>质量权重:</span>
                                <input type="range" min="0" max="100" value="40" style="width: 80px;">
                                <span>40%</span>
                            </div>
                            <div class="promotion-controls">
                                <span>相关性权重:</span>
                                <input type="range" min="0" max="100" value="30" style="width: 80px;">
                                <span>30%</span>
                            </div>
                        </div>
                        
                        <div class="admin-panel">
                            <div class="admin-header">违规监控</div>
                            <div class="admin-metrics">
                                <div class="admin-metric">
                                    <div class="metric-number">5,680</div>
                                    <div class="metric-text">今日检测</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">3</div>
                                    <div class="metric-text">发现违规</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">2</div>
                                    <div class="metric-text">自动处理</div>
                                </div>
                                <div class="admin-metric">
                                    <div class="metric-number">1</div>
                                    <div class="metric-text">人工审核</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="switchTab('user')">
                        <div class="nav-icon">📱</div>
                        <div class="nav-label">用户端</div>
                    </div>
                    <div class="nav-item" onclick="switchTab('merchant')">
                        <div class="nav-icon">🏪</div>
                        <div class="nav-label">商家端</div>
                    </div>
                    <div class="nav-item" onclick="switchTab('admin')">
                        <div class="nav-icon">⚙️</div>
                        <div class="nav-label">管理端</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchTab(tab) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有导航项的active状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示对应内容
            document.getElementById(tab + 'Content').classList.add('active');
            
            // 激活对应导航项
            event.target.closest('.nav-item').classList.add('active');
        }
        
        // 模拟实时数据更新
        function updateRealTimeData() {
            // 更新点击数
            const clickElements = document.querySelectorAll('.stat-value');
            clickElements.forEach(element => {
                if (element.textContent === '156') {
                    const newValue = 156 + Math.floor(Math.random() * 5);
                    element.textContent = newValue.toString();
                }
            });
            
            // 更新排名
            const rankingElements = document.querySelectorAll('.merchant-ranking');
            rankingElements.forEach(element => {
                if (Math.random() < 0.1) {
                    const currentRank = parseInt(element.textContent.match(/\d+/)[0]);
                    const change = Math.random() < 0.5 ? -1 : 1;
                    const newRank = Math.max(1, Math.min(10, currentRank + change));
                    element.textContent = `排名 #${newRank}`;
                }
            });
        }
        
        // 每3秒更新一次数据
        setInterval(updateRealTimeData, 3000);
        
        // 筛选标签切换
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
