package com.laundry.merchant.ui.product

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayoutMediator
import com.laundry.merchant.databinding.ActivityProductDetailBinding
import com.laundry.merchant.ui.product.adapter.ProductImageAdapter
import com.laundry.merchant.ui.product.adapter.ProductReviewAdapter
import com.laundry.merchant.ui.product.adapter.ProductSpecAdapter
import com.laundry.merchant.ui.cart.CartActivity
import com.laundry.merchant.utils.formatCurrency
import com.laundry.merchant.utils.showToast
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ProductDetailActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProductDetailBinding
    private val viewModel: ProductDetailViewModel by viewModels()
    
    private lateinit var imageAdapter: ProductImageAdapter
    private lateinit var specAdapter: ProductSpecAdapter
    private lateinit var reviewAdapter: ProductReviewAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val productId = intent.getStringExtra("product_id") ?: return

        setupViews()
        setupViewPager()
        setupRecyclerViews()
        observeViewModel()
        
        // 加载产品详情
        viewModel.loadProductDetail(productId)
    }

    private fun setupViews() {
        // 设置工具栏
        binding.imageViewBack.setOnClickListener {
            onBackPressed()
        }

        binding.imageViewShare.setOnClickListener {
            shareProduct()
        }

        binding.imageViewFavorite.setOnClickListener {
            viewModel.toggleFavorite()
        }

        binding.imageViewCart.setOnClickListener {
            startActivity(Intent(this, CartActivity::class.java))
        }

        // 设置底部按钮
        binding.buttonAddToCart.setOnClickListener {
            viewModel.addToCart()
        }

        binding.buttonBuyNow.setOnClickListener {
            viewModel.buyNow()
        }

        // 设置客服按钮
        binding.buttonCustomerService.setOnClickListener {
            contactCustomerService()
        }

        // 设置"问大家"按钮
        binding.buttonAskEveryone.setOnClickListener {
            // TODO: 跳转到问大家页面
            showToast("问大家功能开发中")
        }
    }

    private fun setupViewPager() {
        imageAdapter = ProductImageAdapter { imageUrl ->
            // TODO: 显示大图预览
        }
        binding.viewPagerImages.adapter = imageAdapter
        
        // 设置指示器
        TabLayoutMediator(binding.tabLayoutIndicator, binding.viewPagerImages) { _, _ ->
            // 空实现，只是为了显示指示器
        }.attach()
    }

    private fun setupRecyclerViews() {
        // 规格参数
        specAdapter = ProductSpecAdapter()
        binding.recyclerViewSpecs.apply {
            layoutManager = LinearLayoutManager(this@ProductDetailActivity)
            adapter = specAdapter
            isNestedScrollingEnabled = false
        }

        // 用户评价
        reviewAdapter = ProductReviewAdapter(
            onReviewClick = { review ->
                // TODO: 显示评价详情
            },
            onImageClick = { imageUrl ->
                // TODO: 显示评价图片
            }
        )
        binding.recyclerViewReviews.apply {
            layoutManager = LinearLayoutManager(this@ProductDetailActivity)
            adapter = reviewAdapter
            isNestedScrollingEnabled = false
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }

        lifecycleScope.launch {
            viewModel.events.collect { event ->
                handleEvent(event)
            }
        }
    }

    private fun updateUI(state: ProductDetailUiState) {
        // 更新加载状态
        binding.progressBar.visibility = if (state.isLoading) View.VISIBLE else View.GONE
        binding.scrollView.visibility = if (state.product != null) View.VISIBLE else View.GONE

        state.product?.let { product ->
            // 更新产品图片
            imageAdapter.updateData(product.imageUrls)

            // 更新产品信息
            binding.textViewProductName.text = product.name
            binding.textViewProductDescription.text = product.description
            
            // 更新价格
            binding.textViewCurrentPrice.text = product.currentPrice.formatCurrency()
            if (product.hasDiscount()) {
                binding.textViewOriginalPrice.visibility = View.VISIBLE
                binding.textViewOriginalPrice.text = product.originalPrice.formatCurrency()
                binding.textViewDiscount.visibility = View.VISIBLE
                binding.textViewDiscount.text = "${product.discountPercentage}%OFF"
            } else {
                binding.textViewOriginalPrice.visibility = View.GONE
                binding.textViewDiscount.visibility = View.GONE
            }

            // 更新库存状态
            updateStockStatus(product)

            // 更新评分和销量
            binding.ratingBar.rating = product.rating
            binding.textViewRating.text = product.rating.toString()
            binding.textViewReviewCount.text = "${product.reviewCount}条评价"
            binding.textViewSalesCount.text = "已售${product.salesCount}"

            // 更新规格参数
            specAdapter.updateData(product.specifications)

            // 更新收藏状态
            binding.imageViewFavorite.setImageResource(
                if (state.isFavorite) android.R.drawable.btn_star_big_on 
                else android.R.drawable.btn_star_big_off
            )

            // 更新购物车数量
            binding.textViewCartCount.text = state.cartCount.toString()
            binding.textViewCartCount.visibility = if (state.cartCount > 0) View.VISIBLE else View.GONE
        }

        // 更新评价列表
        reviewAdapter.updateData(state.reviews)
        binding.textViewReviewTitle.text = "用户评价 (${state.reviews.size})"

        // 显示错误
        if (state.error != null) {
            showError(state.error)
        }
    }

    private fun updateStockStatus(product: com.laundry.merchant.data.model.Product) {
        when (product.getStockStatus()) {
            com.laundry.merchant.data.model.StockStatus.OUT_OF_STOCK -> {
                binding.textViewStockStatus.text = "暂时缺货"
                binding.textViewStockStatus.setTextColor(getColor(android.R.color.holo_red_dark))
                binding.buttonAddToCart.isEnabled = false
                binding.buttonBuyNow.isEnabled = false
            }
            com.laundry.merchant.data.model.StockStatus.LOW_STOCK -> {
                binding.textViewStockStatus.text = "仅剩${product.stock}件"
                binding.textViewStockStatus.setTextColor(getColor(android.R.color.holo_orange_dark))
                binding.buttonAddToCart.isEnabled = true
                binding.buttonBuyNow.isEnabled = true
            }
            com.laundry.merchant.data.model.StockStatus.IN_STOCK -> {
                binding.textViewStockStatus.text = "现货充足"
                binding.textViewStockStatus.setTextColor(getColor(android.R.color.holo_green_dark))
                binding.buttonAddToCart.isEnabled = true
                binding.buttonBuyNow.isEnabled = true
            }
        }
    }

    private fun handleEvent(event: ProductDetailEvent) {
        when (event) {
            is ProductDetailEvent.ShowError -> {
                showError(event.message)
            }
            is ProductDetailEvent.ShowSuccess -> {
                showToast(event.message)
            }
            is ProductDetailEvent.NavigateToCart -> {
                startActivity(Intent(this, CartActivity::class.java))
            }
            is ProductDetailEvent.NavigateToCheckout -> {
                // TODO: 跳转到结算页面
                showToast("立即购买功能开发中")
            }
        }
    }

    private fun shareProduct() {
        val product = viewModel.uiState.value.product ?: return
        val shareText = "${product.name}\n${product.description}\n价格：${product.currentPrice.formatCurrency()}"
        
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, shareText)
        }
        startActivity(Intent.createChooser(shareIntent, "分享商品"))
    }

    private fun contactCustomerService() {
        // TODO: 联系客服功能
        showToast("客服功能开发中")
    }

    private fun showError(message: String) {
        showToast(message)
    }
}
